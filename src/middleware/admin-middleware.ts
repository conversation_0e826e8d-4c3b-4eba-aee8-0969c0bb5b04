import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";

// Admin-specific public routes
const ADMIN_PUBLIC_ROUTES = [
  "/",
  "/register",
  "/forgot-password",
  "/login",
  "/adminRegister",
  "/change-password",
];

// Check if admin access token is expired
function isAdminTokenExpired(accessToken: string): boolean {
  try {
    if (!accessToken || accessToken.trim() === "") {
      return true;
    }

    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === "string" || !decoded.payload) {
      return true;
    }

    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error("Admin token validation error:", error);
    return true;
  }
}

function isAdminAuthenticated(request: NextRequest): {
  isAuth: boolean;
  needsRefresh: boolean;
} {
  // Get Cognito tokens from cookies (admin uses cognito)
  const accessToken = request.cookies.get("cognito_access_token")?.value;
  const idToken = request.cookies.get("cognito_id_token")?.value;
  const refreshToken = request.cookies.get("cognito_refresh_token")?.value;

  // If no access token, admin is not authenticated
  if (!accessToken || !idToken) {
    return { isAuth: false, needsRefresh: false };
  }

  // Check if access token is expired
  const isExpired = isAdminTokenExpired(accessToken);

  if (isExpired) {
    // If expired but has refresh token, needs refresh
    if (refreshToken) {
      return { isAuth: false, needsRefresh: true };
    }
    // If expired and no refresh token, not authenticated
    return { isAuth: false, needsRefresh: false };
  }

  // Token is valid
  return { isAuth: true, needsRefresh: false };
}

function getCleanPath(pathname: string): string {
  return pathname.replace(/\/+$/, "") || "/";
}

export function adminMiddleware(request: NextRequest) {
  const cleanPath = getCleanPath(request.nextUrl.pathname);

  // Only handle admin routes (exclude member routes)
  const isMemberRoute =
    cleanPath.startsWith("/member-user") ||
    cleanPath.startsWith("/login") ||
    cleanPath.startsWith("/member-register") ||
    cleanPath.startsWith("/member-forgot-password");

  if (isMemberRoute) {
    return null; // Let member middleware handle this
  }

  console.log("Admin Middleware - Path:", cleanPath);

  const authStatus = isAdminAuthenticated(request);
  const isPublicRoute = ADMIN_PUBLIC_ROUTES.includes(cleanPath);

  console.log("Admin Middleware - Auth status:", authStatus);

  const hasRefreshCookie = !!request.cookies.get("cognito_refresh_token")
    ?.value;

  // If admin needs token refresh, redirect to refresh page immediately
  if (authStatus.needsRefresh && !isPublicRoute) {
    console.log(
      "Admin Middleware - Token needs refresh, redirecting to /auth/refresh"
    );
    const refreshUrl = new URL("/auth/refresh", request.url);
    refreshUrl.searchParams.set("returnUrl", cleanPath);
    return NextResponse.redirect(refreshUrl);
  }

  // If not authenticated but has refresh token, attempt refresh flow instead of sending to login
  if (!authStatus.isAuth && !isPublicRoute && hasRefreshCookie) {
    console.log(
      "Admin Middleware - Not authenticated but refresh token present, redirecting to /auth/refresh"
    );
    const refreshUrl = new URL("/auth/refresh", request.url);
    refreshUrl.searchParams.set("returnUrl", cleanPath);
    return NextResponse.redirect(refreshUrl);
  }

  // If not authenticated and trying to access admin protected route
  if (!authStatus.isAuth && !isPublicRoute) {
    const loginUrl = new URL("/", request.url);
    loginUrl.searchParams.set("redirect", cleanPath);
    console.log(
      "Admin Middleware - Redirecting unauthenticated admin to login"
    );
    return NextResponse.redirect(loginUrl);
  }

  // If authenticated and trying to access admin public route
  if (authStatus.isAuth && isPublicRoute) {
    const redirectTo = request.nextUrl.searchParams.get("redirect");
    const target =
      redirectTo &&
      redirectTo.startsWith("/") &&
      !redirectTo.startsWith("/member-")
        ? redirectTo
        : "/dashboard";
    console.log(
      "Admin Middleware - Redirecting authenticated admin from public route to:",
      target
    );
    return NextResponse.redirect(new URL(target, request.url));
  }

  console.log("Admin Middleware - Allowing access to:", cleanPath);
  return NextResponse.next();
}
