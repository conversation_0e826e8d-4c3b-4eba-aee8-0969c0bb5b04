import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { auth0 } from "../lib/auth0";

// Member-specific protected routes
const MEMBER_PROTECTED_PREFIX = "/member-user";

export async function memberMiddleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Only handle member protected routes
  const isMemberProtectedRoute = pathname.startsWith(MEMBER_PROTECTED_PREFIX);

  // If not a member route, let other middleware handle it
  if (!isMemberProtectedRoute) {
    return null;
  }

  console.log("Member Middleware - Processing member route:", pathname);

  // Use Auth0 to check authentication status
  const session = await auth0.getSession(request);
  console.log("Member Middleware - Auth0 session:", !!session);

  // If not authenticated and trying to access member protected route
  if (!session) {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("returnTo", pathname);
    console.log(
      "Member Middleware - Redirecting unauthenticated member to login page"
    );
    return NextResponse.redirect(loginUrl);
  }

  console.log("Member Middleware - Allowing access to:", pathname);
  return NextResponse.next();
}
