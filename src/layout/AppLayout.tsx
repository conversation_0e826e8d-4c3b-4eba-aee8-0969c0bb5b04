"use client";

import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { removeAuthToken } from "@/utils/auth";
import {
  AdminPanelSettings,
  Business,
  ChevronLeft,
  ChevronRight,
  Dashboard,
  Event,
  Logout,
  Menu as MenuIcon,
  People,
  Security
} from "@mui/icons-material";
import {
  AppBar,
  Avatar,
  Box,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  SxProps,
  Toolbar,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import ApiStatusIndicator from "./ApiStatusIndicator";

const drawerWidth = 240;
const miniDrawerWidth = 64;

interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path: string;
  role?: "super_admin" | "admin" | "moderator";
}

interface AppLayoutProps {
  children: React.ReactNode;
  sx?: SxProps;
}

const menuItems: MenuItem[] = [
  { text: "Dashboard", icon: <Dashboard />, path: "/dashboard" },
  { text: "Members", icon: <People />, path: "/members" },
  { text: "Audit Logs", icon: <Event />, path: "/logs" },
  { text: "Admin Users", icon: <AdminPanelSettings />, path: "/admin-users" },
  { text: "Organizations", icon: <Business />, path: "/organizations" },
];

function NavigationDrawer({
  filteredMenuItems,
  handleNavigation,
  handleLogout,
  open,
  isMobile,
  handleDrawerToggle,
}: {
  filteredMenuItems: MenuItem[];
  handleNavigation: (path: string) => void;
  handleLogout: () => void;
  open: boolean;
  isMobile: boolean;
  handleDrawerToggle: () => void;
}) {
  const theme = useTheme();

  return (
    <Box>
      {/* Header with logo and toggle */}
      <Box
        sx={{
          p: open ? 1 : 1,
          display: "flex",
          alignItems: "center",
          justifyContent: open ? "space-between" : "center",
          minHeight: 64,
          transition: theme.transitions.create(["padding"], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.shorter,
          }),
        }}
      >
        {open && (
          <Image
            src="/CO-gold.svg"
            alt="US Chamber CO Logo"
            width={120}
            height={35}
            style={{ objectFit: "contain" }}
          />
        )}
        {!isMobile && (
          <IconButton
            onClick={handleDrawerToggle}
            sx={{
              color: "#1e3a8a",
              ml: open ? 0 : "auto",
              mr: open ? 0 : "auto",
            }}
          >
            {open ? <ChevronLeft /> : <ChevronRight />}
          </IconButton>
        )}
      </Box>

      <Divider />

      {/* Navigation Menu */}
      <List sx={{ pt: 1 }}>
        {filteredMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding sx={{ display: "block" }}>
            <Tooltip title={!open ? item.text : ""} placement="right" arrow>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                sx={{
                  minHeight: 48,
                  justifyContent: open ? "initial" : "center",
                  px: 2.5,
                  mx: 1,
                  borderRadius: 1,
                  transition: theme.transitions.create(["background-color"], {
                    duration: theme.transitions.duration.shorter,
                  }),
                  "&:hover": {
                    backgroundColor: "rgba(30, 58, 138, 0.08)",
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 0,
                    mr: open ? 3 : "auto",
                    justifyContent: "center",
                    color: "#1e3a8a",
                    transition: theme.transitions.create(["margin"], {
                      duration: theme.transitions.duration.shorter,
                    }),
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  sx={{
                    opacity: open ? 1 : 0,
                    transition: theme.transitions.create(["opacity"], {
                      duration: theme.transitions.duration.shorter,
                    }),
                  }}
                />
              </ListItemButton>
            </Tooltip>
          </ListItem>
        ))}

        {/* Logout Button */}
        <ListItem disablePadding sx={{ display: "block", mt: 2 }}>
          <Tooltip title={!open ? "Logout" : ""} placement="right" arrow>
            <ListItemButton
              onClick={handleLogout}
              sx={{
                minHeight: 48,
                justifyContent: open ? "initial" : "center",
                px: 2.5,
                mx: 1,
                borderRadius: 1,
                transition: theme.transitions.create(["background-color"], {
                  duration: theme.transitions.duration.shorter,
                }),
                "&:hover": {
                  backgroundColor: "rgba(220, 38, 38, 0.08)",
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : "auto",
                  justifyContent: "center",
                  color: "#dc2626",
                  transition: theme.transitions.create(["margin"], {
                    duration: theme.transitions.duration.shorter,
                  }),
                }}
              >
                <Logout />
              </ListItemIcon>
              <ListItemText
                primary="Logout"
                sx={{
                  opacity: open ? 1 : 0,
                  color: "#dc2626",
                  transition: theme.transitions.create(["opacity"], {
                    duration: theme.transitions.duration.shorter,
                  }),
                }}
              />
            </ListItemButton>
          </Tooltip>
        </ListItem>
      </List>
    </Box>
  );
}

function AppToolbar({
  handleDrawerToggle,
  open,
  isMobile,
  onProfileClick,
  currentUser,
}: {
  handleDrawerToggle: () => void;
  open: boolean;
  isMobile: boolean;
  onProfileClick: () => void;
  currentUser: any;
}) {
  const currentDrawerWidth = open ? drawerWidth : miniDrawerWidth;

  const getInitials = () => {
    if (currentUser?.firstName && currentUser?.lastName) {
      return `${currentUser.firstName[0]}${currentUser.lastName[0]}`.toUpperCase();
    }
    return currentUser?.username?.substring(0, 2).toUpperCase() || "U";
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: {
          xs: "100%",
          md: `calc(100% - ${currentDrawerWidth}px)`,
        },
        ml: {
          xs: 0,
          md: `${currentDrawerWidth}px`,
        },
        backgroundColor: "#ffffff !important",
        color: "#1e3a8a !important",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        transition: (theme) =>
          theme.transitions.create(["width", "margin"], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.shorter,
          }),
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
      color="default"
    >
      <Toolbar sx={{ backgroundColor: "#ffffff", color: "#1e3a8a" }}>
        {isMobile && (
          <IconButton
            color="inherit"
            aria-label="toggle drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              color: "#1e3a8a",
            }}
          >
            <MenuIcon />
          </IconButton>
        )}
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            color: "#1e3a8a !important",
            fontWeight: 700,
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
            textShadow: "0 1px 2px rgba(0,0,0,0.1)",
            flexGrow: 1,
          }}
        >
          US Chamber CO - Member Dashboard
        </Typography>

        {/* Profile Icon */}
        <Tooltip title="User Profile">
          <IconButton
            onClick={onProfileClick}
            sx={{
              color: "#1e3a8a",
              "&:hover": {
                backgroundColor: "rgba(30, 58, 138, 0.08)",
              },
            }}
          >
            <Avatar
              sx={{
                width: 32,
                height: 32,
                bgcolor: "#1e3a8a",
                fontSize: "0.875rem",
              }}
            >
              {getInitials()}
            </Avatar>
          </IconButton>
        </Tooltip>
      </Toolbar>
    </AppBar>
  );
}

export default function AppLayout({ children, sx }: AppLayoutProps) {
  const [drawerOpen, setDrawerOpen] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const router = useRouter();

  const currentUserRole = useCurrentUserRole();
  const currentUser = useAppSelector((state) => state.auth.user);

  // Handle drawer toggle based on screen size
  const handleDrawerToggle = () => {
    if (isMobile) {
      setMobileOpen(!mobileOpen);
    } else {
      setDrawerOpen(!drawerOpen);
    }
  };

  // Close mobile drawer when navigating
  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleLogout = () => {
    router.push("/");
    removeAuthToken();
  };

  const handleProfileClick = () => {
    router.push("/profile");
  };

  // Auto-collapse drawer on mobile
  useEffect(() => {
    if (isMobile) {
      setDrawerOpen(false);
    } else {
      setDrawerOpen(true);
    }
  }, [isMobile]);

  const allPermissions = useAppSelector(
    (state) => state.roles.allRolesPermissions?.roles || []
  );
  const permission = allPermissions.find(
    (permission) => currentUserRole === permission.roleSlug
  );
  const modulePermissions = permission?.modulePermissions;

  const filter = menuItems.filter((item) => {
    const permission = modulePermissions?.find(
      (permission) =>
        "/" + permission.moduleName.replace(" ", "-").toLowerCase() ===
        item.path
    );
    return permission?.view;
  });

  const filteredMenuItems =
    currentUserRole === "admin"
      ? [...filter, { text: "Roles", icon: <Security />, path: "/roles" }]
      : filter;

  const currentDrawerWidth = drawerOpen ? drawerWidth : miniDrawerWidth;

  return (
    <Box sx={{ display: "flex", ...sx }}>
      <AppToolbar
        handleDrawerToggle={handleDrawerToggle}
        open={drawerOpen}
        isMobile={isMobile}
        onProfileClick={handleProfileClick}
        currentUser={currentUser}
      />

      <Box
        component="nav"
        sx={{ width: { md: currentDrawerWidth }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: "block", md: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              backgroundColor: "#ffffff",
              borderRight: "1px solid rgba(0, 0, 0, 0.12)",
            },
          }}
        >
          <NavigationDrawer
            filteredMenuItems={filteredMenuItems}
            handleNavigation={handleNavigation}
            handleLogout={handleLogout}
            open={true}
            isMobile={true}
            handleDrawerToggle={handleDrawerToggle}
          />
        </Drawer>

        {/* Desktop drawer with mini variant */}
        <Drawer
          variant="permanent"
          open={drawerOpen}
          sx={{
            display: { xs: "none", md: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: currentDrawerWidth,
              backgroundColor: "#ffffff",
              borderRight: "1px solid rgba(0, 0, 0, 0.12)",
              transition: theme.transitions.create("width", {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.shorter,
              }),
              overflowX: "hidden",
            },
          }}
        >
          <NavigationDrawer
            filteredMenuItems={filteredMenuItems}
            handleNavigation={handleNavigation}
            handleLogout={handleLogout}
            open={drawerOpen}
            isMobile={false}
            handleDrawerToggle={handleDrawerToggle}
          />
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, md: 3 },
          width: {
            xs: "100%",
            md: `calc(100% - ${currentDrawerWidth}px)`,
          },
          mt: { xs: 7, md: 8 },
          minHeight: "100vh",
          backgroundColor: "#f8fafc",
          transition: theme.transitions.create(["width", "margin"], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.shorter,
          }),
        }}
      >
        {children}
      </Box>
      <ApiStatusIndicator />
    </Box>
  );
}
