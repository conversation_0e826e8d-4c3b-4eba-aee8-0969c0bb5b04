'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Chip, Button, Typography } from '@mui/material';
import { CheckCircle, Error, Refresh } from '@mui/icons-material';

const ApiStatusIndicator: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'checking' | 'available' | 'unavailable'>('checking');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [mounted, setMounted] = useState(false);


  const getStatusColor = () => {
    switch (apiStatus) {
      case 'available':
        return 'success';
      case 'unavailable':
        return 'error';
      case 'checking':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (apiStatus) {
      case 'available':
        return <CheckCircle fontSize="small" />;
      case 'unavailable':
        return <Error fontSize="small" />;
      case 'checking':
        return <Refresh fontSize="small" className="animate-spin" />;
      default:
        return undefined;
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'available':
        return 'API Available';
      case 'unavailable':
        return 'Using Mock Data';
      case 'checking':
        return 'Checking...';
      default:
        return 'Unknown';
    }
  };

  // Don't render anything until mounted to prevent hydration mismatch
  if (!mounted || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Box sx={{ position: 'fixed', top: 80, right: 16, zIndex: 1000 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Chip
          icon={getStatusIcon()}
          label={getStatusText()}
          color={getStatusColor()}
          size="small"
          variant="outlined"
        />
        <Button
          size="small"
          onClick={() => {}}
          disabled={apiStatus === 'checking'}
          sx={{ minWidth: 'auto', p: 0.5 }}
        >
          <Refresh fontSize="small" />
        </Button>
      </Box>
      {lastChecked && (
        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
          Last checked: {lastChecked.toLocaleTimeString()}
        </Typography>
      )}
    </Box>
  );
};

export default ApiStatusIndicator; 