// Generic API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    field?: string;
  };
  timestamp: string;
}

// Pagination Types
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface ApiPaginatedResponse<T> extends ApiResponse<PaginatedResponse<T>> {}

// Filter Types
export interface BaseFilters {
  search?: string;
  status?: string;
  [key: string]: any;
}

// Request Types
export interface CreateRequest<T> {
  data: T;
}

export interface UpdateRequest<T> {
  id: string;
  data: Partial<T>;
}

export interface DeleteRequest {
  id: string;
}

export interface BulkActionRequest {
  ids: string[];
  action: 'delete' | 'export' | 'activate' | 'deactivate';
  [key: string]: any;
}

// Response Types for Common Operations
export interface CreateResponse<T> extends ApiResponse<T> {}
export interface UpdateResponse<T> extends ApiResponse<T> {}
export interface DeleteResponse extends ApiResponse<{ deleted: boolean }> {}
export interface BulkActionResponse extends ApiResponse<{ 
  success: string[];
  failed: string[];
  message: string;
}> {}

// Error Types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ApiError {
  code: string;
  message: string;
  details?: ValidationError[];
  field?: string;
}

// HTTP Status Codes
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// API Endpoint Types
export interface ApiEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  requiresAuth?: boolean;
  description?: string;
}

// Rate Limiting Types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// File Upload Types
export interface FileUploadResponse extends ApiResponse<{
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}> {}

export interface FileUploadRequest {
  file: File;
  folder?: string;
  allowedTypes?: string[];
  maxSize?: number;
}

// WebSocket Types
export interface WebSocketMessage<T = any> {
  type: string;
  data: T;
  timestamp: string;
  id?: string;
}

export interface WebSocketError {
  type: 'error';
  error: {
    code: string;
    message: string;
  };
  timestamp: string;
}

// Cache Types
export interface CacheConfig {
  ttl: number; // Time to live in seconds
  key: string;
  tags?: string[];
}

// Retry Types
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  retryableStatusCodes: number[];
}

// Monitoring Types
export interface ApiMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  timestamp: string;
  userAgent?: string;
  ip?: string;
}

 