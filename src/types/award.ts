// Award Types
export interface Award {
  id: number;
  memberId: number;
  organizationId: number;
  awardListingElementId: number;
  awardname: string;
  awarddescription?: string;
  awarddate: string;
  createdBy: string;
  updatedBy?: string;
  dateCreated: string;
  dateUpdated?: string;
}

export interface CreateAwardData {
  memberId: number;
  organizationId: number;
  awardListingElementId: number;
  awardname: string;
  awarddescription?: string;
  awarddate: string;
  createdBy: string;
}

export interface UpdateAwardData {
  awardname?: string;
  awarddescription?: string;
  awarddate?: string;
  updatedBy: string;
}

export interface AwardSearchParams {
  perPage?: number;
  pageSize?: number;
  query?: string;
}

export interface AwardResponse {
  status_code: number;
  success: boolean;
  message: string;
  award?: Award;
  awards?: Award[];
  total_count?: number;
  page?: number;
  page_size?: number;
}

// Award Listing Types
export interface AwardListing {
  id: number;
  name: string;
  description?: string;
  category?: string;
  eligibility?: string;
  criteria?: string;
  deadline?: string;
  createdBy: string;
  updatedBy?: string;
  dateCreated: string;
  dateUpdated?: string;
}

export interface CreateAwardListingData {
  name: string;
  description?: string;
  category?: string;
  eligibility?: string;
  criteria?: string;
  deadline?: string;
  createdBy: string;
}

export interface UpdateAwardListingData {
  name?: string;
  description?: string;
  category?: string;
  eligibility?: string;
  criteria?: string;
  deadline?: string;
  updatedBy: string;
}

export interface AwardListingResponse {
  status_code: number;
  success: boolean;
  message: string;
  award_listing?: AwardListing;
  award_listings?: AwardListing[];
  total_count?: number;
  page?: number;
  page_size?: number;
}
