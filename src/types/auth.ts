export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  roles: UserRole[];
  permissions: Permission[];
  mfaEnabled: boolean;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

export enum UserRole {
  ADMIN = "admin",
  MEMBER = "member",
  GUEST = "guest",
}

export enum Permission {
  READ_DASHBOARD = "read:dashboard",
  WRITE_DASHBOARD = "write:dashboard",
  MANAGE_USERS = "manage:users",
  VIEW_REPORTS = "view:reports",
  MANAGE_SETTINGS = "manage:settings",
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  requiresMFA: boolean;
  mfaChallengeType?: "SMS" | "TOTP";
  tempRegistration: null | {
    username: string;
    password: string;
    email?: string;
    isAwaitingTOTP?: boolean;
  };
}

export interface LoginFormData {
  username: string;
  password: string;
}

export interface RegisterFormData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName?: string;
  lastName?: string;
}

export interface MFAFormData {
  code: string;
  rememberDevice?: boolean;
}

export interface ForgotPasswordFormData {
  email: string;
}

export interface ResetPasswordFormData {
  email: string;
  code: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AuthTokens {
  accessToken: string;
  idToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface CognitoConfig {
  userPoolId: string;
  userPoolWebClientId: string;
  region: string;
  oauth?: {
    domain: string;
    scope: string[];
    redirectSignIn: string;
    redirectSignOut: string;
    responseType: "code" | "token";
  };
}

export interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordWithTokenFormData {
  accessToken: string;
  newPassword: string;
  confirmPassword: string;
}

export interface MFASetupFormData {
  code: string;
}

export interface TOTPSetupResponse {
  sharedSecret: string;
  qrCodeUrl: string;
  username: string;
}
