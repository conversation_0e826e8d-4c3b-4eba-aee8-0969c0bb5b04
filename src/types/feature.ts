// Feature Flag Types
export interface FeatureFlag {
  id: number;
  memberId: number;
  featureHandle: string;
  enabled: boolean;
  createdBy: string;
  updatedBy?: string;
  dateCreated: string;
  dateUpdated?: string;
}

export interface CreateFeatureFlagData {
  memberId: number;
  featureHandle: string;
  enabled: boolean;
  createdBy: string;
}

export interface UpdateFeatureFlagData {
  featureHandle?: string;
  enabled?: boolean;
  updatedBy: string;
}

export interface FeatureFlagSearchParams {
  perPage?: number;
  pageSize?: number;
  query?: string;
}

export interface FeatureFlagResponse {
  status_code: number;
  success: boolean;
  message: string;
  feature_flag?: FeatureFlag;
  feature_flags?: FeatureFlag[];
  total_count?: number;
  page?: number;
  page_size?: number;
}

// Bookmark Types
export interface Bookmark {
  id: number;
  memberId: number;
  bookmarkname: string;
  bookmarkurl: string;
  bookmarkdescription?: string;
  createdBy: string;
  updatedBy?: string;
  dateCreated: string;
  dateUpdated?: string;
}

export interface CreateBookmarkData {
  memberId: number;
  bookmarkname: string;
  bookmarkurl: string;
  bookmarkdescription?: string;
  createdBy: string;
}

export interface UpdateBookmarkData {
  bookmarkname?: string;
  bookmarkurl?: string;
  bookmarkdescription?: string;
  updatedBy: string;
}

export interface BookmarkSearchParams {
  perPage?: number;
  pageSize?: number;
  query?: string;
}

export interface BookmarkResponse {
  status_code: number;
  success: boolean;
  message: string;
  bookmark?: Bookmark;
  bookmarks?: Bookmark[];
  total_count?: number;
  page?: number;
  page_size?: number;
}
