export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  resource: string;
  action: string;
}

export interface Role {
  name: string;
  uuid: string;
  slug: string;
  description: string;
  createdBy: {
    uuid: string;
    username: string;
  };
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface CreateRoleData {
  role: {
    name: string;
    description: string;
  };
  permissions: {
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  };
}

export interface UpdateRoleData {
  role?: {
    name?: string;
    description?: string;
  };
  permissions?: {
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  };
}

// System permissions that can be assigned to roles
export const SYSTEM_PERMISSIONS: Permission[] = [
  // Member Management
  {
    id: "members.view",
    name: "View Members",
    description: "View member information",
    category: "Members",
    resource: "members",
    action: "view",
  },
  {
    id: "members.create",
    name: "Create Members",
    description: "Create new members",
    category: "Members",
    resource: "members",
    action: "create",
  },
  {
    id: "members.edit",
    name: "Edit Members",
    description: "Edit member information",
    category: "Members",
    resource: "members",
    action: "edit",
  },
  {
    id: "members.delete",
    name: "Delete Members",
    description: "Delete members",
    category: "Members",
    resource: "members",
    action: "delete",
  },
  {
    id: "members.export",
    name: "Export Members",
    description: "Export member data",
    category: "Members",
    resource: "members",
    action: "export",
  },

  // Admin User Management
  {
    id: "admin_users.view",
    name: "View Admin Users",
    description: "View admin user information",
    category: "Admin Users",
    resource: "admin_users",
    action: "view",
  },
  {
    id: "admin_users.create",
    name: "Create Admin Users",
    description: "Create new admin users",
    category: "Admin Users",
    resource: "admin_users",
    action: "create",
  },
  {
    id: "admin_users.edit",
    name: "Edit Admin Users",
    description: "Edit admin user information",
    category: "Admin Users",
    resource: "admin_users",
    action: "edit",
  },
  {
    id: "admin_users.delete",
    name: "Delete Admin Users",
    description: "Delete admin users",
    category: "Admin Users",
    resource: "admin_users",
    action: "delete",
  },

  // Role Management
  {
    id: "roles.view",
    name: "View Roles",
    description: "View role information",
    category: "Roles",
    resource: "roles",
    action: "view",
  },
  {
    id: "roles.create",
    name: "Create Roles",
    description: "Create new roles",
    category: "Roles",
    resource: "roles",
    action: "create",
  },
  {
    id: "roles.edit",
    name: "Edit Roles",
    description: "Edit role information",
    category: "Roles",
    resource: "roles",
    action: "edit",
  },
  {
    id: "roles.delete",
    name: "Delete Roles",
    description: "Delete roles",
    category: "Roles",
    resource: "roles",
    action: "delete",
  },
  {
    id: "roles.assign",
    name: "Assign Roles",
    description: "Assign roles to users",
    category: "Roles",
    resource: "roles",
    action: "assign",
  },

  // Analytics
  {
    id: "analytics.view",
    name: "View Analytics",
    description: "View analytics and reports",
    category: "Analytics",
    resource: "analytics",
    action: "view",
  },
  {
    id: "analytics.export",
    name: "Export Analytics",
    description: "Export analytics data",
    category: "Analytics",
    resource: "analytics",
    action: "export",
  },

  // Dashboard
  {
    id: "dashboard.view",
    name: "View Dashboard",
    description: "View dashboard overview",
    category: "Dashboard",
    resource: "dashboard",
    action: "view",
  },

  // System Settings
  {
    id: "settings.view",
    name: "View Settings",
    description: "View system settings",
    category: "Settings",
    resource: "settings",
    action: "view",
  },
  {
    id: "settings.edit",
    name: "Edit Settings",
    description: "Edit system settings",
    category: "Settings",
    resource: "settings",
    action: "edit",
  },

  // User Management
  {
    id: "users.view",
    name: "View Users",
    description: "View user information",
    category: "Users",
    resource: "users",
    action: "view",
  },
  {
    id: "users.edit",
    name: "Edit Users",
    description: "Edit user information",
    category: "Users",
    resource: "users",
    action: "edit",
  },
  {
    id: "users.delete",
    name: "Delete Users",
    description: "Delete users",
    category: "Users",
    resource: "users",
    action: "delete",
  },
];

// Permission categories for grouping
export const PERMISSION_CATEGORIES = [
  "Members",
  "Admin Users",
  "Roles",
  "Analytics",
  "Dashboard",
  "Settings",
  "Users",
];

// Default system roles
export const DEFAULT_ROLES: Pick<Role, "name" | "description">[] = [
  {
    name: "Super Admin",
    description: "Full system access with all permissions",
  },
  {
    name: "Admin",
    description: "Administrative access with most permissions",
  },
  {
    name: "Moderator",
    description: "Limited administrative access",
  },
  {
    name: "Member",
    description: "Standard member access",
  },
];

// API Response types for allRolesPermissions
export interface ModulePermission {
  moduleName: string;
  moduleSlug: string;
  create: boolean;
  update: boolean;
  delete: boolean;
  view: boolean;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface RoleWithPermissions {
  roleName: string;
  roleSlug: string;
  roleDescription: string;
  modulePermissions: ModulePermission[];
}

export interface AllRolesPermissionsResponse {
  roles: RoleWithPermissions[];
  total_roles: number;
}
