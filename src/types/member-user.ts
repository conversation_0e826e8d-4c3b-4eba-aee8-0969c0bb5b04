export interface MemberUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  organizationId?: string;
  membershipType: MembershipType;
  status: MemberStatus;
  permissions: MemberPermission[];
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
  phoneNumber?: string;
  address?: MemberAddress;
  preferences?: MemberPreferences;
}

export enum MembershipType {
  BASIC = "BASIC",
  PREMIUM = "PREMIUM",
  ENTERPRISE = "ENTERPRISE",
  TRIAL = "TRIAL",
}

export enum MemberStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED",
  PENDING = "PENDING",
  EXPIRED = "EXPIRED",
}

export enum MemberPermission {
  VIEW_DASHBOARD = "VIEW_DASHBOARD",
  MANAGE_PROFILE = "MANAGE_PROFILE",
  VIEW_ORGANIZATION = "VIEW_ORGANIZATION",
  MANAGE_SETTINGS = "<PERSON>NAGE_SETTINGS",
  VIEW_REPORTS = "VIEW_REPORTS",
  EXPORT_DATA = "EXPORT_DATA",
  INVITE_MEMBERS = "INVITE_MEMBERS",
  MANAGE_BILLING = "MANAGE_BILLING",
}

export interface MemberAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface MemberPreferences {
  notifications: NotificationSettings;
  theme: "light" | "dark" | "auto";
  language: string;
  timezone: string;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
  security: boolean;
}

export interface MemberLoginResponse {
  user: MemberUser;
  token: string;
  refreshToken: string;
  expiresIn: number;
  message?: string;
}

export interface MemberSocialLoginResponse {
  success: boolean;
  authUrl: string;
  provider: string;
  message?: string;
}

export interface MemberAuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
  statusCode?: number;
}

export interface MemberRegistrationRequest {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  phoneNumber?: string;
  organizationName?: string;
  membershipType?: MembershipType;
}

export interface MemberRegistrationResponse {
  user: MemberUser;
  token: string;
  refreshToken: string;
  message: string;
}

export interface MemberProfileUpdateRequest {
  name?: string;
  phoneNumber?: string;
  avatar?: string;
  address?: MemberAddress;
  preferences?: MemberPreferences;
}

export interface MemberPasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface MemberDashboardStats {
  totalLogins: number;
  lastLoginDate: string;
  accountAge: number;
  membershipExpiryDate?: string;
  storageUsed: number;
  storageLimit: number;
}

export interface MemberOrganization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  memberCount: number;
  createdAt: string;
  industry?: string;
  website?: string;
}

export interface MemberBillingInfo {
  planName: string;
  planPrice: number;
  billingCycle: "monthly" | "yearly";
  nextBillingDate: string;
  paymentMethod: string;
  invoices: MemberInvoice[];
}

export interface MemberInvoice {
  id: string;
  amount: number;
  currency: string;
  status: "paid" | "pending" | "failed";
  createdAt: string;
  downloadUrl?: string;
}

// API Response types
export interface MemberApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: MemberAuthError;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form validation types
export interface MemberLoginForm {
  email: string;
  password: string;
}

export interface MemberRegistrationForm {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  phoneNumber?: string;
  organizationName?: string;
  agreeToTerms: boolean;
}

export interface MemberForgotPasswordForm {
  email: string;
}

export interface MemberResetPasswordForm {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface MemberCodeValidationResponse {
  success: boolean;
  token: string;
  refreshToken: string;
  user: MemberUser;
  message?: string;
}
