interface ModulePermission {
  create: boolean;
  update: boolean;
  delete: boolean;
  view: boolean;
}

export interface Permission {
  [moduleName: string]: ModulePermission;
}

export interface Admin {
  uuid: string;
  username: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  countrycode: string | null;
  isactive: boolean;
  istemppassword: boolean;
  emailVerified: boolean;
  roles: string[] | string; // Support both array and string formats
  createdBy: string;
  permissions: Permission[];
  // Additional fields from API response
  cognitoid?: string | null;
  updatedBy?: string | null;
  lastlogin?: string | null;
  dateCreated?: string | null;
  dateUpdated?: string | null;
}

export interface AdminUserResponse {
  success: boolean;
  message: string;
  user: Admin;
  status_code: 200;
}

export interface AdminUserFilters {
  search?: string;
  status?: "all" | "active" | "inactive" | "pending";
  role?: "all" | "super_admin" | "admin" | "moderator";
  department?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface AdminUserSearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters: AdminUserFilters;
}

export interface AdminUserListResponse {
  adminUsers: Admin[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  pagination: {
    totalCount: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

export interface CreateAdminUserRequest {
  username: string;
  email: string;
  roles: ("super_admin" | "admin" | "moderator")[];
  phone?: string;
  department?: string;
  notes?: string;
  generatePassword?: boolean;
  sendInvitation?: boolean;
  firstName?: string;
  lastName?: string;
}

export interface UpdateAdminUserRequest {
  password?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isactive?: boolean;
  istemppassword?: boolean;
  roles?: string[];
}

export interface BulkActionRequest {
  adminuserIds: string[];
  action: "delete" | "activate" | "deactivate" | "change_role";
  role?: "super_admin" | "admin" | "moderator";
}

export interface AdminUserStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  superAdmins: number;
  admins: number;
  moderators: number;
  newThisMonth: number;
}

// Column definitions for the table
export interface AdminUserTableColumn {
  id: keyof Admin;
  label: string;
  sortable: boolean;
  width?: string;
  align?: "left" | "center" | "right";
}

export const ADMIN_USER_TABLE_COLUMNS: AdminUserTableColumn[] = [
  { id: "firstName", label: "First Name", sortable: true, width: "150px" },
  { id: "lastName", label: "Last Name", sortable: true, width: "150px" },
  { id: "email", label: "email", sortable: true, width: "250px" },
  {
    id: "roles",
    label: "Roles",
    sortable: true,
    width: "120px",
    align: "center",
  },
];

// Permission definitions
export const ADMIN_PERMISSIONS = {
  SUPER_ADMIN: [
    "manage_admin_users",
    "manage_members",
    "manage_analytics",
    "manage_settings",
    "view_reports",
    "manage_roles",
    "system_access",
  ],
  ADMIN: [
    "manage_members",
    "manage_analytics",
    "view_reports",
    "limited_settings",
  ],
  MODERATOR: ["view_members", "view_analytics", "basic_reports"],
} as const;

export type AdminRole = keyof typeof ADMIN_PERMISSIONS;

export function getRolePermissions(role: AdminRole): readonly string[] {
  return ADMIN_PERMISSIONS[role] || [];
}

// Updated AdminUser interface to match API response structure
export interface UpdatedAdminUser {
  uuid: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  phone: string;
  countrycode: string;
  isactive: boolean;
  istemppassword: boolean;
  emailVerified: boolean;
  roles: string[];
  cognitoid: string;
  createdBy: string;
  updatedBy: string;
  lastlogin: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface UpdateAdminUserResponse {
  success?: boolean;
  data?: UpdatedAdminUser;
  error?: string;
  message?: string;
  // Direct API response (when success is not wrapped)
  uuid?: string;
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isactive?: boolean;
  istemppassword?: boolean;
  emailVerified?: boolean;
  roles?: string[];
  cognitoid?: string;
  createdBy?: string;
  updatedBy?: string;
  lastlogin?: string;
  dateCreated?: string;
  dateUpdated?: string;
}

export interface CreateAdminUserResponse {
  message: string;
  status_code: number;
  success: boolean;
}
