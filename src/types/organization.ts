// Organization Types based on API Documentation

import { Pagination } from "@/services/members";

export interface Organization {
  uuid: string;
  name: string;
  email: string;
  phone: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  industry: string;
  annualRevenue?: string;
  companySize?: string;
  yearFounded?: string;
  businessProfileElementId?: number;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface OrganizationVerifiedData {
  uuid: string;
  organization_uuid: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  ein: string;
  industry: string;
  foundingyear: number;
  verificationStatus: string;
  verificationType: string;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface MemberRelation {
  uuid: string;
  member_uuid: string;
  organization_uuid: string;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface MemberAward {
  uuid: string;
  memberId: string;
  organizationId: string;
  awardListingElementId: number;
  status: string;
  progress: number;
  categories: Record<string, any>;
  isDisQualified: boolean;
  isPreviousWinner: boolean;
  isQualified: boolean;
  isJudged: boolean;
  isPaid: boolean;
  isWinner: boolean;
  winnerTypes: string;
  applicationLink?: string;
  startedDate?: string;
  submittedDate?: string;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

// Member Award Data Types
export interface CreateMemberAwardData {
  memberId: string;
  organizationId: number;
  awardListingElementId: number;
  status: string;
  progress: number;
  categories: Record<string, any>;
  isDisQualified: boolean;
  isPreviousWinner: boolean;
  isQualified: boolean;
  isJudged: boolean;
  isPaid: boolean;
  isWinner: boolean;
  winnerTypes?: string;
  applicationLink?: string;
  startedDate?: string;
  submittedDate?: string;
}

export interface UpdateMemberAwardData extends Partial<CreateMemberAwardData> {
  updatedBy: string;
}

// Form Data Types
export interface CreateOrganizationData {
  name: string;
  email: string;
  phone: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  industry: string;
  annualRevenue?: string;
  companySize?: string;
  yearFounded?: string;
  businessProfileElementId?: number;
}

export interface UpdateOrganizationData
  extends Partial<CreateOrganizationData> {}

// API Response Types
export interface ApiResponse<T> {
  status_code: number;
  success: boolean;
  message: string;
  data?: T;
}

// Direct API responses (not wrapped in data property)
export interface OrganizationApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  organization: Organization;
}

export interface OrganizationsApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  organizations: Organization[];
  pagination: Pagination;
}

export interface OrganizationWithVerifiedDataApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  organization: Organization;
  verified_data: OrganizationVerifiedData;
}

export interface OrganizationWithRelationsApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  organization: Organization;
  member_relations: MemberRelation[];
  awards: MemberAward[];
}

export interface MemberAwardsApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  awards: MemberAward[];
}

export interface MemberOrganizationsApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  organizations: Organization[];
}

// Additional response types for specific operations
export interface OrganizationVerifiedDataApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  verified_data: OrganizationVerifiedData;
}

export interface MemberRelationApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  relation: MemberRelation;
}

export interface MemberRelationsApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  relations: MemberRelation[];
}

export interface MemberAwardApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  award: MemberAward;
}

export interface OrganizationsResponse {
  organizations: Organization[];
  pagination: PaginationState;
}

export interface OrganizationResponse {
  organization: Organization;
}

export interface OrganizationWithVerifiedDataResponse {
  organization: Organization;
  verified_data: OrganizationVerifiedData;
}

export interface OrganizationWithRelationsResponse {
  organization: Organization;
  member_relations: MemberRelation[];
  awards: MemberAward[];
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

// Search Types
export interface SearchParams extends PaginationParams {
  query: string;
}

// Missing types that are imported in services
export interface MemberAwardResponse {
  award: MemberAward;
}

// Removed - replaced with enhanced OrganizationSearchParams above

export interface CreateOrganizationVerifiedData {
  organizationId: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  ein: string;
  industry: string;
  foundingyear: number;
  verificationStatus: string;
  verificationType: string;
}

export interface UpdateOrganizationVerifiedData
  extends Partial<CreateOrganizationVerifiedData> {}

export interface OrganizationVerifiedDataResponse {
  verified_data: OrganizationVerifiedData;
}

export interface MemberOrganization extends Organization {
  memberId: string;
}

export interface CreateMemberOrganizationData {
  memberId: string;
  organizationId: string;
}

export interface UpdateMemberOrganizationData
  extends Partial<CreateMemberOrganizationData> {}

export interface MemberOrganizationResponse {
  organization: MemberOrganization;
}

// Organization Member Interface
export interface OrganizationMember {
  uuid: string;
  name: string;
  username: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

// Enhanced Filter Parameters based on API documentation
export interface OrganizationFilterParams {
  // Pagination
  page?: number;
  pageSize?: number;

  // Text filters (partial, case-insensitive)
  name?: string;
  city?: string;
  state?: string;
  zip?: string;
  companySize?: string;
  industry?: string;
  yearFounded?: string;
  phone?: string;
  email?: string;

  // Date filters (format: YYYY-MM-DD)
  dateCreatedFrom?: string;
  dateCreatedTo?: string;

  // Range filters
  annualRevenueMin?: string;
  annualRevenueMax?: string;
  yearFoundedMin?: string;
  yearFoundedMax?: string;

  // Exact match filter
  memberCount?: number;

  // Sorting
  sortBy?:
    | "dateUpdated"
    | "name"
    | "dateCreated"
    | "city"
    | "state"
    | "industry"
    | "yearFounded"
    | "annualRevenue";
  sortOrder?: "asc" | "desc";
}

export interface OrganizationSearchParams extends OrganizationFilterParams {
  query?: string;
}
