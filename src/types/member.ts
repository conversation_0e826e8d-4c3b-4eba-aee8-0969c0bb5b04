// Main member interface
export interface Member {
  uuid: string;
  auth0Id: string;
  customerIoId?: string;
  openWaterId?: number;
  firstName?: string;
  lastName?: string;
  loginEmail?: string;
  loginEmailVerified: boolean;
  identityType?: "business" | "individual" | "organization";
  personalBusinessEmail?: string;
  phone?: string;
  professionalTitle?: string;
  membershipTier: "basic" | "premium" | "enterprise" | "vip";
  communityStatus: "unverified" | "verified" | "pending" | "rejected";
  verificationStatus?: "pending" | "verified" | "rejected" | "under_review";
  hasSeenhirstloginmessage: boolean;
  dateCreated: string;
  dateUpdated: string;
}

// Organization interface
export interface Organization {
  id: number;
  name?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  email?: string;
  annualRevenue?: string;
  industry?: string;
  yearFounded?: string;
  companySize?: string;
  businessProfileElementId?: number;
  dateCreated: string;
  dateUpdated: string;
}

// Verified data interfaces
export interface MemberVerifiedData {
  memberId: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  professionalTitle?: string;
  verificationStatus:
    | "requires_review"
    | "in_progress"
    | "completed"
    | "rejected";
  verificationType: "auto" | "manual";
  dateCreated: string;
  dateUpdated: string;
}

export interface OrganizationVerifiedData {
  organizationId: number;
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  ein?: string;
  industry?: string;
  foundingYear?: number;
  verificationStatus:
    | "requires_review"
    | "in_progress"
    | "completed"
    | "rejected";
  verificationType: "auto" | "manual";
  dateCreated: string;
  dateUpdated: string;
}

// Awards interface
export interface MemberAward {
  memberId: number;
  organizationId: number;
  awardListingElementId: number;
  status: string;
  progress?: number;
  categories?: any;
  isDisQualified: boolean;
  isPreviousWinner: boolean;
  isQualified: boolean;
  isJudged?: boolean;
  isPaid?: boolean;
  isWinner?: boolean;
  winnerTypes?: string;
  applicationLink?: string;
  startedDate?: string;
  submittedDate?: string;
  dateCreated: string;
  dateUpdated: string;
}

// Blacklist report interface
export interface MemberBlacklistReport {
  memberId: number;
  disposableemail: boolean;
  appears: boolean;
  frequency: number;
  submitted: string;
  updated: string;
  spamRate: number;
  exists?: boolean;
  inAntispamUpdated?: string;
  dateCreated: string;
  dateUpdated: string;
  isSpam: boolean;
}

// Feature flag interface
export interface MemberFeatureFlag {
  id: number;
  memberId: number;
  featureHandle: string;
  enabled: boolean;
}

// Member with all relations
export interface MemberWithRelations extends Member {
  organizations: Organization[];
  verifiedData?: MemberVerifiedData;
  awards: MemberAward[];
  blacklistReport?: MemberBlacklistReport;
  featureFlags: MemberFeatureFlag[];
}

// Member with organizations (for API responses)
export interface MemberWithOrganizations extends Member {
  organizations: OrganizationInfo[];
  verifiedData?: MemberVerifiedData;
  awards?: MemberAward[];
  blacklistReport?: MemberBlacklistReport;
  featureFlags?: MemberFeatureFlag[];
}

// Legacy filters interface (for backward compatibility)
export interface MemberFilters {
  search: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  membershipTier?: string;
  communityStatus?: string;
  organizationName?: string;
  organizationCity?: string;
  organizationState?: string;
  organizationZip?: string;
  companySize?: string;
  industry?: string;
  // Legacy fields for backward compatibility
  status?: "all" | "active" | "inactive" | "pending";
  role?: "all" | "admin" | "member" | "premium";
  membershipType?: "all" | "basic" | "premium" | "enterprise";
  identityType?: "all" | "business" | "individual" | "organization";
}

// Search parameters interface
export interface MemberSearchParams {
  page: number;
  pageSize: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
  filters: MemberFilters;
}

// Bulk action interface
export interface MemberBulkAction {
  memberIds: string[];
  action: "delete" | "export" | "update_status" | "update_tier" | "verify";
  data?: any;
}

// Form types for creating/editing members
export interface CreateMemberData {
  firstName: string;
  lastName: string;
  loginEmail: string;
  phone?: string;
  professionalTitle?: string;
  identityType: "business" | "individual" | "organization";
  membershipTier: "lite" | "premium";
  communityStatus: "unverified" | "verified" | "pending" | "rejected";
  verificationStatus?: "pending" | "verified" | "rejected" | "under_review";
  personalBusinessEmail?: string;
  password?: string;
  confirmPassword?: string;
  organization?: {
    name?: string;
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zip?: string;
    phone?: string;
    email?: string;
    annualRevenue?: string;
    industry?: string;
    yearFounded?: string;
    companySize?: string;
  };
  selectedOrganizations?: Array<{
    uuid: string;
    name: string;
    city?: string;
    state?: string;
    industry?: string;
  }>;
}

export interface UpdateMemberData extends Partial<CreateMemberData> {
  // No uuid field needed since it's passed in the URL
}

// Verification request interface
export interface VerificationRequest {
  memberId: string;
  verificationType: "auto" | "manual";
  data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    professionalTitle?: string;
  };
}

// API response interfaces
export interface MemberWithOrganizationsResponse {
  success: boolean;
  message: string;
  members: MemberWithOrganizations[];
  pagination: {
    totalCount: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Organization info interface
export interface OrganizationInfo {
  uuid: string;
  name?: string;
  phone?: string;
  email?: string;
  companySize?: string;
  city?: string;
  state?: string;
  zip?: string;
  industry?: string;
  dateCreated: string;
}

// Export interfaces
export interface MemberExportRequest {
  filters: {
    firstName?: string;
    lastName?: string;
    email?: string;
    organizationName?: string;
    membershipTier?: string;
    communityStatus?: string;
    verificationStatus?: string;
    dateCreated?: string;
    city?: string;
    state?: string;
    zip?: string;
    industry?: string;
    companySize?: string;
  };
  selectedFields: string[];
  notes: string;
}

export interface MemberExportResponse {
  success: boolean;
  message: string;
  downloadUrl?: string;
  filename?: string;
}

// Search filters interface
export interface MemberSearchFilters {
  search?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  membershipTier?: string;
  communityStatus?: string;
  verificationStatus?: string;
  organizationName?: string;
  organizationCity?: string;
  organizationState?: string;
  organizationZip?: string;
  companySize?: string;
  industry?: string;
  dateCreatedFrom?: string;
  dateCreatedTo?: string;
}
