// Log Table Component with MUI DataGrid
"use client";

import React, { useCallback } from "react";
import {
  Box,
  Typography,
  Paper,
  Chip,
  Tooltip,
  IconButton,
  Button,
} from "@mui/material";
import {
  DataGrid,
  GridColDef,
  GridPaginationModel,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import {
  Visibility as ViewIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import { LogEntry, PAGE_SIZE_OPTIONS } from "@/types/log";
import { logApiService } from "@/services/logApiService";
import { useRouter } from "next/navigation";
import Link from "../Link";

interface LogTableProps {
  logs: LogEntry[];
  loading: boolean;
  pagination: {
    page: number;
    page_size: number;
    total_count: number;
  };
  onPaginationChange: (page: number, pageSize: number) => void;
  activeTab?: "audit" | "member_delete";
}

const LogTable: React.FC<LogTableProps> = ({
  logs,
  loading,
  pagination,
  onPaginationChange,
  activeTab = "audit",
}) => {
  const router = useRouter();
  // Handle pagination change
  const handlePaginationModelChange = useCallback(
    (newModel: GridPaginationModel) => {
      onPaginationChange(newModel.page + 1, newModel.pageSize);
    },
    [onPaginationChange]
  );


  const formatTimestamp = (dateString: string) => {
    let normalizedDateString = dateString;

    // If it doesn't end with 'Z' or a timezone offset, assume it's UTC and append 'Z'
    if (!/[Z+-]/.test(dateString.slice(-1))) {
      // Remove fractional seconds if present
      normalizedDateString = dateString.split(".")[0] + "Z";
    }

    const utcDate = new Date(normalizedDateString);

    return utcDate.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true, // 12-hour format
    });
  };

  // Render action cell with action type styling
  const renderActionCell = (params: GridRenderCellParams) => {
    const action = params.value as string;
    let color:
      | "default"
      | "primary"
      | "secondary"
      | "error"
      | "info"
      | "success"
      | "warning" = "default";

    switch (action) {
      case "member_export":
        color = "primary";
        break;
      case "log_export":
        color = "info";
        break;
      case "user_login":
        color = "success";
        break;
      case "data_update":
        color = "warning";
        break;
      case "report_generation":
        color = "secondary";
        break;
      default:
        color = "default";
    }

    return (
      <Chip label={action} color={color} size="small" variant="outlined" />
    );
  };

  // Render username cell with icon
  const renderUserCell = (params: GridRenderCellParams) => {
    const username = params.value as string;
    const userUuid = params.row.userUuid;

    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <PersonIcon color="action" fontSize="small" />
        <Button
          // component={RouterLink}
          sx={{
            textDecoration: "none",
            color: "primary.main",

            cursor: "pointer",
            "&:hover": {
              textDecoration: "underline",
            },
          }}
        >
          {username || "Unknown User"}
        </Button>
      </Box>
    );
  };

  // Render filters applied cell
  const renderFiltersCell = (params: GridRenderCellParams) => {
    const filters = params.value;
    const filtersText = logApiService.parseFiltersApplied(filters);

    if (filtersText === "None") {
      return (
        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          None
        </Typography>
      );
    }

    return (
      <Tooltip title={filtersText} arrow>
        <Typography
          variant="body2"
          sx={{
            cursor: "help",
            display: 'flex',
            alignItems: 'center',
            height: '100%',
            maxWidth: "200px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {filtersText.length > 50
            ? `${filtersText.substring(0, 50)}...`
            : filtersText}
        </Typography>
      </Tooltip>
    );
  };

  // Render selected fields cell
  const renderSelectedFieldsCell = (params: GridRenderCellParams) => {
    const fields = params.value;
    const fieldsText = logApiService.parseSelectedFields(fields);

    if (fieldsText === "None") {
      return (
        <Typography variant="body2" color="text.secondary">
          None
        </Typography>
      );
    }

    return (
      <Tooltip title={fieldsText} arrow>
        <Typography
          variant="body2"
          sx={{
            cursor: "help",
            maxWidth: "200px",
            display: 'flex',
            alignItems: 'center',
            height: '100%',
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {fieldsText.length > 50
            ? `${fieldsText.substring(0, 50)}...`
            : fieldsText}
        </Typography>
      </Tooltip>
    );
  };

  // Render purpose cell with tooltip for long text
  const renderPurposeCell = (params: GridRenderCellParams) => {
    const purpose = params.value as string;

    if (!purpose) {
      return (
        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          No purpose
        </Typography>
      );
    }

    return (
      <Tooltip title={purpose} arrow>
        <Typography
          variant="body2"
          sx={{
            cursor: "help",
            maxWidth: "300px",
            display: 'flex',
            alignItems: 'center',
            height: '100%',
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {purpose.length > 60 ? `${purpose.substring(0, 60)}...` : purpose}
        </Typography>
      </Tooltip>
    );
  };

  // Render deleted email cell for member delete logs
  const renderDeletedEmailCell = (params: GridRenderCellParams) => {
    const deletedEmail = params.value as string;

    if (!deletedEmail) {
      return (
        <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          N/A
        </Typography>
      );
    }

    return (
      <Tooltip title={deletedEmail} arrow>
        <Typography
          variant="body2"
          sx={{
            cursor: "help",
            maxWidth: "200px",
            overflow: "hidden",
            display: 'flex',
            alignItems: 'center',
            height: '100%',
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            color: "error.main",
            fontWeight: 500,
          }}
        >
          {deletedEmail}
        </Typography>
      </Tooltip>
    );
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: "timestamp",
      headerName: "Timestamp",
      width: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ fontFamily: "monospace", fontSize: "0.75rem", display: 'flex', alignItems: 'center', height: '100%' }}
        >
          {formatTimestamp(params.value)}
        </Typography>
      ),
    },
    {
      field: "username",
      headerName: "User",
      width: 120,
      renderCell: renderUserCell,
    },
    {
      field: "action",
      headerName: "Action",
      width: 140,
      renderCell: renderActionCell,
    },
    // Conditionally show deleted_email field for member_delete tab
    ...(activeTab === "member_delete"
      ? [
          {
            field: "deleted_email",
            headerName: "Deleted Email",
            width: 200,
            renderCell: renderDeletedEmailCell,
          },
        ]
      : []),
    // Conditionally show filters_applied field for audit tab only
    ...(activeTab === "audit"
      ? [
          {
            field: "filters_applied",
            headerName: "Filters Applied",
            width: 200,
            renderCell: renderFiltersCell,
          },
        ]
      : []),
    // Conditionally show selected_fields field for audit tab only
    ...(activeTab === "audit"
      ? [
          {
            field: "selected_fields",
            headerName: "Selected Fields",
            width: 200,
            renderCell: renderSelectedFieldsCell,
          },
        ]
      : []),
    {
      field: "purpose",
      headerName: "Purpose",
      flex: 1,
      minWidth: 300,
      renderCell: renderPurposeCell,
    },
  ];

  return (
    <Box sx={{ height: "100%", width: "100%" }}>
      <Paper sx={{ height: 600, width: "100%" }}>
        <DataGrid
          rows={logs || []}
          columns={columns}
          pagination={true}
          paginationMode="server"
          rowCount={pagination.total_count}
          paginationModel={{
            page: (pagination.page || 1) - 1, // DataGrid uses 0-based page indexing
            pageSize: pagination.page_size || 50,
          }}
          disableColumnMenu
          disableColumnSorting
          disableColumnFilter
          onPaginationModelChange={handlePaginationModelChange}
          pageSizeOptions={PAGE_SIZE_OPTIONS.map((option) => option.value)}
          loading={loading}
          getRowId={(row) => row.uuid}
          sx={{
            "& .MuiDataGrid-cell": {
              borderBottom: "1px solid rgb(224, 224, 224)",
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#f5f5f5",
              borderBottom: "2px solid #e0e0e0",
              fontWeight: 600,
            },
            "& .MuiDataGrid-row:hover": {
              backgroundColor: "#f8f9fa",
            },
            "& .MuiTablePagination-toolbar": {
              minHeight: 56,
            },
            "& .MuiTablePagination-selectLabel": {
              fontSize: "0.875rem",
            },
            "& .MuiTablePagination-displayedRows": {
              fontSize: "0.875rem",
            },
          }}
          slotProps={{
            pagination: {
              labelRowsPerPage: "Records per page:",
              labelDisplayedRows: ({
                from,
                to,
                count,
              }: {
                from: number;
                to: number;
                count: number;
              }) =>
                `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`,
            },
          }}
        />
      </Paper>
    </Box>
  );
};

export default LogTable;
