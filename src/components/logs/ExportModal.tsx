// Export Modal Component for Log CSV Export
"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormGroup,
  FormControlLabel,
  Checkbox,
  TextField,
  Typography,
  Box,
  Alert,
  Divider,
  CircularProgress,
} from "@mui/material";
import {
  Download as DownloadIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import {
  ExportField,
  ExportRequest,
  LogFilterParams,
  DEFAULT_EXPORT_FIELDS,
} from "@/types/log";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  onExport: (exportRequest: ExportRequest) => void;
  exportFields: ExportField[];
  currentFilters: LogFilterParams;
  loading?: boolean;
  error?: string | null;
  activeTab?: "audit" | "member_delete";
}

const ExportModal: React.FC<ExportModalProps> = ({
  open,
  onClose,
  onExport,
  exportFields,
  currentFilters,
  loading = false,
  error = null,
  activeTab = "audit",
}) => {
  // Filter export fields based on active tab
  const filteredExportFields =
    activeTab === "member_delete"
      ? exportFields.filter(
          (field) =>
            !["filters_applied", "selected_fields"].includes(field.field)
        )
      : exportFields;

  const [selectedFields, setSelectedFields] = useState<string[]>(
    activeTab === "member_delete"
      ? DEFAULT_EXPORT_FIELDS.filter(
          (field) => !["filters_applied", "selected_fields"].includes(field)
        )
      : DEFAULT_EXPORT_FIELDS
  );
  const [notes, setNotes] = useState("");

  useEffect(() => {
    if (open) {
      // Reset form when modal opens
      setSelectedFields(
        activeTab === "member_delete"
          ? DEFAULT_EXPORT_FIELDS.filter(
              (field) => !["filters_applied", "selected_fields"].includes(field)
            )
          : DEFAULT_EXPORT_FIELDS
      );
      setNotes("");
    }
  }, [open, activeTab]);

  const handleFieldChange = (fieldName: string, checked: boolean) => {
    setSelectedFields((prev) => {
      if (checked) {
        return [...prev, fieldName];
      } else {
        return prev.filter((field) => field !== fieldName);
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedFields.length === filteredExportFields.length) {
      setSelectedFields([]);
    } else {
      setSelectedFields(filteredExportFields.map((field) => field.field));
    }
  };

  const handleExport = () => {
    if (selectedFields.length === 0) {
      return; // Don't export if no fields selected
    }

    const { page, page_size, ...filterParams } = currentFilters;

    const exportRequest: ExportRequest = {
      filters: filterParams,
      selected_fields: selectedFields,
      notes: notes.trim() || "Log export from admin panel",
    };

    onExport(exportRequest);
  };

  const isFormValid = selectedFields.length > 0;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: "500px" },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <DownloadIcon color="primary" />
          <Typography variant="h6">Export Logs to CSV</Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure your export settings and select the fields to include in the
          CSV file.
        </Typography>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Current Filters Summary */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Current Filters Applied
          </Typography>
          <Box
            sx={{
              p: 2,
              backgroundColor: "grey.50",
              borderRadius: 1,
              border: "1px solid",
              borderColor: "grey.200",
            }}
          >
            {Object.keys(currentFilters).length > 2 ? (
              <Box>
                {currentFilters.start_timestamp && (
                  <Typography variant="body2">
                    <strong>Start Date:</strong>{" "}
                    {new Date(currentFilters.start_timestamp).toLocaleString()}
                  </Typography>
                )}
                {currentFilters.end_timestamp && (
                  <Typography variant="body2">
                    <strong>End Date:</strong>{" "}
                    {new Date(currentFilters.end_timestamp).toLocaleString()}
                  </Typography>
                )}
                {currentFilters.action && (
                  <Typography variant="body2">
                    <strong>Action:</strong> {currentFilters.action}
                  </Typography>
                )}
                {currentFilters.purpose && (
                  <Typography variant="body2">
                    <strong>Purpose:</strong> {currentFilters.purpose}
                  </Typography>
                )}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No filters applied - all logs will be exported
              </Typography>
            )}
          </Box>
        </Box>

        {/* Field Selection */}
        <Box sx={{ mb: 3 }}>
          {/* <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              Select Fields to Export ({selectedFields.length} of{" "}
              {exportFields.length})
            </Typography>
            <Button size="small" onClick={handleSelectAll} variant="outlined">
              {selectedFields.length === exportFields.length
                ? "Deselect All"
                : "Select All"}
            </Button>
          </Box> */}

          <FormGroup>
            {filteredExportFields.map((field) => (
              <FormControlLabel
                key={field.field}
                control={
                  <Checkbox
                    checked={selectedFields.includes(field.field)}
                    onChange={(e) =>
                      handleFieldChange(field.field, e.target.checked)
                    }
                    size="small"
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {field.display_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {field.description}
                    </Typography>
                  </Box>
                }
                sx={{ mb: 1 }}
              />
            ))}
          </FormGroup>

          {selectedFields.length === 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Please select at least one field to export.
            </Alert>
          )}
        </Box>

        {/* Export Notes */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Export Notes (Optional)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this export for audit purposes..."
            size="small"
          />
        </Box>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} disabled={loading} startIcon={<CloseIcon />}>
          Cancel
        </Button>
        <Button
          onClick={handleExport}
          variant="contained"
          disabled={!isFormValid || loading}
          startIcon={
            loading ? <CircularProgress size={16} /> : <DownloadIcon />
          }
        >
          {loading ? "Exporting..." : "Export CSV"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExportModal;
