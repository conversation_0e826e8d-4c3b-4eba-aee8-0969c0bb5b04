// Log Statistics Component
"use client";

import React from "react";
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  Assessment as AssessmentIcon,
  People as PeopleIcon,
  Category as CategoryIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
} from "@mui/icons-material";
import { LogEntry } from "@/types/log";
import { format, formatDistanceToNow, differenceInMinutes, differenceInHours, differenceInDays } from 'date-fns';

interface LogStatsProps {
  logs: LogEntry[];
  totalCount: number;
  loading: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color = "primary",
  subtitle,
}) => (
  <Card sx={{ height: "100%" }}>
    <CardContent>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 1,
        }}
      >
        <Box sx={{ color: `${color}.main` }}>{icon}</Box>
        <Typography variant="h4" component="div" fontWeight="bold">
          {value}
        </Typography>
      </Box>
      <Typography variant="h6" component="div" gutterBottom>
        {title}
      </Typography>
      {subtitle && (
        <Typography variant="body2" color="text.secondary">
          {subtitle}
        </Typography>
      )}
    </CardContent>
  </Card>
);

const LogStats: React.FC<LogStatsProps> = ({ logs, totalCount, loading }) => {
  // Calculate statistics
  const getStats = () => {
    if (!logs || logs.length === 0) {
      return {
        currentPageLogs: 0,
        uniqueUsers: 0,
        uniqueActions: 0,
        recentActivity: [],
        topActions: [],
        topUsers: [],
      };
    }

    // Count unique users
    const uniqueUsers = new Set(logs.map((log) => log.userUuid)).size;

    // Count unique actions
    const uniqueActions = new Set(logs.map((log) => log.action)).size;

    // Get recent activity (last 5 logs sorted by timestamp)
    const recentActivity = [...logs]
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
      .slice(0, 5);

    // Count actions
    const actionCounts = logs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get top actions
    const topActions = Object.entries(actionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    // Count users
    const userCounts = logs.reduce((acc, log) => {
      acc[log.userUuid] = (acc[log.userUuid] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get top users
    const topUsers = Object.entries(userCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3);

    return {
      currentPageLogs: logs.length,
      uniqueUsers,
      uniqueActions,
      recentActivity,
      topActions,
      topUsers,
    };
  };

  const stats = getStats();

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp); // automatically parsed in local time
    const now = new Date();
  
    const diffMins = differenceInMinutes(now, date);
    const diffHours = differenceInHours(now, date);
    const diffDays = differenceInDays(now, date);
  
    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
  
    // Display in local timezone (formatted date + time)
    return format(date, 'dd MMM yyyy, hh:mm a');
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "member_export":
        return "primary";
      case "log_export":
        return "info";
      case "user_login":
        return "success";
      case "data_update":
        return "warning";
      case "report_generation":
        return "secondary";
      default:
        return "default";
    }
  };

  if (loading) {
    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {[1, 2, 3, 4].map((i) => (
          <Grid item xs={12} sm={6} md={3} key={i}>
            <Card sx={{ height: "100%" }}>
              <CardContent>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <Box sx={{ color: "grey.400" }}>
                    <AssessmentIcon />
                  </Box>
                  <Typography variant="h4" component="div" color="grey.400">
                    --
                  </Typography>
                </Box>
                <Typography variant="h6" component="div" color="grey.400">
                  Loading...
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  return (
    <Box sx={{ mb: 3 }}>
      {/* Main Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Logs"
            value={totalCount.toLocaleString()}
            icon={<AssessmentIcon fontSize="large" />}
            color="primary"
            subtitle={`${stats.currentPageLogs} on current page`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Unique Users"
            value={stats.uniqueUsers}
            icon={<PeopleIcon fontSize="large" />}
            color="success"
            subtitle="Active in current view"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Action Types"
            value={stats.uniqueActions}
            icon={<CategoryIcon fontSize="large" />}
            color="info"
            subtitle="Different log actions"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Recent Activity"
            value={stats.recentActivity.length}
            icon={<TrendingUpIcon fontSize="large" />}
            color="warning"
            subtitle="Latest log entries"
          />
        </Grid>
      </Grid>

      {/* Detailed Statistics */}
      <Grid container spacing={3}>
        {/* Top Actions */}
        {/* <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <CategoryIcon color="primary" />
                Top Actions
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {stats.topActions.length > 0 ? (
                <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                  {stats.topActions.map(([action, count]) => (
                    <Box
                      key={action}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Chip
                        label={action}
                        size="small"
                        color={getActionColor(action) as any}
                        variant="outlined"
                      />
                      <Typography variant="body2" fontWeight="bold">
                        {count}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No actions in current view
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid> */}

        {/* Top Users */}
        {/* <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <PeopleIcon color="success" />
                Most Active Users
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {stats.topUsers.length > 0 ? (
                <List dense>
                  {stats.topUsers.map(([userUuid, count]) => (
                    <ListItem key={userUuid} sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Typography
                            variant="body2"
                            sx={{ fontFamily: "monospace" }}
                          >
                            {userUuid.substring(0, 8)}...
                          </Typography>
                        }
                        secondary={`${count} log${count !== 1 ? "s" : ""}`}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No users in current view
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid> */}

        {/* Recent Activity */}
        {/* <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <ScheduleIcon color="warning" />
                Recent Activity
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {stats.recentActivity.length > 0 ? (
                <List dense>
                  {stats.recentActivity.map((log) => (
                    <ListItem key={log.uuid} sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Chip
                              label={log.action}
                              size="small"
                              color={getActionColor(log.action) as any}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {formatTimestamp(log.timestamp)}
                            </Typography>
                            <Typography
                              variant="caption"
                              sx={{ fontFamily: "monospace" }}
                            >
                              {log.userUuid.substring(0, 8)}...
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No recent activity
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid> */}
      </Grid>
    </Box>
  );
};

export default LogStats;
