"use client";

import { useAppSelector } from "@/store/hooks";
import { Role } from "@/types/role";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Box,
  Button,
  Checkbox,
  Chip,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
  useTheme
} from "@mui/material";
import React, { useEffect, useState } from "react";

interface RoleFormProps {
  role?: Role; // If provided, form is in edit mode
  isRoleNameDisable?: boolean;
  rolePermissions?: {
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  }; // Permissions for the role being edited
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
  currentUserRole?: string;
}

// Helper: get all unique modules/resources
const ACTIONS = [
  { key: "view", label: "View" },
  { key: "create", label: "Create" },
  { key: "update", label: "Update" },
  { key: "delete", label: "Delete" },
];

export function RoleForm({
  role,
  onSubmit,
  onCancel,
  loading = false,
  error,
  isRoleNameDisable,
}: RoleFormProps) {
  const theme = useTheme();
  const isEditMode = !!role;

  // Get allRolesPermissions from Redux
  const allRolesPermissions = useAppSelector(
    (state) => state.roles.allRolesPermissions?.roles || []
  );

  const MODULES = useAppSelector((state) =>
    state.modules.modules.map((mod) => ({ name: mod.name, module: mod.slug }))
  ).filter((mod) => !(role?.slug === "admin" && mod.module === "roles"));

  // Find permissions for the current role from Redux if not provided as a prop
  const reduxRolePermissions = React.useMemo(() => {
    if (!isEditMode || !role) return undefined;
    const found = allRolesPermissions.find(
      (r) => r.roleSlug === role.slug || r.roleName === role.name
    );

    if (!found) return undefined;
    // Convert array of module_permissions to the expected object shape
    const perms: {
      [module: string]: {
        view: boolean;
        create: boolean;
        update: boolean;
        delete: boolean;
      };
    } = {};
    found.modulePermissions.forEach((mod) => {
      perms[mod.moduleSlug] = {
        view: mod.view,
        create: mod.create,
        update: mod.update,
        delete: mod.delete,
      };
    });
    return perms;
  }, [isEditMode, role, allRolesPermissions]);

  // Form state for name/description
  const [name, setName] = useState(role ? role.name : "");
  const [description, setDescription] = useState(role ? role.description : ""); // Permission matrix state: { [module]: { view: boolean, create: boolean, update: boolean, delete: boolean } }
  const [permissionMatrix, setPermissionMatrix] = useState<{
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  }>(() => {
    // Priority: prop > redux > empty
    console.log(reduxRolePermissions, "reduxRolePermissions");
    const source = reduxRolePermissions || {};
    const matrix: any = {};
    MODULES.forEach(({ module }) => {
      matrix[module] = {
        view: source[module]?.view || false,
        create: source[module]?.create || false,
        update: source[module]?.update || false,
        delete: source[module]?.delete || false,
      };
    });
    console.log(matrix, "matrix");
    return matrix;
  });

  // Update permission matrix when rolePermissions or reduxRolePermissions change
  useEffect(() => {
    const source = reduxRolePermissions;
    if (source) {
      const matrix: any = {};
      MODULES.forEach(({ module }) => {
        matrix[module] = {
          view: source[module]?.view || false,
          create: source[module]?.create || false,
          update: source[module]?.update || false,
          delete: source[module]?.delete || false,
        };
      });
      setPermissionMatrix(matrix);
    }
  }, [reduxRolePermissions]);

  // Handle select all for an action (column)
  const handleSelectAllAction = (action: string, checked: boolean) => {
    setPermissionMatrix((prev) => {
      const updated = { ...prev };
      MODULES.forEach(({ module }) => {
        updated[module] = { ...updated[module], [action]: checked };
      });
      return updated;
    });
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!name.trim()) return;
    if (!description.trim()) return;

    // Get original permissions for comparison
    const originalPermissions = reduxRolePermissions || {};

    // Build API data - only include modules that have changed from original
    const permissions: any = {};
    MODULES.forEach(({ module }) => {
      const current = permissionMatrix[module];
      const original = originalPermissions[module] || {
        view: false,
        create: false,
        update: false,
        delete: false,
      };
      const hasChanged =
        current.view !== original.view ||
        current.create !== original.create ||
        current.update !== original.update ||
        current.delete !== original.delete;
      if (hasChanged) {
        permissions[module] = {
          view: current.view,
          create: current.create,
          update: current.update,
          delete: current.delete,
        };
      }
    });
    const apiData = {
      role: {
        name: name.trim(),
        description: description.trim(),
      },
      permissions,
    };
    await onSubmit(apiData);
  };

  // Render
  return (
    <Box>
      <Box component="form" onSubmit={handleFormSubmit}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        <Typography variant="h6" gutterBottom>
          Basic Information
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Role Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              fullWidth
              required
              disabled={loading || isRoleNameDisable}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              fullWidth
              required
              rows={3}
              disabled={loading}
            />
          </Grid>
        </Grid>
        <Divider sx={{ my: 3 }} />
        <Typography
          variant="h6"
          sx={{
            mb: 2,
            fontWeight: 600,
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <span style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <span style={{ fontSize: 20, fontWeight: 700 }}>Permissions</span>
          </span>
          <Box sx={{ flex: 1 }} />
          <FormControlLabel
            control={
              <Checkbox
                checked={MODULES.every(({ module }) =>
                  Object.values(permissionMatrix[module]).every(Boolean)
                )}
                indeterminate={
                  MODULES.some(({ module }) =>
                    Object.values(permissionMatrix[module]).some(Boolean)
                  ) &&
                  !MODULES.every(({ module }) =>
                    Object.values(permissionMatrix[module]).every(Boolean)
                  )
                }
                onChange={(e) => {
                  const checked = e.target.checked;
                  setPermissionMatrix((prev) => {
                    const updated = { ...prev };
                    MODULES.forEach(({ module }) => {
                      updated[module] = {
                        view: checked,
                        create: checked,
                        update: checked,
                        delete: checked,
                      };
                    });
                    return updated;
                  });
                }}
                disabled={loading}
              />
            }
            label={
              <Typography fontWeight={500}>Select All Permissions</Typography>
            }
            sx={{ mr: 2 }}
          />
        </Typography>
        <Box>
          {MODULES.map(({ module, name }) => {
            const selectedCount = Object.values(
              permissionMatrix[module]
            ).filter(Boolean).length;
            const totalCount = ACTIONS.length;
            const allSelected = selectedCount === totalCount;
            const noneSelected = selectedCount === 0;
            const indeterminate = !allSelected && !noneSelected;
            return (
              <Accordion
                key={module}
                TransitionProps={{ unmountOnExit: true }}
                sx={{
                  mb: 1,
                  borderRadius: 2,
                  boxShadow: "none",
                  border: "1px solid #eee",
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={allSelected}
                        indeterminate={indeterminate}
                        onChange={(e) => {
                          const checked = e.target.checked;
                          setPermissionMatrix((prev) => ({
                            ...prev,
                            [module]: {
                              view: checked,
                              create: checked,
                              update: checked,
                              delete: checked,
                            },
                          }));
                        }}
                        onClick={(e) => e.stopPropagation()}
                        disabled={loading}
                      />
                    }
                    label={<Typography fontWeight={600}>{name}</Typography>}
                    onClick={(e) => e.stopPropagation()}
                    sx={{ mr: 2 }}
                  />
                  <Chip
                    label={`${selectedCount}/${totalCount}`}
                    color={
                      allSelected
                        ? "primary"
                        : indeterminate
                        ? "warning"
                        : "default"
                    }
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </AccordionSummary>
                <AccordionDetails>
                  <Box
                    sx={{ display: "flex", flexWrap: "wrap", gap: 3, pl: 1 }}
                  >
                    {ACTIONS.map((action) => (
                      <Box
                        key={action.key}
                        sx={{
                          minWidth: 200,
                          width: "35%",
                          mb: 2,
                          display: "flex",
                          flexWrap: "wrap",
                          flexDirection: "column",
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={
                                permissionMatrix[module][
                                  action.key as
                                    | "view"
                                    | "create"
                                    | "update"
                                    | "delete"
                                ]
                              }
                              onChange={(_, checked) =>
                                setPermissionMatrix((prev) => ({
                                  ...prev,
                                  [module]: {
                                    ...prev[module],
                                    [action.key]: checked,
                                  },
                                }))
                              }
                              disabled={loading}
                            />
                          }
                          label={
                            <Typography fontWeight={600}>
                              {action.label} {module}
                            </Typography>
                          }
                        />
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ ml: 4 }}
                        >
                          {/* Placeholder description, replace with real one if available */}
                          {action.label} {module.toLowerCase()} information
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Box>
        <Divider sx={{ my: 2 }} />
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" fontWeight={600}>
            Selected Permissions (
            {MODULES.reduce(
              (acc, { module }) =>
                acc +
                Object.values(permissionMatrix[module]).filter(Boolean).length,
              0
            )}
            )
          </Typography>
          <Box sx={{ mt: 1, display: "flex", flexWrap: "wrap", gap: 2 }}>
            {MODULES.map(({ module }) => {
              const selected = ACTIONS.filter(
                (action) =>
                  permissionMatrix[module][
                    action.key as "view" | "create" | "update" | "delete"
                  ]
              );
              if (selected.length === 0) return null;
              return (
                <Chip
                  key={module}
                  label={`View ${module}`}
                  color="primary"
                  variant="outlined"
                />
              );
            })}
          </Box>
          {error && (
            <Typography color="error" sx={{ mt: 1 }}>
              {error}
            </Typography>
          )}
        </Box>
        <Box
          sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}
        >
          <Button onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {isEditMode ? "Update" : "Save"}
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
