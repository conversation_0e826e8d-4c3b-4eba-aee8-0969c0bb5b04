'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>nackbar,
  Alert,
  AlertTitle,
  Button,
  Box,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import globalErrorHandler, { ToastNotification } from '@/lib/errorHandler';

interface ToastState extends ToastNotification {
  open: boolean;
}

const ToastNotifications: React.FC = () => {
  const [toasts, setToasts] = useState<ToastState[]>([]);

  useEffect(() => {
    // Register toast callback
    globalErrorHandler.registerToastCallback((toast: ToastNotification) => {
      const newToast: ToastState = {
        ...toast,
        open: true,
      };
      
      setToasts(prev => [...prev, newToast]);
    });

    return () => {
      // Cleanup if needed
    };
  }, []);

  const handleClose = (id: string) => {
    setToasts(prev => 
      prev.map(toast => 
        toast.id === id ? { ...toast, open: false } : toast
      )
    );
  };

  const handleExited = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const getSeverity = (type: ToastNotification['type']) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'info';
    }
  };

  const getIcon = (type: ToastNotification['type']) => {
    switch (type) {
      case 'success':
        return <SuccessIcon />;
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <InfoIcon />;
    }
  };

  return (
    <>
      {toasts.map((toast) => (
        <Snackbar
          key={toast.id}
          open={toast.open}
          autoHideDuration={toast.duration || 6000}
          onClose={() => handleClose(toast.id)}
          TransitionComponent={Collapse}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{ mt: 8 }} // Account for app bar
        >
          <Alert
            severity={getSeverity(toast.type)}
            icon={getIcon(toast.type)}
            onClose={() => handleClose(toast.id)}
            action={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {toast.action && (
                  <Button
                    color="inherit"
                    size="small"
                    onClick={toast.action.onClick}
                    sx={{ minWidth: 'auto', px: 1 }}
                  >
                    {toast.action.label}
                  </Button>
                )}
                <IconButton
                  size="small"
                  color="inherit"
                  onClick={() => handleClose(toast.id)}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            }
            sx={{
              width: '100%',
              maxWidth: 400,
              '& .MuiAlert-message': {
                whiteSpace: 'pre-line',
              },
            }}
          >
            <AlertTitle sx={{ fontWeight: 600 }}>
              {toast.title}
            </AlertTitle>
            {toast.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default ToastNotifications; 