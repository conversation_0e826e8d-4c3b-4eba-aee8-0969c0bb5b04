'use client';

import React from 'react';
import {
  Box,
  Typography,
  Breadcrumbs,
  Link as MuiLink,
  Button,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import { NavigateNext as NavigateNextIcon } from '@mui/icons-material';
import Link from 'next/link';

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
  backButton?: {
    href: string;
    label: string;
  };
}

export function PageHeader({
  title,
  subtitle,
  breadcrumbs,
  actions,
  backButton,
}: PageHeaderProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box sx={{ mb: 4 }}>
      {/* Breadcrumbs */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 2 }}
        >
          {breadcrumbs.map((item, index) => (
            <MuiLink
              key={index}
              href={item.href || '#'}
              component={item.href ? Link : 'span'}
              color={item.href ? 'inherit' : 'text.primary'}
              sx={{
                textDecoration: item.href ? 'none' : 'none',
                '&:hover': {
                  textDecoration: item.href ? 'underline' : 'none',
                },
              }}
            >
              {item.label}
            </MuiLink>
          ))}
        </Breadcrumbs>
      )}

      {/* Back Button */}
      {backButton && (
        <Box sx={{ mb: 2 }}>
          <Button
            component={Link}
            href={backButton.href}
            variant="outlined"
            size="small"
            startIcon={<NavigateNextIcon sx={{ transform: 'rotate(180deg)' }} />}
          >
            {backButton.label}
          </Button>
        </Box>
      )}

      {/* Header Content */}
      <Box sx={{ 
        display: 'flex', 
        flexDirection: isMobile ? 'column' : 'row',
        alignItems: isMobile ? 'flex-start' : 'center',
        justifyContent: 'space-between',
        gap: 2,
      }}>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="body1" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        
        {actions && (
          <Stack direction={isMobile ? 'column' : 'row'} spacing={1} sx={{ flexShrink: 0 }}>
            {actions}
          </Stack>
        )}
      </Box>
    </Box>
  );
} 