'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert,
  AlertTitle,
  AlertColor,
  IconButton,
  Collapse,
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Chip,
  Stack,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';

// Notification Types
export interface Notification {
  id: string;
  type: AlertColor;
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  persistent?: boolean;
  timestamp: Date;
}

export interface ConfirmationDialog {
  id: string;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  severity?: AlertColor;
  onConfirm: () => void;
  onCancel?: () => void;
}

export interface SystemAlert {
  id: string;
  type: AlertColor;
  title: string;
  message: string;
  dismissible?: boolean;
  timestamp: Date;
}

// Context Types
interface NotificationContextType {
  notifications: Notification[];
  confirmations: ConfirmationDialog[];
  systemAlerts: SystemAlert[];
  showNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  showSuccess: (message: string, title?: string) => void;
  showError: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
  showConfirmation: (confirmation: Omit<ConfirmationDialog, 'id'>) => void;
  showSystemAlert: (alert: Omit<SystemAlert, 'id' | 'timestamp'>) => void;
  dismissNotification: (id: string) => void;
  dismissConfirmation: (id: string) => void;
  dismissSystemAlert: (id: string) => void;
  clearAllNotifications: () => void;
  clearAllSystemAlerts: () => void;
}

// Context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Provider Component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [confirmations, setConfirmations] = useState<ConfirmationDialog[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);

  const showNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-dismiss if not persistent
    if (!notification.persistent && notification.duration !== 0) {
      setTimeout(() => {
        dismissNotification(id);
      }, notification.duration || 6000);
    }
  }, []);

  const showSuccess = useCallback((message: string, title?: string) => {
    showNotification({
      type: 'success',
      title,
      message,
      duration: 4000,
    });
  }, [showNotification]);

  const showError = useCallback((message: string, title?: string) => {
    showNotification({
      type: 'error',
      title,
      message,
      duration: 8000,
    });
  }, [showNotification]);

  const showWarning = useCallback((message: string, title?: string) => {
    showNotification({
      type: 'warning',
      title,
      message,
      duration: 6000,
    });
  }, [showNotification]);

  const showInfo = useCallback((message: string, title?: string) => {
    showNotification({
      type: 'info',
      title,
      message,
      duration: 5000,
    });
  }, [showNotification]);

  const showConfirmation = useCallback((confirmation: Omit<ConfirmationDialog, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setConfirmations(prev => [...prev, { ...confirmation, id }]);
  }, []);

  const showSystemAlert = useCallback((alert: Omit<SystemAlert, 'id' | 'timestamp'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newAlert: SystemAlert = {
      ...alert,
      id,
      timestamp: new Date(),
    };
    setSystemAlerts(prev => [...prev, newAlert]);
  }, []);

  const dismissNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const dismissConfirmation = useCallback((id: string) => {
    setConfirmations(prev => prev.filter(c => c.id !== id));
  }, []);

  const dismissSystemAlert = useCallback((id: string) => {
    setSystemAlerts(prev => prev.filter(a => a.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const clearAllSystemAlerts = useCallback(() => {
    setSystemAlerts([]);
  }, []);

  const value: NotificationContextType = {
    notifications,
    confirmations,
    systemAlerts,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation,
    showSystemAlert,
    dismissNotification,
    dismissConfirmation,
    dismissSystemAlert,
    clearAllNotifications,
    clearAllSystemAlerts,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationRenderer />
    </NotificationContext.Provider>
  );
};

// Hook
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Notification Renderer Component
const NotificationRenderer: React.FC = () => {
  const { 
    notifications, 
    confirmations, 
    systemAlerts,
    dismissNotification, 
    dismissConfirmation,
    dismissSystemAlert 
  } = useNotifications();

  const handleConfirm = (confirmation: ConfirmationDialog) => {
    confirmation.onConfirm();
    dismissConfirmation(confirmation.id);
  };

  const handleCancel = (confirmation: ConfirmationDialog) => {
    confirmation.onCancel?.();
    dismissConfirmation(confirmation.id);
  };

  return (
    <>
      {/* Toast Notifications */}
      {notifications.map((notification) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.duration || 6000}
          onClose={() => dismissNotification(notification.id)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{ mt: 8 }}
        >
          <Alert
            onClose={() => dismissNotification(notification.id)}
            severity={notification.type}
            variant="filled"
            sx={{ width: '100%', minWidth: 300 }}
            action={
              notification.action && (
                <Button
                  color="inherit"
                  size="small"
                  onClick={notification.action.onClick}
                >
                  {notification.action.label}
                </Button>
              )
            }
          >
            {notification.title && (
              <AlertTitle>{notification.title}</AlertTitle>
            )}
            {notification.message}
          </Alert>
        </Snackbar>
      ))}

      {/* Confirmation Dialogs */}
      {confirmations.map((confirmation) => (
        <Dialog
          key={confirmation.id}
          open={true}
          onClose={() => handleCancel(confirmation)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            color: confirmation.severity === 'error' ? 'error.main' : 'inherit'
          }}>
            {confirmation.severity === 'error' && <ErrorIcon color="error" />}
            {confirmation.severity === 'warning' && <WarningIcon color="warning" />}
            {confirmation.severity === 'info' && <InfoIcon color="info" />}
            {confirmation.title}
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              {confirmation.message}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => handleCancel(confirmation)}>
              {confirmation.cancelText || 'Cancel'}
            </Button>
            <Button 
              onClick={() => handleConfirm(confirmation)} 
              variant="contained"
              color={confirmation.severity === 'error' ? 'error' : 'primary'}
              autoFocus
            >
              {confirmation.confirmText || 'Confirm'}
            </Button>
          </DialogActions>
        </Dialog>
      ))}

      {/* System Alerts */}
      {systemAlerts.length > 0 && (
        <Box sx={{ 
          position: 'fixed', 
          top: 80, 
          right: 16, 
          zIndex: 1200,
          maxWidth: 400,
          width: '100%'
        }}>
          <Stack spacing={1}>
            {systemAlerts.map((alert) => (
              <Collapse key={alert.id} in={true}>
                <Alert
                  severity={alert.type}
                  onClose={alert.dismissible ? () => dismissSystemAlert(alert.id) : undefined}
                  sx={{ 
                    boxShadow: 3,
                    '& .MuiAlert-message': {
                      width: '100%'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {alert.title}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {alert.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                        {alert.timestamp.toLocaleTimeString()}
                      </Typography>
                    </Box>
                  </Box>
                </Alert>
              </Collapse>
            ))}
          </Stack>
        </Box>
      )}
    </>
  );
};

// Notification Badge Component
export const NotificationBadge: React.FC<{ count?: number }> = ({ count = 0 }) => {
  if (count === 0) return null;

  return (
    <Chip
      icon={<NotificationIcon />}
      label={count}
      size="small"
      color="error"
      sx={{
        position: 'absolute',
        top: -8,
        right: -8,
        minWidth: 20,
        height: 20,
        fontSize: '0.75rem',
      }}
    />
  );
};

// Quick Notification Helpers
export const notify = {
  success: (message: string, title?: string) => {
    // This will be used with the context
    console.log('Success:', message);
  },
  error: (message: string, title?: string) => {
    console.error('Error:', message);
  },
  warning: (message: string, title?: string) => {
    console.warn('Warning:', message);
  },
  info: (message: string, title?: string) => {
    console.info('Info:', message);
  },
  confirm: (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
    // This will be used with the context
    console.log('Confirm:', message);
  },
}; 