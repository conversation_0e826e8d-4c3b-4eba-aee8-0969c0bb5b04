'use client';

import React from 'react';
import {
  Box,
  Skeleton,
  CircularProgress,
  LinearProgress,
  Card,
  CardContent,
  Typography,
  Backdrop,
  Fade,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CloudDownload as DownloadIcon,
} from '@mui/icons-material';

// Skeleton Components
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 6 
}) => (
  <Paper sx={{ width: '100%', overflow: 'hidden' }}>
    <Table>
      <TableHead>
        <TableRow>
          {Array.from({ length: columns }).map((_, index) => (
            <TableCell key={index}>
              <Skeleton variant="text" width="80%" />
            </TableCell>
          ))}
        </TableRow>
      </TableHead>
      <TableBody>
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <TableRow key={rowIndex}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <TableCell key={colIndex}>
                <Skeleton variant="text" width="90%" />
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </Paper>
);

export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <Grid container spacing={3}>
    {Array.from({ length: count }).map((_, index) => (
      <Grid item xs={12} sm={6} md={4} key={index}>
        <Card>
          <CardContent>
            <Skeleton variant="rectangular" height={140} sx={{ mb: 2 }} />
            <Skeleton variant="text" width="60%" sx={{ mb: 1 }} />
            <Skeleton variant="text" width="40%" />
          </CardContent>
        </Card>
      </Grid>
    ))}
  </Grid>
);

export const StatsCardSkeleton: React.FC = () => (
  <Grid container spacing={2}>
    {Array.from({ length: 6 }).map((_, index) => (
      <Grid item xs={6} sm={4} md={2} key={index}>
        <Card sx={{ height: '100%' }}>
          <CardContent sx={{ textAlign: 'center', p: { xs: 1.5, md: 2 } }}>
            <Skeleton variant="text" width="60%" height={40} sx={{ mb: 1 }} />
            <Skeleton variant="text" width="80%" />
          </CardContent>
        </Card>
      </Grid>
    ))}
  </Grid>
);

export const FormSkeleton: React.FC = () => (
  <Card>
    <CardContent>
      <Skeleton variant="text" width="40%" height={32} sx={{ mb: 3 }} />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <Skeleton variant="rectangular" height={56} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Skeleton variant="rectangular" height={56} />
        </Grid>
        <Grid item xs={12}>
          <Skeleton variant="rectangular" height={56} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Skeleton variant="rectangular" height={56} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Skeleton variant="rectangular" height={56} />
        </Grid>
      </Grid>
      <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
        <Skeleton variant="rectangular" width={100} height={36} />
        <Skeleton variant="rectangular" width={100} height={36} />
      </Box>
    </CardContent>
  </Card>
);

export const MemberTableRowSkeleton: React.FC = () => (
  <TableRow>
    <TableCell>
      <Skeleton variant="circular" width={20} height={20} />
    </TableCell>
    <TableCell>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Skeleton variant="circular" width={32} height={32} />
        <Box>
          <Skeleton variant="text" width={120} />
          <Skeleton variant="text" width={80} />
        </Box>
      </Box>
    </TableCell>
    <TableCell>
      <Skeleton variant="text" width={150} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 1 }} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 1 }} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="rectangular" width={60} height={24} sx={{ borderRadius: 1 }} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="text" width={40} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="text" width={60} />
    </TableCell>
    <TableCell align="center">
      <Skeleton variant="text" width={60} />
    </TableCell>
    <TableCell align="center">
      <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} variant="circular" width={32} height={32} />
        ))}
      </Box>
    </TableCell>
  </TableRow>
);

// Progress Components
export const LoadingOverlay: React.FC<{ 
  open: boolean; 
  message?: string;
  transparent?: boolean;
}> = ({ open, message = 'Loading...', transparent = false }) => (
  <Backdrop
    sx={{
      color: '#fff',
      zIndex: (theme) => theme.zIndex.drawer + 1,
      backgroundColor: transparent ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.8)',
    }}
    open={open}
  >
    <Fade in={open}>
      <Box sx={{ textAlign: 'center' }}>
        <CircularProgress color="inherit" size={60} />
        {message && (
          <Typography variant="h6" sx={{ mt: 2, color: 'white' }}>
            {message}
          </Typography>
        )}
      </Box>
    </Fade>
  </Backdrop>
);

export const PageLoading: React.FC<{ message?: string }> = ({ message = 'Loading page...' }) => (
  <Box sx={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center', 
    minHeight: '50vh',
    gap: 2
  }}>
    <CircularProgress size={60} />
    <Typography variant="h6" color="text.secondary">
      {message}
    </Typography>
  </Box>
);

export const InlineLoading: React.FC<{ message?: string; size?: number }> = ({ 
  message = 'Loading...', 
  size = 20 
}) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    <CircularProgress size={size} />
    {message && (
      <Typography variant="body2" color="text.secondary">
        {message}
      </Typography>
    )}
  </Box>
);

export const ProgressBar: React.FC<{ 
  value: number; 
  message?: string;
  showPercentage?: boolean;
}> = ({ value, message, showPercentage = true }) => (
  <Box sx={{ width: '100%' }}>
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
      {message && (
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      )}
      {showPercentage && (
        <Typography variant="body2" color="text.secondary">
          {Math.round(value)}%
        </Typography>
      )}
    </Box>
    <LinearProgress 
      variant="determinate" 
      value={value} 
      sx={{ height: 8, borderRadius: 4 }}
    />
  </Box>
);

// Loading States for Different Components
export const DataTableLoading: React.FC<{ message?: string }> = ({ message }) => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <CircularProgress size={40} />
    {message && (
      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
        {message}
      </Typography>
    )}
  </Box>
);

export const ChartLoading: React.FC<{ height?: number }> = ({ height = 300 }) => (
  <Box sx={{ 
    height, 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center',
    border: '1px dashed',
    borderColor: 'divider',
    borderRadius: 1
  }}>
    <Box sx={{ textAlign: 'center' }}>
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
        Loading chart...
      </Typography>
    </Box>
  </Box>
);

export const ImageLoading: React.FC<{ aspectRatio?: number }> = ({ aspectRatio = 16/9 }) => (
  <Box sx={{ 
    position: 'relative',
    width: '100%',
    paddingTop: `${(1 / aspectRatio) * 100}%`,
    backgroundColor: 'grey.100',
    borderRadius: 1,
    overflow: 'hidden'
  }}>
    <Box sx={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      textAlign: 'center'
    }}>
      <CircularProgress size={30} />
    </Box>
  </Box>
);

// Optimistic UI Loading States
export const OptimisticButton: React.FC<{
  children: React.ReactNode;
  loading: boolean;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'text' | 'outlined' | 'contained';
  size?: 'small' | 'medium' | 'large';
  startIcon?: React.ReactNode;
}> = ({ 
  children, 
  loading, 
  onClick, 
  disabled = false,
  variant = 'contained',
  size = 'medium',
  startIcon
}) => (
  <Box sx={{ position: 'relative' }}>
    <Box
      component="button"
      onClick={onClick}
      disabled={disabled || loading}
      sx={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1,
        padding: size === 'small' ? '6px 16px' : size === 'large' ? '12px 24px' : '8px 20px',
        border: variant === 'outlined' ? '1px solid' : 'none',
        borderColor: 'primary.main',
        borderRadius: 1,
        backgroundColor: variant === 'contained' ? 'primary.main' : 'transparent',
        color: variant === 'contained' ? 'white' : 'primary.main',
        fontSize: size === 'small' ? '0.875rem' : size === 'large' ? '1.125rem' : '1rem',
        fontWeight: 500,
        cursor: disabled || loading ? 'not-allowed' : 'pointer',
        opacity: disabled || loading ? 0.6 : 1,
        transition: 'all 0.2s',
        '&:hover': {
          backgroundColor: variant === 'contained' ? 'primary.dark' : 'primary.light',
          opacity: disabled || loading ? 0.6 : 0.8,
        },
        '&:disabled': {
          cursor: 'not-allowed',
        }
      }}
    >
      {startIcon}
      {children}
    </Box>
    {loading && (
      <CircularProgress
        size={16}
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          marginTop: '-8px',
          marginLeft: '-8px',
        }}
      />
    )}
  </Box>
);

// Loading Context for Global Loading States
export const LoadingContext = React.createContext<{
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  loadingMessage: string;
  setLoadingMessage: (message: string) => void;
}>({
  isLoading: false,
  setIsLoading: () => {},
  loadingMessage: '',
  setLoadingMessage: () => {},
});

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingMessage, setLoadingMessage] = React.useState('');

  return (
    <LoadingContext.Provider value={{ 
      isLoading, 
      setIsLoading, 
      loadingMessage, 
      setLoadingMessage 
    }}>
      {children}
      <LoadingOverlay open={isLoading} message={loadingMessage} />
    </LoadingContext.Provider>
  );
};

export const useLoading = () => React.useContext(LoadingContext); 