"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth0 } from "@/hooks/useAuth0";
import { MemberLoadingScreen } from "./MemberLoadingScreen";

interface MemberRouteGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
}

export function MemberRouteGuard({
  children,
  requiredPermissions = [],
}: MemberRouteGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuth0();
  const router = useRouter();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return <MemberLoadingScreen />;
  }

  // If not authenticated, don't render anything
  // (redirect will happen in useEffect)
  if (!isAuthenticated) {
    return null;
  }

  // Note: Permission checking would be done by backend on API calls
  // Frontend just checks if user is authenticated via Auth0

  return <>{children}</>;
}
