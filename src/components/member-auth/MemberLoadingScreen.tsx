import { Box, CircularProgress } from "@mui/material";

export function MemberLoadingScreen() {
  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
        }}
      >
        <CircularProgress size={60} sx={{ color: "#fff" }} />
        <Box
          component="img"
          src="/CO-gold.svg"
          alt="Member Portal Logo"
          sx={{
            width: 120,
            height: "auto",
            opacity: 0.8,
          }}
        />
      </Box>
    </Box>
  );
}
