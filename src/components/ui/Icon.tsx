import React from "react";

interface IconProps {
  name: string;
  className?: string;
}

export function Icon({ name, className = "" }: IconProps) {
  // This is a simple icon component that can be extended with actual icon library
  // For now, we'll use a placeholder approach
  const getIconContent = (iconName: string) => {
    switch (iconName) {
      case "search":
        return "🔍";
      case "filter":
        return "⚙️";
      case "plus":
        return "➕";
      case "edit":
        return "✏️";
      case "trash":
        return "🗑️";
      case "users":
        return "👥";
      case "flag":
        return "🚩";
      case "chart":
        return "📊";
      case "chevron-up":
        return "▲";
      case "chevron-down":
        return "▼";
      case "chevron-left":
        return "◀";
      case "chevron-right":
        return "▶";
      case "building":
        return "🏢";
      case "info":
        return "ℹ️";
      case "map-pin":
        return "📍";
      case "user":
        return "👤";
      case "share":
        return "📤";
      case "settings":
        return "⚙️";
      case "shield-check":
        return "🛡️";
      case "tag":
        return "🏷️";
      case "cog":
        return "⚙️";
      case "template":
        return "📋";
      case "save":
        return "💾";
      case "check":
        return "✅";
      case "x":
        return "❌";
      case "download":
        return "⬇️";
      case "trending-up":
        return "📈";
      case "trending-down":
        return "📉";
      case "minus":
        return "➖";
      case "alert-circle":
        return "⚠️";
      case "inbox":
        return "📥";
      case "check-circle":
        return "✅";
      case "folder":
        return "📁";
      default:
        return "📄";
    }
  };

  return (
    <span className={`inline-block ${className}`} role="img" aria-label={name}>
      {getIconContent(name)}
    </span>
  );
}
