import React from "react";
import { Icon } from "./Icon";
import { Checkbox } from "./Checkbox";
import { Button } from "./Button";
import { LoadingSpinner } from "./LoadingSpinner";

interface Column<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (item: T) => React.ReactNode;
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: PaginationInfo;
  selectedItems?: T[];
  sortBy?: keyof T;
  sortOrder?: "asc" | "desc";
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSort?: (column: keyof T, order: "asc" | "desc") => void;
  onItemSelect?: (item: T, isSelected: boolean) => void;
  onBulkSelect?: (isSelected: boolean) => void;
  getItemId?: (item: T) => string | number;
}

export function DataTable<T>({
  data,
  columns,
  loading = false,
  pagination,
  selectedItems = [],
  sortBy,
  sortOrder = "desc",
  onPageChange,
  onPageSizeChange,
  onSort,
  onItemSelect,
  onBulkSelect,
  getItemId = (item: any) => item.id,
}: DataTableProps<T>) {
  const allSelected = data.length > 0 && selectedItems.length === data.length;
  const someSelected =
    selectedItems.length > 0 && selectedItems.length < data.length;

  const handleSort = (column: keyof T) => {
    if (!onSort) return;

    const newOrder = sortBy === column && sortOrder === "asc" ? "desc" : "asc";
    onSort(column, newOrder);
  };

  const handleItemSelect = (item: T, isSelected: boolean) => {
    onItemSelect?.(item, isSelected);
  };

  const handleBulkSelect = (isSelected: boolean) => {
    onBulkSelect?.(isSelected);
  };

  if (loading && data.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {onItemSelect && (
                <th className="px-6 py-3 text-left">
                  <Checkbox
                    checked={allSelected}
                    indeterminate={someSelected}
                    onChange={handleBulkSelect}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.sortable ? "cursor-pointer hover:bg-gray-100" : ""
                  }`}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && sortBy === column.key && (
                      <Icon
                        name={
                          sortOrder === "asc" ? "chevron-up" : "chevron-down"
                        }
                        className="w-4 h-4"
                      />
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr
                key={getItemId(item)}
                className="hover:bg-gray-50 transition-colors"
              >
                {onItemSelect && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Checkbox
                      checked={selectedItems.some(
                        (selected) => getItemId(selected) === getItemId(item)
                      )}
                      onChange={(checked) => handleItemSelect(item, checked)}
                    />
                  </td>
                )}
                {columns.map((column) => (
                  <td
                    key={String(column.key)}
                    className="px-6 py-4 whitespace-nowrap"
                  >
                    {column.render
                      ? column.render(item)
                      : String(item[column.key] || "")}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button
              variant="secondary"
              size="sm"
              disabled={!pagination.hasPrevPage}
              onClick={() => onPageChange?.(pagination.currentPage - 1)}
            >
              Previous
            </Button>
            <Button
              variant="secondary"
              size="sm"
              disabled={!pagination.hasNextPage}
              onClick={() => onPageChange?.(pagination.currentPage + 1)}
            >
              Next
            </Button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {(pagination.currentPage - 1) * pagination.pageSize + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    pagination.currentPage * pagination.pageSize,
                    pagination.totalItems
                  )}
                </span>{" "}
                of <span className="font-medium">{pagination.totalItems}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <Button
                  variant="secondary"
                  size="sm"
                  disabled={!pagination.hasPrevPage}
                  onClick={() => onPageChange?.(pagination.currentPage - 1)}
                >
                  <Icon name="chevron-left" className="w-4 h-4" />
                </Button>

                {/* Page numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.totalPages) },
                  (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={
                          page === pagination.currentPage
                            ? "primary"
                            : "secondary"
                        }
                        size="sm"
                        onClick={() => onPageChange?.(page)}
                      >
                        {page}
                      </Button>
                    );
                  }
                )}

                <Button
                  variant="secondary"
                  size="sm"
                  disabled={!pagination.hasNextPage}
                  onClick={() => onPageChange?.(pagination.currentPage + 1)}
                >
                  <Icon name="chevron-right" className="w-4 h-4" />
                </Button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Empty state */}
      {!loading && data.length === 0 && (
        <div className="text-center py-12">
          <Icon name="inbox" className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No data found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new item.
          </p>
        </div>
      )}
    </div>
  );
}
