import React from "react";
import { Icon } from "./Icon";

interface StatisticsCardProps {
  title: string;
  value: number | string;
  change?: number | string;
  changeLabel?: string;
  icon?: string;
  trend?: "up" | "down" | "neutral";
  className?: string;
}

export function StatisticsCard({
  title,
  value,
  change,
  changeLabel,
  icon,
  trend = "neutral",
  className = "",
}: StatisticsCardProps) {
  const getTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return "trending-up";
      case "down":
        return "trending-down";
      default:
        return "minus";
    }
  };

  return (
    <div className={`bg-white overflow-hidden shadow rounded-lg ${className}`}>
      <div className="p-5">
        <div className="flex items-center">
          {icon && (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <Icon name={icon} className="w-5 h-5 text-blue-600" />
              </div>
            </div>
          )}
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {typeof value === "number" ? value.toLocaleString() : value}
                </div>
                {change !== undefined && (
                  <div
                    className={`ml-2 flex items-baseline text-sm font-semibold ${getTrendColor()}`}
                  >
                    <Icon name={getTrendIcon()} className="w-4 h-4 mr-1" />
                    <span>
                      {typeof change === "number"
                        ? Math.abs(change).toLocaleString()
                        : change}
                      {changeLabel && ` ${changeLabel}`}
                    </span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
}
