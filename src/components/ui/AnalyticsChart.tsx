import React from "react";

interface AnalyticsData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

interface AnalyticsChartProps {
  data: AnalyticsData;
  className?: string;
}

export function AnalyticsChart({ data, className = "" }: AnalyticsChartProps) {
  // This is a simple chart component that can be extended with actual chart library
  // For now, we'll display the data in a simple format
  return (
    <div className={`bg-gray-50 p-4 rounded-lg ${className}`}>
      <div className="space-y-4">
        {data.datasets.map((dataset, datasetIndex) => (
          <div key={datasetIndex}>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {dataset.label}
            </h4>
            <div className="space-y-2">
              {data.labels.map((label, labelIndex) => (
                <div
                  key={labelIndex}
                  className="flex items-center justify-between"
                >
                  <span className="text-sm text-gray-600">{label}</span>
                  <div className="flex items-center space-x-2">
                    <div
                      className="h-2 bg-blue-500 rounded"
                      style={{
                        width: `${Math.min(
                          (dataset.data[labelIndex] || 0) * 2,
                          100
                        )}%`,
                      }}
                    />
                    <span className="text-sm font-medium text-gray-900">
                      {dataset.data[labelIndex] || 0}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
