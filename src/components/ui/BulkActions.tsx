import React from "react";
import { Icon } from "./Icon";
import { Button } from "./Button";

interface BulkAction {
  key: string;
  label: string;
  icon: string;
  variant: "primary" | "secondary" | "success" | "warning" | "danger";
}

interface BulkActionsProps {
  selectedCount: number;
  actions: BulkAction[];
  onAction: (action: string) => void;
  onClearSelection: () => void;
  className?: string;
}

export function BulkActions({
  selectedCount,
  actions,
  onAction,
  onClearSelection,
  className = "",
}: BulkActionsProps) {
  if (selectedCount === 0) return null;

  return (
    <div
      className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-blue-900">
            {selectedCount} item{selectedCount !== 1 ? "s" : ""} selected
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="text-blue-600 hover:text-blue-800"
          >
            Clear selection
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          {actions.map((action) => (
            <Button
              key={action.key}
              variant={action.variant}
              size="sm"
              onClick={() => onAction(action.key)}
              className="flex items-center space-x-1"
            >
              <Icon name={action.icon} className="w-4 h-4" />
              <span>{action.label}</span>
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
