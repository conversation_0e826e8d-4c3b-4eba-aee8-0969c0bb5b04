import React, { useState } from "react";
import { Icon } from "./Icon";
import { Button } from "./Button";
import { Select } from "./Select";
import { Input } from "./Input";

export type FilterOption = {
  key: string;
  label: string;
  type: "select" | "date" | "text" | "number";
  options?: { value: string; label: string }[];
};

interface FilterPanelProps {
  filters: Record<string, any>;
  options: FilterOption[];
  onFiltersChange: (filters: Record<string, any>) => void;
  className?: string;
}

export function FilterPanel({
  filters,
  options,
  onFiltersChange,
  className = "",
}: FilterPanelProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters };
    if (value === "" || value === null || value === undefined) {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    onFiltersChange({});
  };

  const activeFiltersCount = Object.keys(filters).length;

  return (
    <div className={`relative ${className}`}>
      <Button
        variant="secondary"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2"
      >
        <Icon name="filter" className="w-4 h-4" />
        <span>Filters</span>
        {activeFiltersCount > 0 && (
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
            {activeFiltersCount}
          </span>
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Filters</h3>
              {activeFiltersCount > 0 && (
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  Clear all
                </Button>
              )}
            </div>

            <div className="space-y-4">
              {options.map((option) => {
                const value = filters[option.key] || "";

                switch (option.type) {
                  case "select":
                    return (
                      <div key={option.key}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {option.label}
                        </label>
                        <Select
                          value={value}
                          onChange={(newValue) =>
                            handleFilterChange(option.key, newValue)
                          }
                          options={option.options || []}
                          placeholder={`Select ${option.label.toLowerCase()}`}
                        />
                      </div>
                    );

                  case "date":
                    return (
                      <div key={option.key}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {option.label}
                        </label>
                        <Input
                          type="date"
                          value={value}
                          onChange={(newValue) =>
                            handleFilterChange(option.key, newValue)
                          }
                        />
                      </div>
                    );

                  case "number":
                    return (
                      <div key={option.key}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {option.label}
                        </label>
                        <Input
                          type="number"
                          value={value}
                          onChange={(newValue) =>
                            handleFilterChange(option.key, newValue)
                          }
                        />
                      </div>
                    );

                  default:
                    return (
                      <div key={option.key}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {option.label}
                        </label>
                        <Input
                          value={value}
                          onChange={(newValue) =>
                            handleFilterChange(option.key, newValue)
                          }
                          placeholder={`Enter ${option.label.toLowerCase()}`}
                        />
                      </div>
                    );
                }
              })}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-end space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
