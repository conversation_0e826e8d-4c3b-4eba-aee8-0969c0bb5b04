'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  Skeleton,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  Person as PersonIcon,
  Event as EventIcon,
  Edit as EditIcon,
  Login as LoginIcon,
  Lock as LockIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';

export interface ActivityFeedProps {
  activities: any[];
  loading?: boolean;
  title?: string;
  maxItems?: number;
}

export function ActivityFeed({ activities, loading = false, title = 'Recent Activity', maxItems = 10 }: ActivityFeedProps) {
  const theme = useTheme();

  const getActivityIcon = (type: any) => {
    switch (type) {
      case 'member_joined':
        return <PersonIcon />;
      case 'event_registered':
        return <EventIcon />;
      case 'profile_updated':
        return <EditIcon />;
      case 'login':
        return <LoginIcon />;
      case 'password_changed':
        return <LockIcon />;
      default:
        return <PersonIcon />;
    }
  };

  const getActivityColor = (type: any): 'success' | 'primary' | 'info' | 'warning' | 'error' => {
    switch (type) {
      case 'member_joined':
        return 'success';
      case 'event_registered':
        return 'primary';
      case 'profile_updated':
        return 'info';
      case 'login':
        return 'warning';
      case 'password_changed':
        return 'error';
      default:
        return 'primary';
    }
  };

  const getActivityColorValue = (type: any) => {
    const color = getActivityColor(type);
    return theme.palette[color];
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const displayedActivities = activities.slice(0, maxItems);

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            {title}
          </Typography>
          <IconButton size="small">
            <MoreVertIcon />
          </IconButton>
        </Box>

        {loading ? (
          <List>
            {[...Array(5)].map((_, index) => (
              <ListItem key={index} sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Skeleton variant="circular" width={40} height={40} />
                </ListItemAvatar>
                <ListItemText
                  primary={<Skeleton variant="text" width="60%" />}
                  secondary={<Skeleton variant="text" width="40%" />}
                />
              </ListItem>
            ))}
          </List>
        ) : displayedActivities.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              No recent activity
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {displayedActivities.map((activity) => (
              <ListItem
                key={activity.id}
                sx={{
                  px: 0,
                  py: 1,
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  '&:last-child': {
                    borderBottom: 'none',
                  },
                }}
              >
                <ListItemAvatar>
                  <Avatar
                    sx={{
                      bgcolor: `${getActivityColorValue(activity.type).main}15`,
                      color: getActivityColorValue(activity.type).main,
                    }}
                  >
                    {getActivityIcon(activity.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" component="span">
                        {activity.title}
                      </Typography>
                      <Chip
                        label={activity.type.replace('_', ' ')}
                        size="small"
                        color={getActivityColor(activity.type) as any}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <>
                      <Typography variant="body2" color="text.secondary" component="span" display="block">
                        {activity.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" component="span" display="block">
                        {formatTimestamp(activity.timestamp)}
                      </Typography>
                    </>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
} 