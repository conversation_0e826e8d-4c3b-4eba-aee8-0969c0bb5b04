// 'use client';

// import React from 'react';
// import {
//   Card,
//   CardContent,
//   Typography,
//   Grid,
//   Button,
//   Box,
//   Skeleton,
//   useTheme,
// } from '@mui/material';
// import {
//   PersonAdd as PersonAddIcon,
//   Event as EventIcon,
//   Assessment as AssessmentIcon,
//   Settings as SettingsIcon,
//   AdminPanelSettings as AdminPanelSettingsIcon,
//   Security as SecurityIcon,
// } from '@mui/icons-material';

// import { useRouter } from 'next/navigation';

// export interface QuickActionsProps {
//   actions: any[];
//   loading?: boolean;
//   title?: string;
// }

// const iconMap: Record<string, React.ReactNode> = {
//   PersonAdd: <PersonAddIcon />,
//   Event: <EventIcon />,
//   Assessment: <AssessmentIcon />,
//   Settings: <SettingsIcon />,
//   AdminPanelSettings: <AdminPanelSettingsIcon />,
//   Security: <SecurityIcon />,
// };

// export function QuickActions({ actions, loading = false, title = 'Quick Actions' }: QuickActionsProps) {
//   const router = useRouter();
//   const theme = useTheme();

//   // Mock current user role - in real app, this would come from auth context
//   const currentUserRole = 'super_admin';

//   // Filter actions based on user role
//   const filteredActions = actions.filter(action => {
//     if (!action.role) return true; // Show actions without role restriction
//     return action.role === currentUserRole;
//   });

//   const handleActionClick = (href: string) => {
//     router.push(href);
//   };

//   if (loading) {
//     return (
//       <Card sx={{ height: '100%' }}>
//         <CardContent>
//           <Typography variant="h6" component="h2" gutterBottom>
//             {title}
//           </Typography>
//           <Grid container spacing={2}>
//             {[...Array(4)].map((_, index) => (
//               <Grid item xs={6} key={index}>
//                 <Skeleton variant="rectangular" height={80} />
//               </Grid>
//             ))}
//           </Grid>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <Card sx={{ height: '100%' }}>
//       <CardContent>
//         <Typography variant="h6" component="h2" gutterBottom>
//           {title}
//         </Typography>
        
//         <Grid container spacing={2}>
//           {filteredActions.map((action) => (
//             <Grid item xs={6} key={action.id}>
//               <Button
//                 variant="outlined"
//                 fullWidth
//                 sx={{
//                   height: 80,
//                   flexDirection: 'column',
//                   gap: 1,
//                   borderColor: `${theme.palette[action.color as keyof typeof theme.palette].main}30`,
//                   color: theme.palette[action.color as keyof typeof theme.palette].main,
//                   padding: '12px 8px',
//                   '&:hover': {
//                     borderColor: theme.palette[action.color as keyof typeof theme.palette].main,
//                     backgroundColor: `${theme.palette[action.color as keyof typeof theme.palette].main}08`,
//                   },
//                 }}
//                 onClick={() => handleActionClick(action.href)}
//               >
//                 <Box
//                   sx={{
//                     color: theme.palette[action.color as keyof typeof theme.palette].main,
//                     fontSize: 24,
//                     display: 'flex',
//                     alignItems: 'center',
//                     justifyContent: 'center',
//                     width: '100%',
//                     height: 24,
//                   }}
//                 >
//                   {iconMap[action.icon] || <PersonAddIcon />}
//                 </Box>
//                 <Box sx={{ textAlign: 'center', width: '100%' }}>
//                   <Typography 
//                     variant="body2" 
//                     component="div" 
//                     sx={{ 
//                       fontWeight: 600,
//                       fontSize: '0.75rem',
//                       lineHeight: 1.2,
//                     }}
//                   >
//                     {action.title}
//                   </Typography>
//                   <Typography 
//                     variant="caption" 
//                     color="text.secondary"
//                     sx={{
//                       fontSize: '0.625rem',
//                       lineHeight: 1.2,
//                       display: 'block',
//                       mt: 0.5,
//                     }}
//                   >
//                     {action.description}
//                   </Typography>
//                 </Box>
//               </Button>
//             </Grid>
//           ))}
//         </Grid>
//       </CardContent>
//     </Card>
//   );
// } 