// Recent Logs Component for Dashboard
"use client";

import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Chip,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import {
  Assessment as LogIcon,
  Visibility as ViewIcon,
} from "@mui/icons-material";
import { LogEntry } from "@/types/log";
import Link from "../Link";

interface RecentLogsProps {
  logs: LogEntry[];
  loading?: boolean;
}

const RecentLogs: React.FC<RecentLogsProps> = ({ logs, loading = false }) => {
  // Format timestamp for display
  const formatTimestamp = (dateString: string) => {
    let normalizedDateString = dateString;

    // If it doesn't end with 'Z' or a timezone offset, assume it's UTC and append 'Z'
    if (!/[Z+-]/.test(dateString.slice(-1))) {
      // Remove fractional seconds if present
      normalizedDateString = dateString.split(".")[0] + "Z";
    }

    const utcDate = new Date(normalizedDateString);

    return utcDate.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  // Get action color
  const getActionColor = (action: string) => {
    switch (action) {
      case "member_export":
        return "primary";
      case "log_export":
        return "info";
      case "member_create":
        return "success";
      case "member_update":
        return "warning";
      case "member_delete":
        return "error";
      default:
        return "default";
    }
  };

  // Format action display name
  const formatAction = (action: string) => {
    return action
      .split("_")
      .join(" ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Truncate text with ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "N/A";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader
          title={
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <LogIcon color="primary" />
              <Typography variant="h6">Recent Activity Logs</Typography>
            </Box>
          }
        />
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            Loading recent logs...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title={
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <LogIcon color="primary" />
            <Typography variant="h6">Recent Activity Logs</Typography>
          </Box>
        }
        action={
          <Link href="/logs" sx={{ textDecoration: "none" }}>
            <Chip
              icon={<ViewIcon />}
              label="View All"
              variant="outlined"
              size="small"
              clickable
            />
          </Link>
        }
      />
      <CardContent sx={{ p: 0 }}>
        {logs.length === 0 ? (
          <Box sx={{ p: 3, textAlign: "center" }}>
            <Typography variant="body2" color="text.secondary">
              No recent activity logs
            </Typography>
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ maxHeight: 410 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Time</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>User</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Action</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Purpose</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {logs.slice(0, 10).map((log) => (
                  <TableRow key={log.uuid} hover>
                    <TableCell>
                      <Typography
                        variant="caption"
                        sx={{ fontFamily: "monospace" }}
                      >
                        {formatTimestamp(log.timestamp)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {log.username}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={log.action}
                        size="small"
                        color={getActionColor(log.action) as any}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title={log.purpose || "No purpose"} arrow>
                        <Typography variant="body2">
                          {truncateText(log.purpose || "No purpose", 40)}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentLogs;
