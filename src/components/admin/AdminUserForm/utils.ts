// Utility functions extracted from AdminUserForm.tsx

export const ROLE_COLORS = {
  super_admin: "error",
  admin: "warning",
  moderator: "info",
  default: "default",
} as const;

export const getRoleColor = (role: string) => {
  return ROLE_COLORS[role as keyof typeof ROLE_COLORS] || ROLE_COLORS.default;
};

export const formatRoleDisplay = (role: string): string => {
  return role.replace("_", " ").toUpperCase();
};

export const getPasswordStrength = (
  password: string
): { score: number; feedback: string[] } => {
  let score = 0;
  const feedback: string[] = [];
  if (!password) return { score, feedback };
  if (password.length >= 8) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score++;
  if (password.length < 8)
    feedback.push("Password should be at least 8 characters");
  if (!/[A-Z]/.test(password)) feedback.push("Add an uppercase letter");
  if (!/[a-z]/.test(password)) feedback.push("Add a lowercase letter");
  if (!/[0-9]/.test(password)) feedback.push("Add a number");
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password))
    feedback.push("Add a special character");
  return { score, feedback };
};

import { useMemo } from "react";
export const usePasswordStrength = (password: string) => {
  return useMemo(() => getPasswordStrength(password), [password]);
};
