import React, { useState } from "react";
import {
  Box,
  TextField as Mu<PERSON><PERSON>ex<PERSON><PERSON><PERSON>,
  Typography,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { usePasswordStrength } from "./utils";
import { FormFieldProps } from "./types";

const FormField: React.FC<FormFieldProps> = React.memo(
  ({
    name,
    formik,
    maxLength,
    helpText,
    showPasswordStrength = false,
    isEditMode = false,
    disabled,
    ...props
  }) => {
    const [showPassword, setShowPassword] = useState(false);
    const fieldValue = formik.values[name] || "";
    const fieldError = formik.touched[name] && formik.errors[name];
    const passwordStrength = usePasswordStrength(
      showPasswordStrength ? fieldValue : ""
    );

    // Check if this is a password field
    const isPasswordField = props.type === "password";

    const handleTogglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    const getStrengthColor = (score: number) => {
      if (score <= 2) return "error";
      if (score <= 4) return "warning";
      return "success";
    };

    const getStrengthText = (score: number) => {
      if (score <= 2) return "Weak";
      if (score <= 4) return "Medium";
      return "Strong";
    };

    const getHelperText = () => {
      if (fieldError) return fieldError;
      if (helpText) return helpText;
      return undefined;
    };

    return (
      <Box>
        <MuiTextField
          {...props}
          name={name}
          value={fieldValue}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={Boolean(fieldError)}
          helperText={getHelperText()}
          disabled={disabled}
          type={isPasswordField && showPassword ? "text" : props.type}
          inputProps={{
            maxLength,
            ...props.inputProps,
          }}
          InputProps={{
            ...props.InputProps,
            endAdornment: isPasswordField ? (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleTogglePasswordVisibility}
                  edge="end"
                  disabled={disabled}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ) : (
              props.InputProps?.endAdornment
            ),
          }}
        />
        {showPasswordStrength && fieldValue && fieldValue.length > 0 && (
          <Box sx={{ mt: 1 }}>
            <Typography
              variant="caption"
              color={getStrengthColor(passwordStrength.score)}
            >
              Password Strength: {getStrengthText(passwordStrength.score)}
            </Typography>
            {passwordStrength.feedback.length > 0 && (
              <Typography
                variant="caption"
                display="block"
                color="text.secondary"
              >
                Suggestions: {passwordStrength.feedback.join(", ")}
              </Typography>
            )}
          </Box>
        )}
      </Box>
    );
  }
);

export default FormField;
