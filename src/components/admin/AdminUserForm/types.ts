import { TextFieldProps as MuiTextFieldProps } from "@mui/material";
import { Admin } from "@/types/adminUser";

export type AdminRole = "admin" | "super_admin" | "moderator";

export interface FormValues {
  id?: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  countrycode: string;
  roles: AdminRole[];
  temporaryPassword: string;
  confirmPassword: string;
}

export interface AdminUserFormProps {
  adminUser?: Admin;
  currentUserRole?: string;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

export interface FormFieldProps
  extends Omit<MuiTextFieldProps, "name" | "value" | "onChange" | "onBlur"> {
  name: string;
  formik: any;
  maxLength?: number;
  helpText?: string;
  showPasswordStrength?: boolean;
  isEditMode?: boolean;
}
