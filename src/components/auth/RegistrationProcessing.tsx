import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

interface RegistrationProcessingProps {
  message?: string;
}

export const RegistrationProcessing: React.FC<RegistrationProcessingProps> = ({
  message = "Processing your registration. Please wait..."
}) => {
  return (
    <Box 
      sx={{ 
        width: "100%", 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        gap: 2,
        py: 4
      }}
    >
      <CircularProgress size={40} />
      <Typography variant="body1" color="text.secondary" textAlign="center">
        {message}
      </Typography>
      <Typography variant="caption" color="text.secondary" textAlign="center">
        This may take a few moments...
      </Typography>
    </Box>
  );
};
