import React from 'react';
import { Box, Container, Paper, Typography } from '@mui/material';
import Image from 'next/image';

interface LoginLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
}

export const LoginLayout: React.FC<LoginLayoutProps> = ({ children, title, subtitle }) => {
  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: "0 8px 32px 0 rgba(30,58,138,0.2)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "90vw", sm: 400 },
            maxWidth: 420,
            mx: "auto",
          }}
        >
          <Box sx={{ mb: 3 }}>
            <Image
              src="/CO-gold.svg"
              alt="Logo"
              width={120}
              height={56}
              style={{ objectFit: "contain" }}
            />
          </Box>

          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            align="center"
            sx={{ fontWeight: 700, color: "#1e3a8a" }}
          >
            {title}
          </Typography>

          <Typography variant="subtitle1" align="center" sx={{ mb: 2, color: "#555" }}>
            {subtitle}
          </Typography>

          {children}
        </Paper>
      </Container>
    </Box>
  );
};
