import React, { useState } from "react";
import {
  Box,
  Button,
  TextField,
  Typography,
  Alert,
  CircularProgress,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { IconButton, InputAdornment } from "@mui/material";
import { ChangePasswordWithTokenFormData } from "@/types/auth";
import { AUTH_CONSTANTS } from "@/constants/auth";

interface ChangePasswordFormProps {
  accessToken: string;
  onSubmit: (data: ChangePasswordWithTokenFormData) => void;
  isLoading: boolean;
  error?: string;
}

export const ChangePasswordForm: React.FC<ChangePasswordFormProps> = ({
  accessToken,
  onSubmit,
  isLoading,
  error,
}) => {
  const [formData, setFormData] = useState<
    Omit<ChangePasswordWithTokenFormData, "accessToken">
  >({
    newPassword: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    newPassword?: string;
    confirmPassword?: string;
  }>({});

  const validateForm = (): boolean => {
    const errors: { newPassword?: string; confirmPassword?: string } = {};

    // Password validation
    if (!formData.newPassword) {
      errors.newPassword = "Password is required";
    } else if (formData.newPassword.length < AUTH_CONSTANTS.FORM_VALIDATION.PASSWORD_MIN_LENGTH) {
      errors.newPassword = `Password must be at least ${AUTH_CONSTANTS.FORM_VALIDATION.PASSWORD_MIN_LENGTH} characters long`;
    } else if (!/(?=.*[a-z])/.test(formData.newPassword)) {
      errors.newPassword =
        "Password must contain at least one lowercase letter";
    } else if (!/(?=.*[A-Z])/.test(formData.newPassword)) {
      errors.newPassword =
        "Password must contain at least one uppercase letter";
    } else if (!/(?=.*\d)/.test(formData.newPassword)) {
      errors.newPassword = "Password must contain at least one number";
    } else if (!/(?=.*[@$!%*?&])/.test(formData.newPassword)) {
      errors.newPassword =
        "Password must contain at least one special character (@$!%*?&)";
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = "Please confirm your password";
    } else if (formData.newPassword !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        accessToken,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      });
    }
  };

  const handleInputChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }));
      // Clear validation error when user starts typing
      if (validationErrors[field]) {
        setValidationErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, width: "100%" }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* <Typography variant="h6" sx={{ mb: 2, textAlign: "center" }}>
        Set Your Password
      </Typography> */}

      <Typography
        variant="body2"
        sx={{ mb: 3, textAlign: "center", color: "text.secondary" }}
      >
        Please set a strong password for your account. After setting your
        password, you'll be prompted to set up two-factor authentication.
      </Typography>

      <TextField
        margin="normal"
        required
        fullWidth
        label="New Password"
        type={showPassword ? "text" : "password"}
        value={formData.newPassword}
        onChange={handleInputChange("newPassword")}
        error={!!validationErrors.newPassword}
        helperText={validationErrors.newPassword}
        sx={{ background: "#fafbfc", borderRadius: 1 }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => setShowPassword(!showPassword)}
                edge="end"
              >
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <TextField
        margin="normal"
        required
        fullWidth
        label="Confirm Password"
        type={showConfirmPassword ? "text" : "password"}
        value={formData.confirmPassword}
        onChange={handleInputChange("confirmPassword")}
        error={!!validationErrors.confirmPassword}
        helperText={validationErrors.confirmPassword}
        sx={{ background: "#fafbfc", borderRadius: 1 }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                edge="end"
              >
                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isLoading}
        sx={{
          mt: 3,
          mb: 2,
          fontWeight: 700,
          background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
          color: "#fff",
          "&:hover": {
            opacity: 0.95,
          },
        }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          "Set Password"
        )}
      </Button>
    </Box>
  );
};
