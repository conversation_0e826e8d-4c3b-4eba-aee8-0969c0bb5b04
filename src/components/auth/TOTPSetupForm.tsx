import React from 'react';
import { <PERSON>, But<PERSON>, <PERSON>Field, Typography } from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';
import { MFAFormData } from '@/types/auth';
import { AUTH_CONSTANTS } from '@/constants/auth';

interface TOTPSetupFormProps {
  sharedSecret: string;
  username: string;
  mfaData: MFAFormData;
  setMfaData: (data: MFAFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
}

export const TOTPSetupForm: React.FC<TOTPSetupFormProps> = ({
  sharedSecret,
  username,
  mfaData,
  setMfaData,
  onSubmit,
  isLoading,
}) => {
  return (
    <Box sx={{ mt: 2, width: "100%", textAlign: "center" }}>
      <Typography variant="body1" sx={{ mb: 2 }}>
        {AUTH_CONSTANTS.MESSAGES.TOTP_SETUP_INSTRUCTION}
      </Typography>

      <Box sx={{ mb: 3, display: "flex", flexDirection: "column", alignItems: "center" }}>
        <QRCodeSVG
          value={`otpauth://totp/${AUTH_CONSTANTS.QR_CODE.ISSUER}:${username}?secret=${sharedSecret}&issuer=${AUTH_CONSTANTS.QR_CODE.ISSUER}`}
          size={AUTH_CONSTANTS.QR_CODE.SIZE}
          style={{ border: "1px solid #ddd", padding: "10px", borderRadius: "8px" }}
        />
        <Typography variant="caption" sx={{ mt: 2, color: "#666" }}>
          {AUTH_CONSTANTS.MESSAGES.TOTP_MANUAL_ENTRY} 
        </Typography>
        <Typography variant="caption" sx={{ mt: 2, color: "#666", wordBreak: "break-all" }}>
          {sharedSecret}
        </Typography>
      </Box>

      <Box component="form" onSubmit={onSubmit} sx={{ width: "100%" }}>
        <TextField
          margin="normal"
          required
          fullWidth
          label="Enter 6-digit code from your app"
          placeholder="123456"
          value={mfaData.code}
          onChange={(e) => setMfaData({ ...mfaData, code: e.target.value })}
          sx={{ background: "#fafbfc", borderRadius: 1 }}
          inputProps={{ 
            maxLength: AUTH_CONSTANTS.FORM_VALIDATION.MFA_CODE_LENGTH, 
            pattern: AUTH_CONSTANTS.FORM_VALIDATION.MFA_CODE_PATTERN 
          }}
        />
        <Button
          type="submit"
          fullWidth
          variant="contained"
          disabled={mfaData.code.length !== AUTH_CONSTANTS.FORM_VALIDATION.MFA_CODE_LENGTH}
          sx={{
            mt: 3,
            mb: 2,
            fontWeight: 700,
            background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
            color: "#fff",
            "&:hover": {
              opacity: 0.95,
            },
          }}
        >
          {isLoading ? "Setting up..." : "Complete Setup"}
        </Button>
      </Box>
    </Box>
  );
};
