import React from "react";
import { <PERSON>, Button, TextField, Typo<PERSON>, Alert } from "@mui/material";
import { ForgotPasswordFormData } from "@/types/auth";

interface ForgotPasswordFormProps {
  formData: ForgotPasswordFormData;
  setFormData: (data: ForgotPasswordFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  error: string | null;
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  formData,
  setFormData,
  onSubmit,
  isLoading,
  error,
}) => {
  return (
    <Box sx={{ width: "100%" }}>
      <Typography
        variant="h5"
        component="h2"
        gutterBottom
        align="center"
        sx={{ fontWeight: 600, color: "#1e3a8a", mb: 2 }}
      >
        Reset Password
      </Typography>
      <Typography variant="body2" align="center" sx={{ mb: 3, color: "#666" }}>
        Enter your email address and we'll send you a verification code to reset
        your password.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={onSubmit} sx={{ width: "100%" }}>
        <TextField
          margin="normal"
          required
          fullWidth
          label="Email Address"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          sx={{ background: "#fafbfc", borderRadius: 1 }}
          disabled={isLoading}
        />
        <Button
          type="submit"
          fullWidth
          variant="contained"
          disabled={isLoading || !formData.email.trim()}
          sx={{
            mt: 3,
            mb: 2,
            fontWeight: 700,
            background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
            color: "#fff",
            "&:hover": {
              opacity: 0.95,
            },
            "&:disabled": {
              opacity: 0.6,
            },
          }}
        >
          {isLoading ? "Sending Code..." : "Send Verification Code"}
        </Button>
      </Box>
    </Box>
  );
};
