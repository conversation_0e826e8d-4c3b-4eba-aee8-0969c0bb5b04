import React, { useState } from "react";
import {
  Box,
  Button,
  TextField,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { ResetPasswordFormData } from "@/types/auth";

interface ResetPasswordFormProps {
  formData: ResetPasswordFormData;
  setFormData: (data: ResetPasswordFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  onBack: () => void;
  isLoading: boolean;
  error: string | null;
  codeDeliveryDetails?: {
    destination: string;
    deliveryMedium: string;
  };
}

export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  formData,
  setFormData,
  onSubmit,
  onBack,
  isLoading,
  error,
  codeDeliveryDetails,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleClickShowConfirmPassword = () =>
    setShowConfirmPassword(!showConfirmPassword);

  const isFormValid =
    formData.code.trim() !== "" &&
    formData.newPassword.length >= 8 &&
    formData.confirmPassword === formData.newPassword;

  return (
    <Box sx={{ width: "100%" }}>
      <Typography
        variant="h5"
        component="h2"
        gutterBottom
        align="center"
        sx={{ fontWeight: 600, color: "#1e3a8a", mb: 2 }}
      >
        Enter Verification Code
      </Typography>

      {codeDeliveryDetails && (
        <Typography
          variant="body2"
          align="center"
          sx={{ mb: 3, color: "#666" }}
        >
          We've sent a 6-digit verification code to{" "}
          {codeDeliveryDetails.destination}
        </Typography>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box component="form" onSubmit={onSubmit} sx={{ width: "100%" }}>
        <TextField
          margin="normal"
          required
          fullWidth
          label="Verification Code"
          value={formData.code}
          onChange={(e) => {
            // Only allow numeric input and limit to 6 digits
            const value = e.target.value.replace(/\D/g, "").slice(0, 6);
            setFormData({ ...formData, code: value });
          }}
          sx={{ background: "#fafbfc", borderRadius: 1 }}
          disabled={isLoading}
          inputProps={{ maxLength: 6, pattern: "[0-9]{6}" }}
          helperText="Enter the 6-digit code sent to your email"
        />

        <TextField
          margin="normal"
          required
          fullWidth
          label="New Password"
          type={showPassword ? "text" : "password"}
          value={formData.newPassword}
          onChange={(e) =>
            setFormData({ ...formData, newPassword: e.target.value })
          }
          sx={{ background: "#fafbfc", borderRadius: 1 }}
          disabled={isLoading}
          helperText="Password must be at least 8 characters"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                  edge="end"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <TextField
          margin="normal"
          required
          fullWidth
          label="Confirm New Password"
          type={showConfirmPassword ? "text" : "password"}
          value={formData.confirmPassword}
          onChange={(e) =>
            setFormData({ ...formData, confirmPassword: e.target.value })
          }
          sx={{ background: "#fafbfc", borderRadius: 1 }}
          disabled={isLoading}
          error={
            formData.confirmPassword !== "" &&
            formData.confirmPassword !== formData.newPassword
          }
          helperText={
            formData.confirmPassword !== "" &&
            formData.confirmPassword !== formData.newPassword
              ? "Passwords do not match"
              : ""
          }
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowConfirmPassword}
                  edge="end"
                >
                  {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Box sx={{ display: "flex", gap: 2, mt: 3, mb: 2 }}>
          <Button
            variant="outlined"
            onClick={onBack}
            disabled={isLoading}
            sx={{
              flex: 1,
              fontWeight: 600,
              borderColor: "#1e3a8a",
              color: "#1e3a8a",
              "&:hover": {
                borderColor: "#1e3a8a",
                backgroundColor: "rgba(30, 58, 138, 0.04)",
              },
            }}
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isLoading || !isFormValid}
            sx={{
              flex: 2,
              fontWeight: 700,
              background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
              color: "#fff",
              "&:hover": {
                opacity: 0.95,
              },
              "&:disabled": {
                opacity: 0.6,
              },
            }}
          >
            {isLoading ? "Resetting Password..." : "Reset Password"}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
