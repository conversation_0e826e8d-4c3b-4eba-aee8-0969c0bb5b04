import React from "react";
import { <PERSON>, <PERSON><PERSON>, TextField } from "@mui/material";
import { LoginFormData } from "@/types/auth";
import { navigate } from "@/utils/routerService";

interface LoginFormProps {
  loginData: LoginFormData;
  setLoginData: (data: LoginFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  loginData,
  setLoginData,
  onSubmit,
  isLoading,
}) => {
  return (
    <Box component="form" onSubmit={onSubmit} sx={{ mt: 2, width: "100%" }}>
      <TextField
        margin="normal"
        required
        fullWidth
        label="Username or Email"
        value={loginData.username}
        onChange={(e) =>
          setLoginData({ ...loginData, username: e.target.value })
        }
        sx={{ background: "#fafbfc", borderRadius: 1 }}
      />
      <TextField
        margin="normal"
        required
        fullWidth
        type="password"
        label="Password"
        value={loginData.password}
        onChange={(e) =>
          setLoginData({ ...loginData, password: e.target.value })
        }
        sx={{ background: "#fafbfc", borderRadius: 1 }}
      />
      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isLoading}
        sx={{
          mt: 3,
          mb: 2,
          fontWeight: 700,
          background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
          color: "#fff",
          "&:hover": {
            opacity: 0.95,
          },
        }}
      >
        {isLoading ? "Signing in..." : "Sign in"}
      </Button>
           {/* Forgot Password Link */}
           <Box sx={{ textAlign: "center", mb: 2 }}>
              <Button
                variant="text"
                onClick={() => navigate("/forgot-password")}
                sx={{
                  color: "#1e3a8a",
                  textTransform: "none",
                  fontWeight: 500,
                  "&:hover": {
                    backgroundColor: "rgba(30, 58, 138, 0.04)",
                  },
                }}
              >
                Forgot your password?
              </Button>
            </Box>

    </Box>
  );
};
