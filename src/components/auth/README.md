# Login Page Refactoring

This document describes the refactored login page structure and architecture.

## Overview

The login page has been refactored from a single monolithic component into a modular, maintainable structure with separate components, hooks, and utilities.

## Architecture

### Components (`/src/components/auth/`)

1. **LoginLayout** - Main layout wrapper for auth pages
2. **LoginForm** - Standard username/password login form
3. **MFAForm** - Multi-factor authentication code entry form
4. **TOTPSetupForm** - TOTP setup with QR code display
5. **ErrorBoundary** - Error handling wrapper component

### Hooks (`/src/hooks/`)

1. **useAuth** - Core authentication logic and API calls
2. **useLoginState** - State management for login flow
3. **useAuthHandler** - Authentication flow handler (if needed)

### Utilities (`/src/utils/`)

1. **AuthFlowManager** - Manages authentication flow logic
2. **authFlow** - Constants for authentication steps

### Constants (`/src/constants/`)

1. **auth** - Authentication-related constants and configuration

## Key Features

### Modular Architecture
- Each component has a single responsibility
- Reusable components for different auth flows
- Centralized state management

### Error Handling
- ErrorBoundary component for graceful error handling
- Consistent error states across components
- User-friendly error messages

### Type Safety
- Full TypeScript support
- Proper interfaces for all component props
- Type-safe authentication flow

### Accessibility
- Proper form labels and ARIA attributes
- Keyboard navigation support
- Screen reader friendly

## Usage

### Basic Login
```tsx
<LoginForm
  loginData={loginData}
  setLoginData={setLoginData}
  onSubmit={handleLogin}
  isLoading={isLoading}
/>
```

### MFA Verification
```tsx
<MFAForm
  mfaData={mfaData}
  setMfaData={setMfaData}
  onSubmit={handleConfirmMFA}
  isLoading={isLoading}
/>
```

### TOTP Setup
```tsx
<TOTPSetupForm
  sharedSecret={sharedSecret}
  username={username}
  mfaData={mfaData}
  setMfaData={setMfaData}
  onSubmit={handleTotpSetup}
  isLoading={isLoading}
/>
```

## Benefits

1. **Maintainability** - Smaller, focused components are easier to maintain
2. **Testability** - Individual components and hooks can be tested in isolation
3. **Reusability** - Components can be reused in other parts of the application
4. **Scalability** - Easy to add new authentication methods or modify existing ones
5. **Developer Experience** - Clear separation of concerns and better code organization

## Configuration

Authentication constants can be configured in `/src/constants/auth.ts`:

```typescript
export const AUTH_CONSTANTS = {
  STORAGE_KEYS: {
    PENDING_TOTP_SETUP: 'pendingTOTPSetup',
  },
  ROUTES: {
    DASHBOARD: '/dashboard',
    LOGIN: '/login',
  },
  // ... other constants
};
```

## Testing

Each component and hook should be tested individually:

```typescript
// Example test for LoginForm
import { render, fireEvent } from '@testing-library/react';
import { LoginForm } from '../LoginForm';

test('should submit login form with correct data', () => {
  const mockSubmit = jest.fn();
  const { getByLabelText, getByRole } = render(
    <LoginForm
      loginData={{ username: '', password: '' }}
      setLoginData={jest.fn()}
      onSubmit={mockSubmit}
      isLoading={false}
    />
  );
  
  // Test implementation
});
```

## Migration Guide

When migrating from the old login page:

1. Replace direct state management with `useLoginState` hook
2. Use individual form components instead of inline forms
3. Implement error handling with ErrorBoundary
4. Update imports to use the new component structure
5. Update any hardcoded strings to use constants

## Future Enhancements

- Add support for OAuth providers
- Implement progressive enhancement for JavaScript-disabled users
- Add analytics tracking for authentication events
- Support for custom branding and theming
- Add remember password functionality
