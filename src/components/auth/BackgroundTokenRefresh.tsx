'use client';

import { useBackgroundTokenRefresh } from '@/hooks/useBackgroundTokenRefresh';
import { useUserInactivityLogout } from '@/hooks/useUserInactivityLogout';

interface BackgroundTokenRefreshProps {
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)
  inactivityTimeout?: number; // Auto logout after X minutes of inactivity (default: 30 minutes)
  enableInactivityLogout?: boolean; // Enable/disable inactivity logout (default: true)
}

/**
 * Component that silently refreshes tokens in the background and handles user inactivity logout
 * Add this to your app layout to enable automatic token refresh and inactivity logout
 */
export const BackgroundTokenRefresh: React.FC<BackgroundTokenRefreshProps> = ({
  checkInterval = 30000, // 30 seconds (fixed the typo from 250000)
  refreshBuffer = 5, // 5 minutes
  inactivityTimeout = 30, // 30 minutes
  enableInactivityLogout = true
}) => {
  useBackgroundTokenRefresh({
    checkInterval,
    refreshBuffer
  });

  useUserInactivityLogout({
    timeout: inactivityTimeout,
    enabled: enableInactivityLogout
  });

  // This component doesn't render anything - it just runs the background refresh logic
  return null;
};
