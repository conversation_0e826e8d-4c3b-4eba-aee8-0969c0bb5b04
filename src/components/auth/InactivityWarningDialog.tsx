'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  Warning as WarningIcon,
  Timer as TimerIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface InactivityWarningDialogProps {
  open: boolean;
  timeLeft: number; // Time left in minutes
  onStayLoggedIn: () => void;
  onLogoutNow?: () => void;
  autoCloseTime?: number; // Auto close and logout after X seconds (default: 60)
}

/**
 * Dialog component that warns users about impending automatic logout due to inactivity
 */
export const InactivityWarningDialog: React.FC<InactivityWarningDialogProps> = ({
  open,
  timeLeft,
  onStayLoggedIn,
  onLogoutNow,
  autoCloseTime = 60 // 60 seconds
}) => {
  const [countdown, setCountdown] = useState(autoCloseTime);
  const [progress, setProgress] = useState(100);

  // Reset countdown when dialog opens
  useEffect(() => {
    if (open) {
      setCountdown(autoCloseTime);
      setProgress(100);
    }
  }, [open, autoCloseTime]);

  // Countdown timer
  useEffect(() => {
    if (!open) return;

    const interval = setInterval(() => {
      setCountdown(prev => {
        const newCountdown = prev - 1;
        const newProgress = (newCountdown / autoCloseTime) * 100;
        setProgress(Math.max(0, newProgress));

        // Auto logout when countdown reaches 0
        if (newCountdown <= 0) {
          if (onLogoutNow) {
            onLogoutNow();
          }
          return 0;
        }

        return newCountdown;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [open, autoCloseTime, onLogoutNow]);

  const handleStayLoggedIn = () => {
    setCountdown(autoCloseTime);
    setProgress(100);
    onStayLoggedIn();
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressColor = (): 'primary' | 'warning' | 'error' => {
    if (countdown > 30) return 'primary';
    if (countdown > 10) return 'warning';
    return 'error';
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon color="warning" />
          <Typography variant="h6" component="span">
            Session Timeout Warning
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pb: 2 }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          You will be automatically logged out due to inactivity.
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            You have been inactive for a while. For security reasons, you will be automatically 
            logged out in <strong>{timeLeft} minute{timeLeft !== 1 ? 's' : ''}</strong>.
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimerIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                Auto logout in:
              </Typography>
            </Box>
            <Chip
              label={formatTime(countdown)}
              color={getProgressColor()}
              variant="outlined"
              size="small"
            />
          </Box>
          <LinearProgress
            variant="determinate"
            value={progress}
            color={getProgressColor()}
            sx={{ height: 6, borderRadius: 3 }}
          />
        </Box>

        <Typography variant="body2" color="text.secondary">
          Click "Stay Logged In" to continue your session, or you will be automatically 
          logged out when the timer reaches zero.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {onLogoutNow && (
          <Button
            onClick={onLogoutNow}
            color="error"
            variant="outlined"
          >
            Logout Now
          </Button>
        )}
        <Button
          onClick={handleStayLoggedIn}
          color="primary"
          variant="contained"
          startIcon={<RefreshIcon />}
          autoFocus
        >
          Stay Logged In
        </Button>
      </DialogActions>
    </Dialog>
  );
};
