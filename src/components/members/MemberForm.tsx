/* eslint-disable @typescript-eslint/no-unused-expressions */
"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  Autocomplete,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  CircularProgress,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
} from "@mui/icons-material";
import { FormikProps } from "formik";
import { membersService } from "@/services/members";
import { getOrganizationsForMember } from "@/services/organizationService";
import { useParams } from "next/navigation";
import { useDebounce } from "@/utils/debounce";

interface MemberFormProps {
  formik: FormikProps<any>;
  showBasicFields?: boolean;
  showOrganizationFields?: boolean;
  showMembershipFields?: boolean;
  showOrganizationLinking?: boolean;
  selectedOrganizations?: any[];
  onOrganizationSelectionChange?: (organizations: any[]) => void;
}

const MemberForm: React.FC<MemberFormProps> = ({
  formik,
  showBasicFields = true,
  showOrganizationFields = true,
  showMembershipFields = true,
  showOrganizationLinking = false,
  selectedOrganizations = [],
  onOrganizationSelectionChange,
}) => {
  // Organization linking state
  const [organizationMode, setOrganizationMode] = useState<
    "link" | "create" | "skip"
  >("skip");
  const params = useParams();
  const [allOrganizations, setAllOrganizations] = useState<any[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchOrg, setSearchOrg] = useState<string | null>(null);
  const [loadingOrganizations, setLoadingOrganizations] = useState(true);

  // Add ref to track if organizations have been fetched
  const hasFetchedOrganizations = useRef(false);

  // Fetch all organizations for autocomplete when organization linking is enabled
  useEffect(() => {
    if (showOrganizationLinking) {
      console.log("🔍 DEBUG MemberForm: showOrganizationLinking is true");
      console.log(
        "🔍 DEBUG MemberForm: selectedOrganizations length:",
        selectedOrganizations?.length
      );
      console.log(
        "🔍 DEBUG MemberForm: hasFetchedOrganizations.current:",
        hasFetchedOrganizations.current
      );

      fetchAllOrganizations(searchOrg);
      // Only fetch member organizations if we don't already have selected organizations
      // and we haven't fetched them before
      if (
        (!selectedOrganizations || selectedOrganizations.length === 0) &&
        !hasFetchedOrganizations.current
      ) {
        console.log("🔍 DEBUG MemberForm: Fetching member organizations");
        fetchMemberOrganizations();
      } else {
        console.log(
          "🔍 DEBUG MemberForm: Skipping member organizations fetch - already have data or already fetched"
        );
        setLoadingOrganizations(false);
      }
    }
  }, [showOrganizationLinking]); // Removed selectedOrganizations from dependency array


  useEffect(() => {
    if (selectedOrganizations && selectedOrganizations.length > 0) {
      setOrganizationMode("link");
    }
  }, [selectedOrganizations]);

  useEffect(() => {
    if (selectedOrganizations && selectedOrganizations.length > 0) {
      setLoadingOrganizations(false);
    }
  }, [selectedOrganizations]);

  const fetchMemberOrganizations = async () => {
    console.log("🔍 DEBUG MemberForm: fetchMemberOrganizations called");
    try {
      setLoading(true);
      if (params?.id) {
        console.log(
          "🔍 DEBUG MemberForm: Fetching organizations for member ID:",
          params.id
        );
        const orgs = await getOrganizationsForMember(params.id as string);
        console.log("🔍 DEBUG MemberForm: Fetched organizations:", orgs);
        // Only update if we don't already have selected organizations
        if (!selectedOrganizations || selectedOrganizations.length === 0) {
          console.log(
            "🔍 DEBUG MemberForm: Calling onOrganizationSelectionChange with:",
            orgs
          );
          onOrganizationSelectionChange?.(orgs);
        } else {
          console.log(
            "🔍 DEBUG MemberForm: Skipping onOrganizationSelectionChange - already have organizations"
          );
        }
        // Mark as fetched to prevent future calls
        hasFetchedOrganizations.current = true;
        console.log("🔍 DEBUG MemberForm: Marked as fetched");
      }
      setLoadingOrganizations(false);
    } catch (err) {
      console.log("🔍 DEBUG MemberForm: Error fetching organizations:", err);
      setLoadingOrganizations(false);
      setError("Failed to fetch organizations for member");
    } finally {
      setLoading(false);
    }
  };
  const fetchAllOrganizations = async (value: string | null) => {
    try {
      setLoading(true);
      const orgs = await membersService.getOrganizations(value);
      setAllOrganizations(orgs.organizations);
    } catch (err) {
      setError("Failed to load organizations");
    } finally {
      setLoading(false);
    }
  };

  const debouncedFetchAllOrganizations = useDebounce(searchOrg, 500);

  useEffect(() => {
    if (debouncedFetchAllOrganizations) {
      fetchAllOrganizations(debouncedFetchAllOrganizations);
    }
  }, [debouncedFetchAllOrganizations]);

  const handleAddOrganization = () => {
    if (
      !selectedOrg ||
      selectedOrganizations.some((org) => org.uuid === selectedOrg.uuid)
    ) {
      return;
    }

    const updatedOrganizations = [...selectedOrganizations, selectedOrg];
    onOrganizationSelectionChange?.(updatedOrganizations);
    setSelectedOrg(null);
  };

  const handleRemoveOrganization = (orgUuid: string) => {
    const updatedOrganizations = selectedOrganizations.filter(
      (org) => org.uuid !== orgUuid
    );
    onOrganizationSelectionChange?.(updatedOrganizations);
  };

  return (
    <Box>
      {showBasicFields && (
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="First Name"
              name="firstName"
              value={formik.values.firstName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.firstName && Boolean(formik.errors.firstName)
              }
              helperText={
                formik.touched.firstName && (formik.errors.firstName as string)
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Last Name"
              name="lastName"
              value={formik.values.lastName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.lastName && Boolean(formik.errors.lastName)}
              helperText={
                formik.touched.lastName && (formik.errors.lastName as string)
              }
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              name="loginEmail"
              type="email"
              value={formik.values.loginEmail}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.loginEmail && Boolean(formik.errors.loginEmail)
              }
              helperText={
                formik.touched.loginEmail &&
                (formik.errors.loginEmail as string)
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Phone"
              name="phone"
              value={formik.values.phone}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.phone && Boolean(formik.errors.phone)}
              helperText={
                formik.touched.phone && (formik.errors.phone as string)
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Professional Title"
              name="professionalTitle"
              value={formik.values.professionalTitle}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.professionalTitle &&
                Boolean(formik.errors.professionalTitle)
              }
              helperText={
                formik.touched.professionalTitle &&
                (formik.errors.professionalTitle as string)
              }
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Personal/Business email"
              name="personalBusinessEmail"
              type="email"
              value={formik.values.personalBusinessEmail}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.personalBusinessEmail &&
                Boolean(formik.errors.personalBusinessEmail)
              }
              helperText={
                formik.touched.personalBusinessEmail &&
                (formik.errors.personalBusinessEmail as string)
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={
                formik.touched.password && (formik.errors.password as string)
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.confirmPassword &&
                Boolean(formik.errors.confirmPassword)
              }
              helperText={
                formik.touched.confirmPassword &&
                (formik.errors.confirmPassword as string)
              }
            />
          </Grid>
        </Grid>
      )}

      {showOrganizationFields && (
        <Box sx={{ mt: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Identity Type*</InputLabel>
                <Select
                  name="identityType"
                  value={formik.values.identityType}
                  label="Identity Type"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.identityType &&
                    Boolean(formik.errors.identityType)
                  }
                >
                  <MenuItem value="individual">Individual</MenuItem>
                  <MenuItem value="business">Business</MenuItem>
                  {/* <MenuItem value="organization">Organization</MenuItem> */}
                </Select>
                {formik.touched.identityType && formik.errors.identityType && (
                  <FormHelperText error>
                    {formik.errors.identityType as string}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            {loadingOrganizations && (
              <Grid
                item
                xs={12}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <CircularProgress />
              </Grid>
            )}

            {showOrganizationLinking && !loadingOrganizations && (
              <Grid item xs={12}>
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Organization Association
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 3 }}
                    >
                      Associate this member with existing organizations or
                      create a new one.
                    </Typography>

                    <FormControl component="fieldset" sx={{ mb: 3 }}>
                      <FormLabel component="legend">
                        Choose an option:
                      </FormLabel>
                      <RadioGroup
                        value={organizationMode}
                        onChange={(e) =>
                          setOrganizationMode(
                            e.target.value as "link" | "create" | "skip"
                          )
                        }
                        row
                      >
                        <FormControlLabel
                          value="link"
                          control={<Radio />}
                          label="Link Existing Organizations"
                        />
                        {formik.values.identityType === "organization" && (
                          <FormControlLabel
                            value="create"
                            control={<Radio />}
                            label="Create New Organization"
                          />
                        )}
                        <FormControlLabel
                          value="skip"
                          control={<Radio />}
                          label="Skip Organization Setup"
                        />
                      </RadioGroup>
                    </FormControl>

                    {error && (
                      <Alert severity="error" sx={{ mb: 2 }}>
                        {error}
                      </Alert>
                    )}

                    {organizationMode === "link" && (
                      <Box>
                        <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                          <Autocomplete
                            sx={{ flex: 1 }}
                            options={allOrganizations}
                            getOptionLabel={(option) =>
                              `${option.name}${
                                option.city ? `, ${option.city}` : ""
                              }`
                            }
                            value={selectedOrg}
                            onChange={(_, value) => setSelectedOrg(value)}
                            loading={loading}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                onChange={(e) => {
                                  setSearchOrg(e.target.value);
                                }}
                                label="Search Organizations"
                                placeholder="Type to search organizations..."
                              />
                            )}
                          />
                          <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleAddOrganization}
                            disabled={!selectedOrg || loading}
                          >
                            Add
                          </Button>
                        </Box>

                        {selectedOrganizations.length > 0 && (
                          <Box>
                            <Typography variant="subtitle2" gutterBottom>
                              Selected Organizations (
                              {selectedOrganizations.length})
                            </Typography>
                            <List dense>
                              {selectedOrganizations.map((org) => (
                                <ListItem key={org.uuid} sx={{ pl: 0 }}>
                                  <BusinessIcon
                                    sx={{ mr: 1, color: "text.secondary" }}
                                  />
                                  <ListItemText
                                    primary={org.name}
                                    secondary={
                                      org.city && org.state
                                        ? `${org.city}, ${org.state}`
                                        : org.industry
                                    }
                                  />
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      edge="end"
                                      color="error"
                                      onClick={() =>
                                        handleRemoveOrganization(org.uuid)
                                      }
                                      size="small"
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            )}

            {organizationMode === "create" &&
              formik.values.identityType === "organization" && (
                <>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Organization Name"
                      name="organization.name"
                      value={formik.values.organization?.name || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        (formik.touched.organization as any)?.name &&
                        Boolean((formik.errors.organization as any)?.name)
                      }
                      helperText={
                        (formik.touched.organization as any)?.name &&
                        ((formik.errors.organization as any)?.name as string)
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Address Line 1"
                      name="organization.address1"
                      value={formik.values.organization?.address1 || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Address Line 2"
                      name="organization.address2"
                      value={formik.values.organization?.address2 || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="City"
                      name="organization.city"
                      value={formik.values.organization?.city || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="State"
                      name="organization.state"
                      value={formik.values.organization?.state || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="ZIP Code"
                      name="organization.zip"
                      value={formik.values.organization?.zip || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Organization Phone"
                      name="organization.phone"
                      value={formik.values.organization?.phone || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Organization email"
                      name="organization.email"
                      type="email"
                      value={formik.values.organization?.email || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Annual Revenue"
                      name="organization.annualRevenue"
                      value={formik.values.organization?.annualRevenue || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Industry"
                      name="organization.industry"
                      value={formik.values.organization?.industry || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Year Founded"
                      name="organization.yearFounded"
                      value={formik.values.organization?.yearFounded || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Company Size"
                      name="organization.companySize"
                      value={formik.values.organization?.companySize || ""}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </Grid>
                </>
              )}
          </Grid>
        </Box>
      )}

      {showMembershipFields && (
        <Box sx={{ mt: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Membership Tier</InputLabel>
                <Select
                  name="membershipTier"
                  value={formik.values.membershipTier}
                  label="Membership Tier"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.membershipTier &&
                    Boolean(formik.errors.membershipTier)
                  }
                >
                  <MenuItem value="lite">Lite</MenuItem>
                  <MenuItem value="premium">Premium</MenuItem>
                </Select>
                {formik.touched.membershipTier &&
                  formik.errors.membershipTier && (
                    <FormHelperText error>
                      {formik.errors.membershipTier as string}
                    </FormHelperText>
                  )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Community Status</InputLabel>
                <Select
                  name="communityStatus"
                  value={formik.values.communityStatus}
                  label="Community Status"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.communityStatus &&
                    Boolean(formik.errors.communityStatus)
                  }
                >
                  <MenuItem value="unverified">Unverified</MenuItem>
                  <MenuItem value="verified">Verified</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
                {formik.touched.communityStatus &&
                  formik.errors.communityStatus && (
                    <FormHelperText error>
                      {formik.errors.communityStatus as string}
                    </FormHelperText>
                  )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Verification Status</InputLabel>
                <Select
                  name="verificationStatus"
                  value={formik.values.verificationStatus || ""}
                  label="Verification Status"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.verificationStatus &&
                    Boolean(formik.errors.verificationStatus)
                  }
                >
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="verified">Verified</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                  <MenuItem value="under_review">Under Review</MenuItem>
                </Select>
                {formik.touched.verificationStatus &&
                  formik.errors.verificationStatus && (
                    <FormHelperText error>
                      {formik.errors.verificationStatus as string}
                    </FormHelperText>
                  )}
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default MemberForm;
