"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  Grid,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import {
  Security as SecurityIcon,
  Email as EmailIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Block as BlockIcon,
  History as HistoryIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
} from "@mui/icons-material";
import { MemberWithRelations } from "@/types/member";
import { membersService } from "@/services/members";

interface MemberSecurityTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberSecurityTab: React.FC<MemberSecurityTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [suspendReason, setSuspendReason] = useState("");
  const [suspendDuration, setSuspendDuration] = useState("7");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSuspendAccount = async () => {
    try {
      setLoading(true);
      setError(null);
      // TODO: Implement account suspension API call
      console.log(
        "Suspending account for member:",
        member.uuid,
        "Reason:",
        suspendReason,
        "Duration:",
        suspendDuration
      );
      setSuspendDialogOpen(false);
      setSuspendReason("");
      setSuspendDuration("7");
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || "Failed to suspend account");
    } finally {
      setLoading(false);
    }
  };

  const handleReactivateAccount = async () => {
    try {
      setLoading(true);
      // TODO: Implement account reactivation API call
      console.log("Reactivating account for member:", member.uuid);
      onMemberUpdate();
    } catch (err: any) {
      setError("Failed to reactivate account");
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    try {
      setLoading(true);
      // TODO: Implement password reset API call
      console.log("Resetting password for member:", member.uuid);
      onMemberUpdate();
    } catch (err: any) {
      setError("Failed to reset password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Auth0 Integration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Auth0 Integration
              </Typography>
              <List dense>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText primary="Auth0 ID" secondary={member.auth0Id} />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="email Verification"
                    secondary={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        {member.loginEmailVerified
                          ? "Verified"
                          : "Not Verified"}
                        {member.loginEmailVerified ? (
                          <CheckIcon
                            sx={{ fontSize: "1rem", color: "success.main" }}
                          />
                        ) : (
                          <WarningIcon
                            sx={{ fontSize: "1rem", color: "warning.main" }}
                          />
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <HistoryIcon />
                  </ListItemIcon>
                  {/* <ListItemText
                    primary="Last Login"
                    secondary={member.la ? formatDate(member.lastLoginDate) : 'Never'}
                  /> */}
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <VisibilityIcon />
                  </ListItemIcon>
                  {/* <ListItemText
                    primary="Account Status"
                    secondary={
                      <Chip
                        label={member.communityStatus === 'suspended' ? 'Suspended' : 'Active'}
                        color={member.communityStatus === 'suspended' ? 'error' : 'success'}
                        size="small"
                      />
                    }
                  /> */}
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Alerts */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Security Alerts
              </Typography>

              {member.blacklistReport?.isSpam && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Spam Activity Detected
                  </Typography>
                  <Typography variant="body2">
                    This account has been flagged for potential spam activity.
                  </Typography>
                </Alert>
              )}

              {!member.loginEmailVerified && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    email Not Verified
                  </Typography>
                  <Typography variant="body2">
                    The member's email address has not been verified.
                  </Typography>
                </Alert>
              )}

              {/* {member.communityStatus === 'suspended' && (
                <Alert severity="error">
                  <Typography variant="subtitle2" gutterBottom>
                    Account Suspended
                  </Typography>
                  <Typography variant="body2">
                    This account has been suspended due to policy violations.
                  </Typography>
                </Alert>
              )} */}

              {/* {!member.blacklistReport?.isSpam && member.loginEmailVerified && member.communityStatus !== 'suspended' && (
                <Alert severity="success">
                  <Typography variant="subtitle2" gutterBottom>
                    No Security Issues
                  </Typography>
                  <Typography variant="body2">
                    No security alerts detected for this account.
                  </Typography>
                </Alert>
              )} */}
            </CardContent>
          </Card>
        </Grid>

        {/* Security Actions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Security Actions
              </Typography>

              <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                <Button
                  variant="outlined"
                  startIcon={<LockIcon />}
                  onClick={handleResetPassword}
                  disabled={loading}
                >
                  Reset Password
                </Button>

                {/* {member.communityStatus !== 'suspended' ? (
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<BlockIcon />}
                    onClick={() => setSuspendDialogOpen(true)}
                    disabled={loading}
                  >
                    Suspend Account
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<CheckIcon />}
                    onClick={handleReactivateAccount}
                    disabled={loading}
                  >
                    Reactivate Account
                  </Button>
                )} */}

                <Button
                  variant="outlined"
                  startIcon={<HistoryIcon />}
                  onClick={() => {
                    // TODO: Show login history
                    console.log("Show login history for member:", member.uuid);
                  }}
                >
                  View Login History
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Blacklist Information */}
        {member.blacklistReport && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Blacklist Report
                </Typography>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary="Spam Flag"
                      secondary={member.blacklistReport.isSpam ? "Yes" : "No"}
                    />
                  </ListItem>
                  {/* {member.blacklistReport.reason && (
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Reason"
                        secondary={member.blacklistReport.reason}
                      />
                    </ListItem>
                  )} */}
                  {/* {member.blacklistReport.dateReported && (
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Date Reported"
                        secondary={formatDate(member.blacklistReport.dateReported)}
                      />
                    </ListItem>
                  )} */}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Suspend Account Dialog */}
      <Dialog
        open={suspendDialogOpen}
        onClose={() => setSuspendDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Suspend Account</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Reason for Suspension"
              multiline
              rows={3}
              value={suspendReason}
              onChange={(e) => setSuspendReason(e.target.value)}
              placeholder="Please provide a reason for suspending this account..."
              required
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth>
              <InputLabel>Suspension Duration</InputLabel>
              <Select
                value={suspendDuration}
                label="Suspension Duration"
                onChange={(e) => setSuspendDuration(e.target.value)}
              >
                <MenuItem value="1">1 day</MenuItem>
                <MenuItem value="7">7 days</MenuItem>
                <MenuItem value="30">30 days</MenuItem>
                <MenuItem value="90">90 days</MenuItem>
                <MenuItem value="permanent">Permanent</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSuspendDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSuspendAccount}
            variant="contained"
            color="error"
            disabled={!suspendReason.trim() || loading}
          >
            Suspend Account
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberSecurityTab;
