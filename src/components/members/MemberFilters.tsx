"use client";

import React, { useState } from "react";
import {
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  Collapse,
  IconButton,
  Chip,
  Divider,
} from "@mui/material";
import {
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { MemberSearchFilters } from "@/types/member";

interface MemberFiltersProps {
  filters: MemberSearchFilters;
  onFiltersChange: (filters: MemberSearchFilters) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  loading?: boolean;
}

const MEMBERSHIP_TIERS = [
  { value: "lite", label: "Lite" },
  { value: "premium", label: "Premium" },
];

const COMMUNITY_STATUSES = [
  { value: "unverified", label: "Unverified" },
  { value: "verified", label: "Verified" },
  { value: "pending", label: "Pending" },
  { value: "rejected", label: "Rejected" },
];

const VERIFICATION_STATUSES = [
  { value: "pending", label: "Pending" },
  { value: "verified", label: "Verified" },
  { value: "rejected", label: "Rejected" },
  { value: "under_review", label: "Under Review" },
];

const COMPANY_SIZES = [
  { value: "1-10", label: "1-10 employees" },
  { value: "11-50", label: "11-50 employees" },
  { value: "51-100", label: "51-100 employees" },
  { value: "101-500", label: "101-500 employees" },
  { value: "500+", label: "500+ employees" },
];

export function MemberFilters({
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  loading = false,
}: MemberFiltersProps) {
  const [expanded, setExpanded] = useState(false);

  const handleFilterChange = (key: keyof MemberSearchFilters, value: any) => {
    const updatedFilters = {
      ...filters,
      [key]: value,
    };
    onFiltersChange(updatedFilters);
  };

  const handleApplyFilters = () => {
    onApplyFilters();
  };

  const handleClearFilters = () => {
    const clearedFilters: MemberSearchFilters = {
      search: "",
      firstName: "",
      lastName: "",
      email: "",
      membershipTier: "",
      communityStatus: "",
      verificationStatus: "",
      organizationName: "",
      organizationCity: "",
      organizationState: "",
      organizationZip: "",
      companySize: "",
      industry: "",
      dateCreatedFrom: "",
      dateCreatedTo: "",
    };
    onFiltersChange(clearedFilters);
    onClearFilters();
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value && value !== ""
  );

  const activeFilterCount = Object.values(filters).filter(
    (value) => value && value !== ""
  ).length;

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box
          onClick={() => setExpanded(!expanded)}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: expanded ? 2 : 0,
            cursor: "pointer",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Filters</Typography>
            {hasActiveFilters && (
              <Chip
                label={`${activeFilterCount} active`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            {hasActiveFilters && (
              <Button
                size="small"
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
                disabled={loading}
              >
                Clear All
              </Button>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {/* Search */}
            {/* <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search || ""}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                placeholder="Search by name, email, or organization..."
                size="small"
              />
            </Grid> */}

            {/* Member Fields */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                value={filters.firstName || ""}
                onChange={(e) =>
                  handleFilterChange("firstName", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={filters.lastName || ""}
                onChange={(e) => handleFilterChange("lastName", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                value={filters.email || ""}
                onChange={(e) => handleFilterChange("email", e.target.value)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Membership Tier</InputLabel>
                <Select
                  value={filters.membershipTier || ""}
                  onChange={(e) =>
                    handleFilterChange("membershipTier", e.target.value)
                  }
                  label="Membership Tier"
                >
                  <MenuItem value="">All Tiers</MenuItem>
                  {MEMBERSHIP_TIERS.map((tier) => (
                    <MenuItem key={tier.value} value={tier.value}>
                      {tier.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Community Status</InputLabel>
                <Select
                  value={filters.communityStatus || ""}
                  onChange={(e) =>
                    handleFilterChange("communityStatus", e.target.value)
                  }
                  label="Community Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  {COMMUNITY_STATUSES.map((status) => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Verification Status</InputLabel>
                <Select
                  value={filters.verificationStatus || ""}
                  onChange={(e) =>
                    handleFilterChange("verificationStatus", e.target.value)
                  }
                  label="Verification Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  {VERIFICATION_STATUSES.map((status) => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Organization Fields */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Organization Filters
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Organization Name"
                value={filters.organizationName || ""}
                onChange={(e) =>
                  handleFilterChange("organizationName", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="City"
                value={filters.organizationCity || ""}
                onChange={(e) =>
                  handleFilterChange("organizationCity", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="State"
                value={filters.organizationState || ""}
                onChange={(e) =>
                  handleFilterChange("organizationState", e.target.value)
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="ZIP Code"
                value={filters.organizationZip || ""}
                onChange={(e) =>
                  handleFilterChange("organizationZip", e.target.value)
                }
                size="small"
              />
            </Grid>

            {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Company Size</InputLabel>
                <Select
                  value={filters.companySize || ""}
                  onChange={(e) =>
                    handleFilterChange("companySize", e.target.value)
                  }
                  label="Company Size"
                >
                  <MenuItem value="">All Sizes</MenuItem>
                  {COMPANY_SIZES.map((size) => (
                    <MenuItem key={size.value} value={size.value}>
                      {size.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid> */}

            {/* <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Industry"
                value={filters.industry || ""}
                onChange={(e) => handleFilterChange("industry", e.target.value)}
                size="small"
              />
            </Grid> */}

            {/* Date Range */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                Date Range Filter According to Member Creation
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created From"
                type="date"
                value={filters.dateCreatedFrom || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedFrom", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Created To"
                type="date"
                value={filters.dateCreatedTo || ""}
                onChange={(e) =>
                  handleFilterChange("dateCreatedTo", e.target.value)
                }
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleApplyFilters}
              disabled={loading}
            >
              Apply Filters
            </Button>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}
