"use client";

import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  newOrganizationSchema,
  type NewOrganizationData,
} from "@/lib/validations/member";
import { membersService } from "@/services/members";

interface NewOrganizationFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (org: NewOrganizationData) => void;
}

const US_STATES = [
  "AL",
  "AK",
  "AZ",
  "AR",
  "CA",
  "CO",
  "CT",
  "DE",
  "FL",
  "GA",
  "HI",
  "ID",
  "IL",
  "IN",
  "IA",
  "KS",
  "KY",
  "LA",
  "ME",
  "MD",
  "MA",
  "MI",
  "MN",
  "MS",
  "MO",
  "MT",
  "NE",
  "NV",
  "NH",
  "NJ",
  "NM",
  "NY",
  "NC",
  "ND",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VT",
  "VA",
  "WA",
  "WV",
  "WI",
  "WY",
];

const INDUSTRIES = [
  "Technology",
  "Healthcare",
  "Finance",
  "Education",
  "Manufacturing",
  "Retail",
  "Real Estate",
  "Consulting",
  "Legal",
  "Marketing",
  "Non-Profit",
  "Government",
  "Other",
];

const COMPANY_SIZES = ["1-10", "11-50", "51-100", "101-500", "500+"];

const NewOrganizationForm: React.FC<NewOrganizationFormProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<NewOrganizationData>({
    resolver: zodResolver(newOrganizationSchema),
    defaultValues: {
      name: "",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      phone: "",
      email: "",
      annualRevenue: "",
      industry: "",
      yearFounded: "",
      companySize: undefined,
    },
    mode: "onChange",
  });

  const handleFormSubmit = async (data: NewOrganizationData) => {
    try {
      // Create organization using the service
      const newOrg = await membersService.createOrganization(
        data as NewOrganizationData
      );
      onSuccess(newOrg as any);
      reset();
    } catch (error) {
      console.error("Failed to create organization:", error);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Organization</DialogTitle>

      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <DialogContent>
          <Grid container spacing={2}>
            {/* Organization Name */}
            <Grid item xs={12}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Organization Name"
                    fullWidth
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    required
                  />
                )}
              />
            </Grid>

            {/* Address */}
            <Grid item xs={12}>
              <Controller
                name="address1"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Address Line 1"
                    fullWidth
                    error={!!errors.address1}
                    helperText={errors.address1?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="address2"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Address Line 2"
                    fullWidth
                    error={!!errors.address2}
                    helperText={errors.address2?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="city"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="City"
                    fullWidth
                    error={!!errors.city}
                    helperText={errors.city?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.state}>
                    <InputLabel>State</InputLabel>
                    <Select {...field} label="State">
                      <MenuItem value="">Select State</MenuItem>
                      {US_STATES.map((state) => (
                        <MenuItem key={state} value={state}>
                          {state}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Controller
                name="zip"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="ZIP Code"
                    fullWidth
                    error={!!errors.zip}
                    helperText={errors.zip?.message}
                    placeholder="12345"
                  />
                )}
              />
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12} sm={6}>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Phone Number"
                    fullWidth
                    error={!!errors.phone}
                    helperText={errors.phone?.message}
                    placeholder="******-123-4567"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Email"
                    type="email"
                    fullWidth
                    error={!!errors.email}
                    helperText={errors.email?.message}
                  />
                )}
              />
            </Grid>

            {/* Business Information */}
            <Grid item xs={12} sm={6}>
              <Controller
                name="industry"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.industry}>
                    <InputLabel>Industry</InputLabel>
                    <Select {...field} label="Industry">
                      <MenuItem value="">Select Industry</MenuItem>
                      {INDUSTRIES.map((industry) => (
                        <MenuItem key={industry} value={industry}>
                          {industry}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="companySize"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.companySize}>
                    <InputLabel>Company Size</InputLabel>
                    <Select {...field} label="Company Size">
                      <MenuItem value="">Select Size</MenuItem>
                      {COMPANY_SIZES.map((size) => (
                        <MenuItem key={size} value={size}>
                          {size} employees
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="annualRevenue"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Annual Revenue"
                    fullWidth
                    error={!!errors.annualRevenue}
                    helperText={errors.annualRevenue?.message}
                    placeholder="$1,000,000"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="yearFounded"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Year Founded"
                    fullWidth
                    error={!!errors.yearFounded}
                    helperText={errors.yearFounded?.message}
                    placeholder="2020"
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit" variant="contained" disabled={!isValid}>
            Create Organization
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default NewOrganizationForm;
