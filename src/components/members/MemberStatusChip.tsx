"use client";

import React from "react";
import { Chip, ChipProps, Tooltip, Box } from "@mui/material";
import {
  CheckCircle as VerifiedIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Pending as PendingIcon,
  Block as BlockedIcon,
  Star as PremiumIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  BusinessCenter as OrganizationIcon,
} from "@mui/icons-material";

export interface MemberStatusChipProps {
  type:
    | "membershipTier"
    | "communityStatus"
    | "verificationStatus"
    | "identityType"
    | "blacklistStatus";
  value: string;
  size?: "small" | "medium";
  variant?: "filled" | "outlined";
  showIcon?: boolean;
  tooltip?: string;
}

export const MemberStatusChip: React.FC<MemberStatusChipProps> = ({
  type,
  value,
  size = "small",
  variant = "outlined",
  showIcon = true,
  tooltip,
}) => {
  const getChipConfig = (): {
    color: ChipProps["color"];
    icon?: React.ReactElement;
    label: string;
  } => {
    switch (type) {
      case "membershipTier":
        switch (value) {
          case "vip":
            return { color: "error", icon: <PremiumIcon />, label: "VIP" };
          case "enterprise":
            return {
              color: "warning",
              icon: <BusinessIcon />,
              label: "Enterprise",
            };
          case "premium":
            return {
              color: "primary",
              icon: <PremiumIcon />,
              label: "Premium",
            };
          case "basic":
            return { color: "default", icon: undefined, label: "Basic" };
          default:
            return { color: "default", icon: undefined, label: value };
        }

      case "communityStatus":
        switch (value) {
          case "verified":
            return {
              color: "success",
              icon: <VerifiedIcon />,
              label: "Verified",
            };
          case "pending":
            return {
              color: "warning",
              icon: <PendingIcon />,
              label: "Pending",
            };
          case "rejected":
            return { color: "error", icon: <ErrorIcon />, label: "Rejected" };
          case "unverified":
            return {
              color: "default",
              icon: <WarningIcon />,
              label: "Unverified",
            };
          default:
            return { color: "default", icon: undefined, label: value };
        }

      case "verificationStatus":
        switch (value) {
          case "verified":
            return {
              color: "success",
              icon: <VerifiedIcon />,
              label: "Verified",
            };
          case "pending":
            return {
              color: "warning",
              icon: <PendingIcon />,
              label: "Pending",
            };
          case "under_review":
            return {
              color: "info",
              icon: <WarningIcon />,
              label: "Under Review",
            };
          case "rejected":
            return { color: "error", icon: <ErrorIcon />, label: "Rejected" };
          default:
            return { color: "default", icon: undefined, label: value };
        }

      case "identityType":
        switch (value) {
          case "organization":
            return {
              color: "primary",
              icon: <OrganizationIcon />,
              label: "Organization",
            };
          case "business":
            return {
              color: "secondary",
              icon: <BusinessIcon />,
              label: "Business",
            };
          case "individual":
            return { color: "info", icon: <PersonIcon />, label: "Individual" };
          default:
            return { color: "default", icon: undefined, label: value };
        }

      case "blacklistStatus":
        return value === "true" || value === "blocked"
          ? { color: "error", icon: <BlockedIcon />, label: "Blacklisted" }
          : { color: "success", icon: <VerifiedIcon />, label: "Clean" };

      default:
        return { color: "default", icon: undefined, label: value };
    }
  };

  const config = getChipConfig();

  const chip = (
    <Chip
      label={config.label}
      color={config.color}
      size={size}
      variant={variant}
      icon={showIcon && config.icon ? config.icon : undefined}
      sx={{
        fontWeight: 500,
        "& .MuiChip-icon": {
          fontSize: size === "small" ? "16px" : "20px",
        },
      }}
    />
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip} arrow>
        <Box component="span">{chip}</Box>
      </Tooltip>
    );
  }

  return chip;
};

export default MemberStatusChip;
