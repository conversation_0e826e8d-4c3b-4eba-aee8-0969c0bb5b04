"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Button,
  Chip,
  Divider,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Warning as WarningIcon,
  CheckCircle as ResolvedIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line, Bar, Doughnut } from "react-chartjs-2";
import { memberAnalyticsService } from "@/services/memberAnalytics";
import { BlacklistAnalytics, BlacklistReportData } from "@/types/analytics";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface BlacklistReportDashboardProps {
  dateRange?: {
    start: string;
    end: string;
  };
}

const BlacklistReportDashboard: React.FC<BlacklistReportDashboardProps> = ({
  dateRange,
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exporting, setExporting] = useState(false);

  const [blacklistAnalytics, setBlacklistAnalytics] =
    useState<BlacklistAnalytics | null>(null);
  const [blacklistReports, setBlacklistReports] = useState<
    BlacklistReportData[]
  >([]);

  useEffect(() => {
    fetchBlacklistData();
  }, [dateRange]);

  const fetchBlacklistData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [analytics, reports] = await Promise.all([
        memberAnalyticsService.getBlacklistAnalytics(dateRange),
        memberAnalyticsService.getBlacklistReports(),
      ]);

      setBlacklistAnalytics(analytics);
      setBlacklistReports(reports);
    } catch (err: any) {
      console.error("Blacklist data fetch error:", err);
      setError(err.message || "Failed to load blacklist data");
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: "csv" | "pdf" | "json") => {
    try {
      setExporting(true);
      const result = await memberAnalyticsService.exportAnalyticsData({
        format,
        dataType: "blacklist_reports",
        dateRange,
      });

      // Create download link
      const link = document.createElement("a");
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err: any) {
      console.error("Export error:", err);
      setError("Failed to export data");
    } finally {
      setExporting(false);
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low":
        return "success.main";
      case "medium":
        return "warning.main";
      case "high":
        return "error.main";
      default:
        return "info.main";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "low":
        return "success";
      case "medium":
        return "warning";
      case "high":
        return "error";
      case "critical":
        return "error";
      default:
        return "default";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "error";
      case "resolved":
        return "success";
      case "false_positive":
        return "info";
      default:
        return "default";
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case "spam":
        return <WarningIcon />;
      case "disposable_email":
        return <ErrorIcon />;
      case "suspicious_activity":
        return <InfoIcon />;
      case "manual_flag":
        return <EditIcon />;
      default:
        return <InfoIcon />;
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "400px",
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 3,
        }}
      >
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
          Blacklist Report Dashboard
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchBlacklistData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={() => handleExport("csv")}
            disabled={exporting}
          >
            Export CSV
          </Button>
        </Box>
      </Box>

      {/* Risk Assessment Summary */}
      {blacklistAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Risk Assessment Summary
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: getRiskColor(
                        blacklistAnalytics.riskAssessment.overallRisk
                      ),
                      mb: 1,
                    }}
                  >
                    {blacklistAnalytics.riskAssessment.riskScore.toFixed(1)}
                  </Typography>
                  <Chip
                    label={blacklistAnalytics.riskAssessment.overallRisk.toUpperCase()}
                    color={
                      blacklistAnalytics.riskAssessment.overallRisk === "high"
                        ? "error"
                        : blacklistAnalytics.riskAssessment.overallRisk ===
                          "medium"
                        ? "warning"
                        : "success"
                    }
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Overall Risk Score
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Risk Factors
                  </Typography>
                  {blacklistAnalytics.riskAssessment.riskFactors.map(
                    (factor, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          mb: 0.5,
                        }}
                      >
                        <WarningIcon
                          sx={{ fontSize: "1rem", color: "warning.main" }}
                        />
                        <Typography variant="body2">{factor}</Typography>
                      </Box>
                    )
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Recommendations
                  </Typography>
                  {blacklistAnalytics.riskAssessment.recommendations.map(
                    (recommendation, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          mb: 0.5,
                        }}
                      >
                        <ResolvedIcon
                          sx={{ fontSize: "1rem", color: "success.main" }}
                        />
                        <Typography variant="body2">
                          {recommendation}
                        </Typography>
                      </Box>
                    )
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Statistics Overview */}
      {blacklistAnalytics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: "center" }}>
                <Typography
                  variant="h4"
                  sx={{ fontWeight: 600, color: "error.main" }}
                >
                  {blacklistAnalytics.statistics.totalReports}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Reports
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: "center" }}>
                <Typography
                  variant="h4"
                  sx={{ fontWeight: 600, color: "warning.main" }}
                >
                  {blacklistAnalytics.statistics.activeReports}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Reports
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: "center" }}>
                <Typography
                  variant="h4"
                  sx={{ fontWeight: 600, color: "success.main" }}
                >
                  {blacklistAnalytics.statistics.resolvedReports}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Resolved Reports
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: "center" }}>
                <Typography
                  variant="h4"
                  sx={{ fontWeight: 600, color: "info.main" }}
                >
                  {blacklistAnalytics.statistics.falsePositives}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  False Positives
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Charts Section */}
      {blacklistAnalytics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* Reports by Type */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Reports by Type
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Doughnut
                    data={{
                      labels: blacklistAnalytics.reportsByType.map(
                        (item) => item.label
                      ),
                      datasets: [
                        {
                          data: blacklistAnalytics.reportsByType.map(
                            (item) => item.value
                          ),
                          backgroundColor: [
                            theme.palette.error.main,
                            theme.palette.warning.main,
                            theme.palette.info.main,
                            theme.palette.primary.main,
                          ],
                          borderWidth: 2,
                          borderColor: theme.palette.background.paper,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { position: "bottom" },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const item =
                                blacklistAnalytics.reportsByType[
                                  context.dataIndex
                                ];
                              return `${context.label}: ${item.value} (${
                                item.percentage?.toFixed(1) ?? "0.0"
                              }%)`;
                            },
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Reports by Severity */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Reports by Severity
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Bar
                    data={{
                      labels: blacklistAnalytics.reportsBySeverity.map(
                        (item) => item.label
                      ),
                      datasets: [
                        {
                          label: "Reports",
                          data: blacklistAnalytics.reportsBySeverity.map(
                            (item) => item.value
                          ),
                          backgroundColor: [
                            theme.palette.success.main,
                            theme.palette.warning.main,
                            theme.palette.error.main,
                            theme.palette.error.dark,
                          ],
                          borderRadius: 4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { display: false },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const item =
                                blacklistAnalytics.reportsBySeverity[
                                  context.dataIndex
                                ];
                              return `${context.label}: ${item.value} (${
                                item.percentage?.toFixed(1) ?? "0.0"
                              }%)`;
                            },
                          },
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: { color: theme.palette.divider },
                        },
                        x: {
                          grid: { display: false },
                        },
                      },
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Time Series Chart */}
      {blacklistAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Blacklist Reports Over Time
            </Typography>
            <Box sx={{ height: 300 }}>
              <Line
                data={{
                  labels: blacklistAnalytics.timeSeriesData.map(
                    (item) => item.date
                  ),
                  datasets: [
                    {
                      label: "Reports",
                      data: blacklistAnalytics.timeSeriesData.map(
                        (item) => item.value
                      ),
                      borderColor: theme.palette.error.main,
                      backgroundColor: theme.palette.error.main + "20",
                      fill: true,
                      tension: 0.4,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      mode: "index",
                      intersect: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: { color: theme.palette.divider },
                    },
                    x: {
                      grid: { color: theme.palette.divider },
                    },
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Recent Reports Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Recent Blacklist Reports
          </Typography>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Member</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Severity</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {blacklistReports.slice(0, 10).map((report) => (
                  <TableRow key={report.reportId}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {report.memberName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {report.memberId}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        {getReportTypeIcon(report.reportType)}
                        <Typography variant="body2">
                          {report.reportType
                            .replace("_", " ")
                            .charAt(0)
                            .toUpperCase() +
                            report.reportType.replace("_", " ").slice(1)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          report.severity.charAt(0).toUpperCase() +
                          report.severity.slice(1)
                        }
                        color={getSeverityColor(report.severity)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          report.status.charAt(0).toUpperCase() +
                          report.status.slice(1)
                        }
                        color={getStatusColor(report.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(report.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", gap: 0.5 }}>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Report">
                          <IconButton size="small">
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default BlacklistReportDashboard;
