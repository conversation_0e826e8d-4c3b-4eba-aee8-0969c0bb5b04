"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  FormHelperText,
  Autocomplete,
  Checkbox,
  ListItemText,
  OutlinedInput,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  onExport: (exportData: {
    filters: any;
    selectedFields: string[];
    notes: string;
  }) => void;
  currentFilters: any;
  loading?: boolean;
}

// Available fields for export
const AVAILABLE_FIELDS = [
  // Member fields
  { value: "firstName", label: "First Name", category: "Member" },
  { value: "lastName", label: "Last Name", category: "Member" },
  { value: "loginEmail", label: "Login Email", category: "Member" },
  {
    value: "personalBusinessEmail",
    label: "Business Email",
    category: "Member",
  },
  { value: "phone", label: "Phone", category: "Member" },
  {
    value: "professionalTitle",
    label: "Professional Title",
    category: "Member",
  },
  { value: "membershipTier", label: "Membership Tier", category: "Member" },
  { value: "communityStatus", label: "Community Status", category: "Member" },
  {
    value: "verificationStatus",
    label: "Verification Status",
    category: "Member",
  },
  { value: "dateCreated", label: "Date Created", category: "Member" },

  // Organization fields
  {
    value: "organizationName",
    label: "Organization Name",
    category: "Organization",
  },
  { value: "city", label: "City", category: "Organization" },
  { value: "state", label: "State", category: "Organization" },
  { value: "zip", label: "ZIP Code", category: "Organization" },
  { value: "industry", label: "Industry", category: "Organization" },
  { value: "companySize", label: "Company Size", category: "Organization" },
  { value: "annualRevenue", label: "Annual Revenue", category: "Organization" },
  { value: "yearFounded", label: "Year Founded", category: "Organization" },
];

export function ExportModal({
  open,
  onClose,
  onExport,
  currentFilters,
  loading = false,
}: ExportModalProps) {
  const [selectedFields, setSelectedFields] = useState<string[]>([
    "firstName",
    "lastName",
    "loginEmail",
    "verificationStatus",
    "organizationName",
    "city",
    "state",
  ]);
  const [notes, setNotes] = useState("");
  const [notesError, setNotesError] = useState("");

  const handleFieldToggle = (fieldValue: string) => {
    setSelectedFields((prev) =>
      prev.includes(fieldValue)
        ? prev.filter((field) => field !== fieldValue)
        : [...prev, fieldValue]
    );
  };

  const handleSelectAll = () => {
    setSelectedFields(AVAILABLE_FIELDS.map((field) => field.value));
  };

  const handleClearAll = () => {
    setSelectedFields([]);
  };

  const handleSubmit = () => {
    // Validate notes
    if (!notes.trim()) {
      setNotesError("Please provide a reason for this export");
      return;
    }

    setNotesError("");
    onExport({
      filters: currentFilters,
      selectedFields,
      notes: notes.trim(),
    });
    setNotes("");
  };

  const handleClose = () => {
    setSelectedFields([
      "firstName",
      "lastName",
      "loginEmail",
      "verificationStatus",
      "organizationName",
      "city",
      "state",
    ]);
    setNotes("");
    setNotesError("");
    onClose();
  };

  const groupedFields = AVAILABLE_FIELDS.reduce((acc, field) => {
    if (!acc[field.category]) {
      acc[field.category] = [];
    }
    acc[field.category].push(field);
    return acc;
  }, {} as Record<string, typeof AVAILABLE_FIELDS>);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div">
          Export Members to CSV
        </Typography>
        <Button onClick={handleClose} sx={{ minWidth: "auto", p: 0.5 }}>
          <CloseIcon />
        </Button>
      </DialogTitle>

      <DialogContent sx={{ pt: 0 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Select Fields to Export
          </Typography>
          <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={handleSelectAll}
              disabled={selectedFields.length === AVAILABLE_FIELDS.length}
            >
              Select All
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={handleClearAll}
              disabled={selectedFields.length === 0}
            >
              Clear All
            </Button>
          </Box>

          {Object.entries(groupedFields).map(([category, fields]) => (
            <Box key={category} sx={{ mb: 2 }}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 600, color: "text.secondary" }}
              >
                {category}
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {fields.map((field) => (
                  <Chip
                    key={field.value}
                    label={field.label}
                    onClick={() => handleFieldToggle(field.value)}
                    color={
                      selectedFields.includes(field.value)
                        ? "primary"
                        : "default"
                    }
                    variant={
                      selectedFields.includes(field.value)
                        ? "filled"
                        : "outlined"
                    }
                    size="small"
                    clickable
                  />
                ))}
              </Box>
            </Box>
          ))}
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Export Reason (Required)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Please explain the purpose of this export..."
            error={!!notesError}
            helperText={notesError}
            variant="outlined"
            size="small"
          />
          <FormHelperText>
            This information will be logged for audit purposes.
          </FormHelperText>
        </Box>

        <Box sx={{ p: 2, bgcolor: "grey.50", borderRadius: 1 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Export Summary
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • {selectedFields.length} field
            {selectedFields.length !== 1 ? "s" : ""} selected
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Current filters will be applied to the export
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • File will be downloaded as CSV format
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, pt: 0 }}>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || selectedFields.length === 0 || !notes.trim()}
        >
          {loading ? "Exporting..." : "Export CSV"}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
