"use client";

import React, { useState } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
} from "@mui/material";
import {
  Flag as FlagIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { MemberWithRelations, MemberFeatureFlag } from "@/types/member";
import { membersService } from "@/services/members";

interface MemberFeatureFlagsTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberFeatureFlagsTab: React.FC<MemberFeatureFlagsTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newFlagName, setNewFlagName] = useState("");
  const [newFlagValue, setNewFlagValue] = useState("true");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleToggleFlag = async (flagId: number, currentValue: boolean) => {
    try {
      setLoading(true);
      setError(null);
      // TODO: Implement toggle feature flag API call
      console.log(
        "Toggling flag:",
        flagId,
        "to:",
        !currentValue,
        "for member:",
        member.uuid
      );
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || "Failed to toggle feature flag");
    } finally {
      setLoading(false);
    }
  };

  const handleAddFlag = async () => {
    try {
      setLoading(true);
      setError(null);
      // TODO: Implement add feature flag API call
      console.log(
        "Adding flag:",
        newFlagName,
        "with value:",
        newFlagValue,
        "for member:",
        member.uuid
      );
      setAddDialogOpen(false);
      setNewFlagName("");
      setNewFlagValue("true");
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || "Failed to add feature flag");
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFlag = async (flagId: number) => {
    try {
      setLoading(true);
      // TODO: Implement remove feature flag API call
      console.log("Removing flag:", flagId, "from member:", member.uuid);
      onMemberUpdate();
    } catch (err: any) {
      setError("Failed to remove feature flag");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getFlagValueDisplay = (flag: MemberFeatureFlag) => {
    return flag.enabled ? "Enabled" : "Disabled";
  };

  const getFlagValueColor = (flag: MemberFeatureFlag) => {
    return flag.enabled ? "success" : "error";
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h6">
          Feature Flags ({member.featureFlags?.length || 0})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
        >
          Add Flag
        </Button>
      </Box>

      {!member.featureFlags || member.featureFlags.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: "center", py: 4 }}>
            <FlagIcon sx={{ fontSize: 48, color: "text.secondary", mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Feature Flags
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This member doesn't have any custom feature flags configured.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={2}>
          {member.featureFlags.map((flag: MemberFeatureFlag) => (
            <Grid item xs={12} md={6} key={flag.id}>
              <Card>
                <CardContent>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      mb: 2,
                    }}
                  >
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {flag.featureHandle}
                      </Typography>
                      <Chip
                        label={getFlagValueDisplay(flag)}
                        color={getFlagValueColor(flag) as any}
                        size="small"
                        icon={flag.enabled ? <CheckIcon /> : <CancelIcon />}
                      />
                    </Box>
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Switch
                        checked={flag.enabled}
                        onChange={() => handleToggleFlag(flag.id, flag.enabled)}
                        disabled={loading}
                      />
                      <Button
                        size="small"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={() => handleRemoveFlag(flag.id)}
                        disabled={loading}
                      >
                        Remove
                      </Button>
                    </Box>
                  </Box>

                  <List dense>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Feature Handle"
                        secondary={flag.featureHandle}
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Status"
                        secondary={flag.enabled ? "Enabled" : "Disabled"}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Add Feature Flag Dialog */}
      <Dialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Feature Flag</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Feature Handle"
              value={newFlagName}
              onChange={(e) => setNewFlagName(e.target.value)}
              placeholder="Enter feature handle (e.g., 'premium_features')"
              required
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={newFlagValue}
                label="Status"
                onChange={(e) => setNewFlagValue(e.target.value)}
              >
                <MenuItem value="true">Enabled</MenuItem>
                <MenuItem value="false">Disabled</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleAddFlag}
            variant="contained"
            disabled={!newFlagName.trim() || loading}
          >
            Add Flag
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberFeatureFlagsTab;
