"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  Chip,
  Alert,
  Grid,
  Divider,
  CircularProgress,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as emailIcon,
  VerifiedUser as VerifyIcon,
} from "@mui/icons-material";
import { MemberWithRelations } from "@/types/member";
// Use Organization from services/members for organizations array
import { membersService } from "@/services/members";
import {
  assignOrganizationToMember,
  getOrganizationsForMember,
  removeOrganizationFromMember,
} from "@/services/organizationService";
import MemberStatusChip from "./MemberStatusChip";
import { showToast } from "@/utils/toast";
import { useDebounce } from "@/utils/debounce";

interface MemberOrganizationsTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
  onOrganizationUpdate?: () => Promise<void>;
}

const MemberOrganizationsTab: React.FC<MemberOrganizationsTabProps> = ({
  member,
  onMemberUpdate,
  onOrganizationUpdate,
}) => {
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [addingOrg, setAddingOrg] = useState(false);
  const [removingOrgId, setRemovingOrgId] = useState<string | null>(null);
  const [searchOrg, setSearchOrg] = useState<string | null>(null);

  // Fetch all orgs for autocomplete
  const [allOrganizations, setAllOrganizations] = useState<any[]>([]);

  const filterOrganizations = useMemo(() => {
    return allOrganizations.filter((org) =>
      !organizations.some((o) => o.uuid === org.uuid)
    );
  }, [allOrganizations, organizations]);

  useEffect(() => {
    fetchMemberOrganizations();
    fetchAllOrganizations(searchOrg);
  }, [member.uuid]);

  const fetchMemberOrganizations = async () => {
    try {
      setLoading(true);
      const orgs = await getOrganizationsForMember(member.uuid);
      setOrganizations(orgs);
    } catch (err) {
      setError("Failed to fetch organizations for member");
      showToast.error("Failed to load member organizations");
    } finally {
      setLoading(false);
    }
  };

  const fetchAllOrganizations = async (value: string | null) => {
    try {
      const orgs = await membersService.getOrganizations(value);
      setAllOrganizations(orgs.organizations);
    } catch (err) {
      // ignore
    }
  };

  const debouncedFetchAllOrganizations = useDebounce(searchOrg, 500);

  useEffect(() => {
    if (debouncedFetchAllOrganizations) {
      fetchAllOrganizations(debouncedFetchAllOrganizations);
    }
  }, [debouncedFetchAllOrganizations]);

  const handleAddOrganization = async () => {
    if (!selectedOrg) return;
    try {
      setAddingOrg(true);
      setError(null);

      await assignOrganizationToMember(member.uuid, selectedOrg.uuid);

      // Refresh organizations list without full page reload
      await fetchMemberOrganizations();

      // Update organization count in header if available
      if (onOrganizationUpdate) {
        await onOrganizationUpdate();
      }

      // Close dialog and reset
      setAddDialogOpen(false);
      setSelectedOrg(null);

      // Show success toast
      showToast.success(
        `Successfully added ${selectedOrg.name} to member's organizations`
      );
    } catch (err: any) {
      const errorMessage = err.message || "Failed to add organization";
      setError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setAddingOrg(false);
    }
  };

  const handleRemoveOrganization = async (orgUuid: string) => {
    const orgToRemove = organizations.find((org) => org.uuid === orgUuid);
    try {
      setRemovingOrgId(orgUuid);
      setError(null);

      await removeOrganizationFromMember(member.uuid, orgUuid);

      // Refresh organizations list without full page reload
      await fetchMemberOrganizations();

      // Update organization count in header if available
      if (onOrganizationUpdate) {
        await onOrganizationUpdate();
      }

      // Show success toast
      showToast.success(
        `Successfully removed ${
          orgToRemove?.name || "organization"
        } from member's organizations`
      );
    } catch (err: any) {
      const errorMessage = err.message || "Failed to remove organization";
      setError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setRemovingOrgId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h6">
          Organizations ({organizations.length})
          {loading && <CircularProgress size={20} sx={{ ml: 2 }} />}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
          disabled={loading}
        >
          Add Organization
        </Button>
      </Box>

      {loading && organizations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: "center", py: 4 }}>
            <CircularProgress size={48} sx={{ mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Loading Organizations...
            </Typography>
          </CardContent>
        </Card>
      ) : organizations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: "center", py: 4 }}>
            <BusinessIcon
              sx={{ fontSize: 48, color: "text.secondary", mb: 2 }}
            />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Organizations Associated
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This member is not associated with any organizations yet.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={2}>
          {organizations.map((org: any) => (
            <Grid item xs={12} md={6} key={org.uuid}>
              <Card>
                <CardContent>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      mb: 2,
                    }}
                  >
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {org.name || "Unnamed Organization"}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
                        {org.city && org.state && (
                          <Chip
                            icon={<LocationIcon />}
                            label={`${org.city}, ${org.state}`}
                            size="small"
                            variant="outlined"
                          />
                        )}
                        {org.industry && (
                          <Chip
                            label={org.industry}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Box>
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveOrganization(org.uuid)}
                      disabled={loading || removingOrgId === org.uuid}
                      sx={{ position: "relative" }}
                    >
                      {removingOrgId === org.uuid ? (
                        <CircularProgress size={20} />
                      ) : (
                        <DeleteIcon />
                      )}
                    </IconButton>
                  </Box>
                  <List dense>
                    {org.email && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText primary="Email" secondary={org.email} />
                      </ListItem>
                    )}
                    {org.phone && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText primary="Phone" secondary={org.phone} />
                      </ListItem>
                    )}
                    {org.address1 && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Address"
                          secondary={
                            <Box>
                              {org.address1}
                              {org.address2 && <br />}
                              {org.address2}
                              {org.city && org.state && org.zip && (
                                <>
                                  <br />
                                  {org.city}, {org.state} {org.zip}
                                </>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    )}
                    {org.annualRevenue && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Annual Revenue"
                          secondary={org.annualRevenue}
                        />
                      </ListItem>
                    )}
                    {org.yearFounded && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Founded"
                          secondary={org.yearFounded}
                        />
                      </ListItem>
                    )}
                    {org.companySize && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Company Size"
                          secondary={org.companySize}
                        />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Add Organization Dialog */}
      <Dialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Organization</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Autocomplete
              options={filterOrganizations}
              getOptionLabel={(option) =>
                `${option.name}${option.city ? `, ${option.city}` : ""}`
              }
              value={selectedOrg}
              onChange={(_, value) => setSelectedOrg(value)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  onChange={(e) => setSearchOrg(e.target.value)}
                  label="Select Organization"
                  placeholder="Search organizations..."
                  fullWidth
                />
              )}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Can't find the organization? You can create a new one or add it
              later.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)} disabled={addingOrg}>
            Cancel
          </Button>
          <Button
            onClick={handleAddOrganization}
            variant="contained"
            disabled={!selectedOrg || addingOrg}
            startIcon={addingOrg ? <CircularProgress size={16} /> : <AddIcon />}
          >
            {addingOrg ? "Adding..." : "Add Organization"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberOrganizationsTab;
