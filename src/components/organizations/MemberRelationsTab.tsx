// Member Relations Tab Component
import React from "react";
import {
  Box,
  Grid,
  Typography,
  Paper,
  Divider,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  CircularProgress,
  TablePagination,
} from "@mui/material";
import {
  People as PeopleIcon,
  EmojiEvents as AwardsIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
} from "@mui/icons-material";

// Types
import {
  Organization,
  MemberRelation,
  MemberAward,
  OrganizationMember,
  PaginationState,
} from "../../types/organization";

interface MemberRelationsTabProps {
  relations?: MemberRelation[];
  awards?: MemberAward[];
  members?: OrganizationMember[];
  organization: Organization;
  membersLoading?: boolean;
  awardsLoading?: boolean;
  membersPagination?: PaginationState;
  onMembersPageChange?: (page: number) => void;
  onMembersPageSizeChange?: (pageSize: number) => void;
}

const MemberRelationsTab: React.FC<MemberRelationsTabProps> = ({
  relations = [],
  awards = [],
  members = [],
  organization,
  membersLoading = false,
  awardsLoading = false,
  membersPagination,
  onMembersPageChange,
  onMembersPageSizeChange,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getAwardStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "winner":
        return "success";
      case "finalist":
        return "primary";
      case "applicant":
        return "warning";
      case "disQualified":
        return "error";
      default:
        return "default";
    }
  };

  const getAwardProgressColor = (progress: number) => {
    if (progress >= 80) return "success";
    if (progress >= 60) return "primary";
    if (progress >= 40) return "warning";
    return "error";
  };

  // Handle pagination change
  const handlePageChange = (event: unknown, newPage: number) => {
    if (onMembersPageChange) {
      onMembersPageChange(newPage + 1); // MUI uses 0-based indexing, API uses 1-based
    }
  };

  const handlePageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onMembersPageSizeChange) {
      onMembersPageSizeChange(parseInt(event.target.value, 10));
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ mb: 1 }}>
          Member Relations & Awards
        </Typography>
        <Typography variant="body2" color="text.secondary">
          View member relationships and awards associated with this organization
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Organization Members */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <PeopleIcon sx={{ mr: 1 }} />
              Organization Members ({membersPagination?.total || members.length}
              )
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {membersLoading ? (
              <Box sx={{ display: "flex", justifyContent: "center", py: 3 }}>
                <CircularProgress />
              </Box>
            ) : members.length === 0 ? (
              <Alert severity="info">
                No members found for this organization.
              </Alert>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Username</TableCell>
                        <TableCell>email</TableCell>
                        <TableCell>Phone</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {members.map((member) => (
                        <TableRow key={member.uuid}>
                          <TableCell>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                              <PersonIcon sx={{ mr: 1, fontSize: "small" }} />
                              {member.name} {member.lastName}
                            </Box>
                          </TableCell>
                          <TableCell>{member.username}</TableCell>
                          <TableCell>{member.email}</TableCell>
                          <TableCell>{member.phoneNumber}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                {membersPagination && (
                  <TablePagination
                    component="div"
                    count={membersPagination.total}
                    page={(membersPagination.page || 1) - 1} // Convert to 0-based for MUI
                    rowsPerPage={membersPagination.pageSize || 10}
                    onPageChange={handlePageChange}
                    onRowsPerPageChange={handlePageSizeChange}
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    labelRowsPerPage="Members per page:"
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} of ${
                        count !== -1 ? count : `more than ${to}`
                      }`
                    }
                  />
                )}
              </>
            )}
          </Paper>
        </Grid>

        {/* Awards */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <AwardsIcon sx={{ mr: 1 }} />
              Awards ({awards.length})
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {awardsLoading ? (
              <Box sx={{ display: "flex", justifyContent: "center", py: 3 }}>
                <CircularProgress />
              </Box>
            ) : awards.length === 0 ? (
              <Alert severity="info">
                No awards found for this organization.
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {awards.map((award) => (
                  <Grid item xs={12} md={6} key={award.uuid}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 500 }}
                        >
                          Award #{award.awardListingElementId}
                        </Typography>
                        <Chip
                          label={award.status}
                          color={getAwardStatusColor(award.status)}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Box>

                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Progress
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mt: 0.5,
                            }}
                          >
                            <Chip
                              label={`${award.progress}%`}
                              color={getAwardProgressColor(award.progress)}
                              size="small"
                            />
                          </Box>
                        </Grid>

                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Member ID
                          </Typography>
                          <Typography variant="body2">
                            {award.memberId}
                          </Typography>
                        </Grid>

                        <Grid item xs={12}>
                          <Box
                            sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}
                          >
                            {award.isQualified && (
                              <Chip
                                label="Qualified"
                                color="success"
                                size="small"
                              />
                            )}
                            {award.isJudged && (
                              <Chip
                                label="Judged"
                                color="primary"
                                size="small"
                              />
                            )}
                            {award.isPaid && (
                              <Chip
                                label="Paid"
                                color="secondary"
                                size="small"
                              />
                            )}
                            {award.isWinner && (
                              <Chip
                                label="Winner"
                                color="success"
                                size="small"
                              />
                            )}
                            {award.isPreviousWinner && (
                              <Chip
                                label="Previous Winner"
                                color="info"
                                size="small"
                              />
                            )}
                            {award.isDisQualified && (
                              <Chip
                                label="DisQualified"
                                color="error"
                                size="small"
                              />
                            )}
                          </Box>
                        </Grid>

                        {award.winnerTypes && (
                          <Grid item xs={12}>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              Winner Types
                            </Typography>
                            <Typography variant="body2">
                              {award.winnerTypes}
                            </Typography>
                          </Grid>
                        )}

                        {award.applicationLink && (
                          <Grid item xs={12}>
                            <Button
                              variant="outlined"
                              size="small"
                              href={award.applicationLink}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              View Application
                            </Button>
                          </Grid>
                        )}

                        <Grid item xs={12}>
                          <Typography variant="caption" color="text.secondary">
                            Created: {formatDate(award.dateCreated)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Grid>

        {/* Summary Statistics */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Summary Statistics
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: "center" }}>
                  <Typography variant="h4" color="primary">
                    {membersPagination?.total || members.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Organization Members
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: "center" }}>
                  <Typography variant="h4" color="secondary">
                    {awards.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Awards
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: "center" }}>
                  <Typography variant="h4" color="success.main">
                    {awards.filter((a) => a.isWinner).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Winners
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: "center" }}>
                  <Typography variant="h4" color="warning.main">
                    {awards.filter((a) => a.isQualified).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Qualified
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MemberRelationsTab;
