// Organization List Component with MUI DataGrid
import React, { useEffect, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Typography, Alert, CircularProgress, Paper } from "@mui/material";
import {
  DataGrid,
  GridColDef,
  GridPaginationModel,
  GridActionsCellItem,
} from "@mui/x-data-grid";
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useRouter } from "next/navigation";
// Redux imports
import { AppDispatch, RootState } from "../../store";
import { organizationsActions } from "../../store/organizations/redux";
import { organizationsSelectors } from "../../store/organizations/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { ConfirmDialog } from "../ui/ConfirmDialog";
import { Organization } from "@/types/organization";

// Constants
const PAGE_SIZE_OPTIONS = [10, 25, 50];

const OrganizationList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  // Redux state
  const organizations = useSelector(organizationsSelectors.selectOrganizations);
  const loading = useSelector(organizationsSelectors.selectLoading);
  const error = useSelector(organizationsSelectors.selectError);
  const pagination = useSelector(organizationsSelectors.selectPagination);
  const filters = useSelector(
    (state: RootState) => state.organizations.filters
  );
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [organizationToDelete, setOrganizationToDelete] = useState<string>("");

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let orgPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const orgModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "organizations"
      );
      if (orgModule) {
        orgPermissions = orgModule;
      }
    }
  }

  // Handle pagination change
  const handlePaginationModelChange = useCallback(
    (newModel: GridPaginationModel) => {
      // Update filters with new pagination
      const newFilters = {
        ...filters,
        page: newModel.page + 1,
        pageSize: newModel.pageSize,
      };

      dispatch(organizationsActions.setFilters(newFilters));
      dispatch(organizationsActions.fetchOrganizations(newFilters));
    },
    [dispatch, filters]
  );

  // Handle view organization
  const handleViewOrganization = (uuid: string) => {
    router.push(`/organizations/${uuid}`);
  };

  // Handle edit organization
  const handleEditOrganization = (uuid: string) => {
    router.push(`/organizations/${uuid}/edit`);
  };

  // Handle delete organization
  const handleDeleteOrganization = (uuid: string) => {
    if (uuid) {
      dispatch(organizationsActions.deleteOrganization(uuid));
      setShowDeleteDialog(false);
    }
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ display: "flex", alignItems: "center", height: "100%" }}
          fontWeight="medium"
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ display: "flex", alignItems: "center", height: "100%" }}
          color="text.secondary"
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "industry",
      headerName: "Industry",
      flex: 0.8,
      minWidth: 150,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ display: "flex", alignItems: "center", height: "100%" }}
          color="text.secondary"
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 0.8,
      minWidth: 150,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ display: "flex", alignItems: "center", height: "100%" }}
          color="text.secondary"
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "city",
      headerName: "City",
      flex: 0.6,
      minWidth: 120,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ display: "flex", alignItems: "center", height: "100%" }}
          color="text.secondary"
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "actions",
      type: "actions",
      headerName: "Actions",
      flex: 0.5,
      minWidth: 120,
      getActions: (params) => [
        <GridActionsCellItem
          key="view"
          icon={<ViewIcon />}
          label="View"
          onClick={() => handleViewOrganization(params.row.uuid)}
          color="primary"
          disabled={!orgPermissions.view}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => handleEditOrganization(params.row.uuid)}
          color="primary"
          disabled={!orgPermissions.update}
        />,
        <GridActionsCellItem
          key="delete"
          icon={
            <DeleteIcon
              sx={{
                color: !orgPermissions.delete ? "gray" : "red",
                cursor: !orgPermissions.delete ? "not-allowed" : "pointer",
              }}
            />
          }
          label="Delete"
          onClick={() => {
            setShowDeleteDialog(true);
            setOrganizationToDelete(params.row.uuid);
          }}
          color="inherit"
          disabled={!orgPermissions.delete}
        />,
      ],
    },
  ];

  return (
    <Box sx={{ height: "100%", width: "100%" }}>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* DataGrid */}
      <Paper sx={{ height: 600, width: "100%" }}>
        <DataGrid
          rows={organizations || []}
          columns={columns}
          pagination={true}
          paginationMode="server"
          rowCount={pagination.total}
          paginationModel={{
            page: (pagination.page || 1) - 1, // DataGrid uses 0-based page indexing
            pageSize: pagination.pageSize || 10,
          }}
          disableColumnMenu
          disableColumnSorting
          onPaginationModelChange={handlePaginationModelChange}
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          loading={loading}
          disableRowSelectionOnClick
          getRowId={(row) => row.uuid}
          sx={{
            "& .MuiDataGrid-cell": {
              borderBottom: "1px solid #e0e0e0",
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#f5f5f5",
              borderBottom: "2px solid #e0e0e0",
            },
            "& .MuiDataGrid-row:hover": {
              backgroundColor: "#f8f9fa",
            },
            "& .MuiTablePagination-toolbar": {
              minHeight: 50,
            },
          }}
        />
      </Paper>

      <ConfirmDialog
        open={showDeleteDialog}
        onConfirm={() => handleDeleteOrganization(organizationToDelete)}
        onCancel={() => setShowDeleteDialog(false)}
        title="Delete Organization"
        message="Are you sure you want to delete this organization?"
      />

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            zIndex: 1,
          }}
        >
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default OrganizationList;
