// Verified Data Tab Component
import React from "react";
import {
  Box,
  Grid,
  Typography,
  Paper,
  Divider,
  Chip,
  Alert,
} from "@mui/material";
import {
  Verified as VerifiedIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon,
  Security as SecurityIcon,
} from "@mui/icons-material";

// Types
import {
  Organization,
  OrganizationVerifiedData,
} from "../../types/organization";

interface VerifiedDataTabProps {
  verifiedData?: OrganizationVerifiedData | null;
  organization: Organization;
}

const VerifiedDataTab: React.FC<VerifiedDataTabProps> = ({
  verifiedData,
  organization,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getverificationStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "verified":
        return "success";
      case "pending":
        return "warning";
      case "rejected":
        return "error";
      default:
        return "default";
    }
  };

  const getverificationTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case "manual":
        return "primary";
      case "automatic":
        return "secondary";
      default:
        return "default";
    }
  };

  if (!verifiedData) {
    return (
      <Box>
        {/* <Alert severity="info" sx={{ mb: 3 }}>
          No verified data available for this organization. Verification data
          will appear here once the organization has been verified.
        </Alert> */}

        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Organization Information (Unverified)
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Organization Name
                </Typography>
                <Typography variant="body1">{organization.name}</Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Industry
                </Typography>
                <Typography variant="body1">{organization.industry}</Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ mb: 1 }}>
          Verified Organization Data
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This data has been verified and validated by our system
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Verification Status */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <SecurityIcon sx={{ mr: 1 }} />
              Verification Status
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Verification Status
                  </Typography>
                  <Chip
                    label={verifiedData.verificationStatus || "Unknown"}
                    color={getverificationStatusColor(
                      verifiedData.verificationStatus
                    )}
                    icon={<VerifiedIcon />}
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Verification Type
                  </Typography>
                  <Chip
                    label={verifiedData.verificationType || "Unknown"}
                    color={getverificationTypeColor(
                      verifiedData.verificationType
                    )}
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    EIN Number
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.ein || "Not provided"}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Verified Basic Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <BusinessIcon sx={{ mr: 1 }} />
              Verified Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Organization Name
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {verifiedData.name}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Industry
              </Typography>
              <Typography variant="body1">{verifiedData.industry}</Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Founding Year
              </Typography>
              <Typography variant="body1">
                {verifiedData.foundingyear || "Not provided"}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Verified Contact Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <PhoneIcon sx={{ mr: 1 }} />
              Verified Contact Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Phone Number
              </Typography>
              <Typography variant="body1">
                {verifiedData.phone || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Address
              </Typography>
              <Typography variant="body1">
                {verifiedData.address || "Not provided"}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Verified Address Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <LocationIcon sx={{ mr: 1 }} />
              Verified Address Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    City
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.city || "Not provided"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    State
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.state || "Not provided"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    ZIP Code
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.zip || "Not provided"}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Verification Metadata */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <CalendarIcon sx={{ mr: 1 }} />
              Verification Metadata
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Verified By
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.createdBy || "Unknown"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Last Updated By
                  </Typography>
                  <Typography variant="body1">
                    {verifiedData.updatedBy || "Unknown"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date Verified
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(verifiedData.dateCreated)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Last Updated
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(verifiedData.dateUpdated)}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VerifiedDataTab;
