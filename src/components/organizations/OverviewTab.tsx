// Overview Tab Component
import React from "react";
import {
  Box,
  Grid,
  Typography,
  Paper,
  Divider,
  Chip,
  Button,
} from "@mui/material";
import {
  Business as BusinessIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  Group as GroupIcon,
} from "@mui/icons-material";

// Types
import { Organization } from "../../types/organization";

interface OverviewTabProps {
  organization: Organization;
  onEdit: () => void;
  onDelete: () => void;
  orgPermissions?: { update?: boolean; delete?: boolean };
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  organization,
  onEdit,
  onDelete,
  orgPermissions = { update: true, delete: true },
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatAddress = () => {
    const parts = [
      organization.address1,
      organization.address2,
      organization.city,
      organization.state,
      organization.zip,
    ].filter(Boolean);
    return parts.join(", ");
  };

  return (
    <Box>
      {/* Header */}
      <Box
        sx={{
          mb: 3,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5" component="h2">
          Organization Overview
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          {orgPermissions.update && (
            <Button variant="outlined" size="small" onClick={onEdit}>
              Edit
            </Button>
          )}
          {orgPermissions.delete && (
            <Button
              variant="outlined"
              color="error"
              size="small"
              onClick={onDelete}
            >
              Delete
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <BusinessIcon sx={{ mr: 1 }} />
              Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Organization Name
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {organization.name}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Industry
              </Typography>
              <Chip
                label={organization.industry}
                color="primary"
                size="small"
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                email
              </Typography>
              <Typography
                variant="body1"
                sx={{ display: "flex", alignItems: "center" }}
              >
                <EmailIcon sx={{ mr: 1, fontSize: "small" }} />
                {organization.email || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Phone
              </Typography>
              <Typography
                variant="body1"
                sx={{ display: "flex", alignItems: "center" }}
              >
                <PhoneIcon sx={{ mr: 1, fontSize: "small" }} />
                {organization.phone || "Not provided"}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Address Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <LocationIcon sx={{ mr: 1 }} />
              Address Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Address
              </Typography>
              <Typography variant="body1">
                {formatAddress() || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                City
              </Typography>
              <Typography variant="body1">
                {organization.city || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                State
              </Typography>
              <Typography variant="body1">
                {organization.state || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                ZIP Code
              </Typography>
              <Typography variant="body1">
                {organization.zip || "Not provided"}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Additional Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, display: "flex", alignItems: "center" }}
            >
              <CalendarIcon sx={{ mr: 1 }} />
              Additional Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Annual Revenue
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ display: "flex", alignItems: "center" }}
                  >
                    <MoneyIcon sx={{ mr: 1, fontSize: "small" }} />
                    {organization.annualRevenue || "Not provided"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Company Size
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ display: "flex", alignItems: "center" }}
                  >
                    <GroupIcon sx={{ mr: 1, fontSize: "small" }} />
                    {organization.companySize || "Not provided"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Year Founded
                  </Typography>
                  <Typography variant="body1">
                    {organization.yearFounded || "Not provided"}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* System Information */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              System Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              {/* <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created By
                  </Typography>
                  <Typography variant="body1">
                    {organization.createdBy || "Unknown"}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Updated By
                  </Typography>
                  <Typography variant="body1">
                    {organization.updatedBy || "Unknown"}
                  </Typography>
                </Box>
              </Grid> */}

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date Created
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(organization.dateCreated)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date Updated
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(organization.dateUpdated)}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default OverviewTab;
