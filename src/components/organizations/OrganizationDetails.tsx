// Organization Details Component
import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  <PERSON>po<PERSON>,
  Tabs,
  Tab,
  Divider,
} from "@mui/material";

// Tab Components
import OverviewTab from "./OverviewTab";
import MembersTab from "./MembersTab";

// Types
import { Organization } from "../../types/organization";

interface OrganizationDetailsProps {
  organization: Organization;
  activeTab: number;
  onTabChange: (event: React.SyntheticEvent, newValue: number) => void;
  onEdit: () => void;
  onDelete: () => void;
  orgPermissions?: { update?: boolean; delete?: boolean };
}

const OrganizationDetails: React.FC<OrganizationDetailsProps> = ({
  organization,
  activeTab,
  onTabChange,
  onEdit,
  onDelete,
  orgPermissions = { update: true, delete: true },
}) => {
  const tabs = [
    { label: "Overview", value: 0 },
    { label: "Members", value: 1 },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return (
          <OverviewTab
            organization={organization}
            onEdit={onEdit}
            onDelete={onDelete}
            orgPermissions={orgPermissions}
          />
        );
      case 1:
        return <MembersTab organization={organization} />;
      default:
        return (
          <OverviewTab
            organization={organization}
            onEdit={onEdit}
            onDelete={onDelete}
            orgPermissions={orgPermissions}
          />
        );
    }
  };

  return (
    <Box>
      {/* Main Content Card */}
      <Card>
        <CardHeader
          title={organization.name}
          subheader={`${organization.industry} • ${organization.city}, ${organization.state}`}
          sx={{
            "& .MuiCardHeader-title": {
              fontSize: "1.5rem",
              fontWeight: 600,
            },
            "& .MuiCardHeader-subheader": {
              fontSize: "0.875rem",
              color: "text.secondary",
            },
          }}
        />
        <Divider />

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs
            value={activeTab}
            onChange={onTabChange}
            aria-label="organization details tabs"
          >
            {tabs.map((tab) => (
              <Tab key={tab.value} label={tab.label} />
            ))}
          </Tabs>
        </Box>

        {/* Tab Content */}
        <CardContent sx={{ p: 3 }}>{renderTabContent()}</CardContent>
      </Card>
    </Box>
  );
};

export default OrganizationDetails;
