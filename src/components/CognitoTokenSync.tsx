'use client';

import { useEffect } from 'react';
import { useCognitoTokenSync } from '@/hooks/useCognitoTokenSync';

interface CognitoTokenSyncProps {
  autoSync?: boolean;
  syncInterval?: number;
  cookieExpireDays?: number;
  debug?: boolean;
}

/**
 * Component that automatically syncs Cognito tokens from localStorage to cookies
 * Add this component to your app layout or main component to enable automatic token sync
 */
export const CognitoTokenSync: React.FC<CognitoTokenSyncProps> = ({
  autoSync = true,
  syncInterval = 30000, // 30 seconds
  cookieExpireDays = 7,
  debug = false
}) => {
  const { syncTokens, hasTokensInLocalStorage } = useCognitoTokenSync({
    autoSync,
    syncInterval,
    cookieExpireDays
  });

  useEffect(() => {
    if (debug) {
      console.log('🔄 CognitoTokenSync component initialized', {
        autoSync,
        syncInterval,
        cookieExpireDays,
        hasTokens: hasTokensInLocalStorage()
      });
    }
  }, [debug, autoSync, syncInterval, cookieExpireDays, hasTokensInLocalStorage]);

  // This component doesn't render anything visible
  return null;
};

export default CognitoTokenSync;
