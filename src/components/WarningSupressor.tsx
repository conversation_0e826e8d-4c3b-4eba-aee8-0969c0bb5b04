"use client";

import { useEffect } from "react";

export default function WarningSupressor({
  suppressAll = false,
}: {
  suppressAll?: boolean;
}) {
  useEffect(() => {
    // Only suppress warnings if environment variable is set
    if (process.env.NEXT_PUBLIC_SUPPRESS_WARNINGS !== "true") {
      return;
    }

    // Aggressive suppression of scroll behavior warnings
    const originalWarn = console.warn;
    const originalLog = console.log;
    const originalError = console.error;

    const shouldSuppress = (message: any) => {
      // If suppressAll is true, suppress all logs
      if (suppressAll) {
        return true;
      }
      // console.log(process.env.AUTH0_DOMAIN,'AUTH0_DOMAIN');
      // Otherwise, use the existing suppression logic
      if (typeof message === "string") {
        return (
          message.includes("Skipping auto-scroll behavior") ||
          message.includes("position: sticky") ||
          message.includes("position: fixed") ||
          message.includes("MuiBox-root") ||
          message.includes("MuiContainer-root") ||
          message.includes("MuiPaper-root")
        );
      }
      return false;
    };

    console.warn = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalWarn.apply(console, args);
      }
    };

    console.log = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalLog.apply(console, args);
      }
    };

    console.error = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalError.apply(console, args);
      }
    };

    // Cleanup function
    return () => {
      console.warn = originalWarn;
      console.log = originalLog;
      console.error = originalError;
    };
  }, [suppressAll]); // Add suppressAll to dependency array

  return null; // This component renders nothing
}
