import { z } from 'zod';

// Password strength requirements
export const passwordRequirements = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
};

// Password strength validation function
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number; // 0-4 (0=weak, 4=strong)
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  // Length check
  if (password.length >= passwordRequirements.minLength) {
    score += 1;
  } else {
    feedback.push(`At least ${passwordRequirements.minLength} characters`);
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('At least one uppercase letter');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('At least one lowercase letter');
  }

  // Numbers check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('At least one number');
  }

  // Special characters check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('At least one special character');
  }

  return {
    isValid: score >= 4, // Require at least 4 out of 5 criteria
    score: Math.min(score, 4),
    feedback,
  };
}

// Change password schema
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(passwordRequirements.minLength, `Password must be at least ${passwordRequirements.minLength} characters`)
    .refine((password) => {
      const validation = validatePasswordStrength(password);
      return validation.isValid;
    }, (password) => {
      const validation = validatePasswordStrength(password);
      return {
        message: `Password must meet requirements: ${validation.feedback.join(', ')}`,
      };
    }),
  confirmPassword: z.string().min(1, 'Please confirm your new password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

// Password strength indicator component props
export interface PasswordStrengthIndicatorProps {
  password: string;
  showFeedback?: boolean;
}

// Password strength levels
export const passwordStrengthLevels = {
  0: { label: 'Very Weak', color: '#d32f2f' },
  1: { label: 'Weak', color: '#f57c00' },
  2: { label: 'Fair', color: '#fbc02d' },
  3: { label: 'Good', color: '#388e3c' },
  4: { label: 'Strong', color: '#2e7d32' },
}; 