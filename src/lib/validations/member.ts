import { z } from "zod";

// Phone number validation regex (supports various formats)
const phoneRegex =
  /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;

// email validation
const emailSchema = z.string().email("Please enter a valid email address");

// Phone validation
const phoneSchema = z
  .string()
  .regex(phoneRegex, "Please enter a valid phone number")
  .optional()
  .or(z.literal(""));

// Organization schema
export const organizationSchema = z.object({
  uuid: z.string().optional(),
  name: z.string().min(1, "Organization name is required"),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal("")),
  annualRevenue: z.string().optional(),
  industry: z.string().optional(),
  yearFounded: z.string().optional(),
  companySize: z.string().optional(),
});

// Feature flag schema
export const featureFlagSchema = z.object({
  id: z.number().optional(),
  memberId: z.number().optional(),
  featureHandle: z.string().min(1, "Feature handle is required"),
  enabled: z.boolean(),
});

// Member form schema
export const memberFormSchema = z.object({
  // Basic Information
  firstName: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name must be less than 50 characters"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name must be less than 50 characters"),
  loginEmail: emailSchema,
  loginEmailVerified: z.boolean().default(false),
  phone: phoneSchema,
  personalBusinessEmail: emailSchema.optional().or(z.literal("")),
  professionalTitle: z
    .string()
    .max(100, "Professional title must be less than 100 characters")
    .optional(),

  // Identity and Membership
  identityType: z.enum(["business", "individual", "organization"]),
  membershipTier: z.enum(["basic", "premium", "enterprise", "vip"]),
  communityStatus: z.enum(["unverified", "verified", "pending", "rejected"]),
  hasSeenFirstLoginMessage: z.boolean().default(false),

  // Organizations
  organizations: z.array(organizationSchema).default([]),

  // Feature Flags
  featureFlags: z.array(featureFlagSchema).default([]),

  // Auth0 Integration
  createAuth0User: z.boolean().default(false),
  auth0Password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .optional(),

  // Metadata
  notes: z.string().optional(),
});

// Create member schema
export const createMemberSchema = memberFormSchema
  .extend({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .optional(),
    confirmPassword: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

// Update member schema
export const updateMemberSchema = memberFormSchema.extend({
  // Additional fields for updates
  selectedOrganizations: z
    .array(
      z.object({
        uuid: z.string(),
        name: z.string(),
        city: z.string().optional(),
        state: z.string().optional(),
        industry: z.string().optional(),
      })
    )
    .optional(),
});

// New organization schema
export const newOrganizationSchema = z.object({
  name: z.string().min(1, "Organization name is required"),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal("")),
  annualRevenue: z.string().optional(),
  industry: z.string().optional(),
  yearFounded: z.string().optional(),
  companySize: z.string().optional(),
});

// Member search filters schema
export const memberSearchFiltersSchema = z.object({
  search: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  membershipTier: z.string().optional(),
  communityStatus: z.string().optional(),
  verificationStatus: z.string().optional(),
  organizationName: z.string().optional(),
  organizationCity: z.string().optional(),
  organizationState: z.string().optional(),
  organizationZip: z.string().optional(),
  companySize: z.string().optional(),
  industry: z.string().optional(),
  dateCreatedFrom: z.string().optional(),
  dateCreatedTo: z.string().optional(),
});

// Export types
export type CreateMemberData = z.infer<typeof createMemberSchema>;
export type UpdateMemberData = z.infer<typeof updateMemberSchema>;
export type OrganizationData = z.infer<typeof organizationSchema>;
export type NewOrganizationData = z.infer<typeof newOrganizationSchema>;
export type FeatureFlagData = z.infer<typeof featureFlagSchema>;

// Utility functions
export const validateemail = (email: string) => {
  return emailSchema.safeParse(email);
};

export const validatePhone = (phone: string) => {
  return phoneSchema.safeParse(phone);
};

export const validateCrossField = (data: any) => {
  // Add cross-field validation logic here if needed
  return { success: true, data };
};
