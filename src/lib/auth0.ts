import { Auth0Client } from "@auth0/nextjs-auth0/server";

// Fallback for APP_BASE_URL if not set
const getAppBaseUrl = () => {
  if (process.env.APP_BASE_URL) {
    return process.env.APP_BASE_URL;
  }

  // In production, we'll need to get this from the request context
  // For now, use a reasonable default
  if (process.env.NODE_ENV === "production") {
    return process.env.NEXT_PUBLIC_APP_URL || "https://your-domain.com";
  }

  return "http://localhost:3000";
};

export const auth0 = new Auth0Client({
  domain: process.env.AUTH0_DOMAIN!,
  clientId: process.env.AUTH0_CLIENT_ID!,
  clientSecret: process.env.AUTH0_CLIENT_SECRET!,
  secret: process.env.AUTH0_SECRET!,
  appBaseUrl: getAppBaseUrl(),
  authorizationParameters: {
    scope: "openid profile email",
    audience: process.env.AUTH0_AUDIENCE,
  },
  session: {
    rolling: true,
    absoluteDuration: 60 * 60 * 24 * 7, // 7 days
    inactivityDuration: 60 * 60 * 24, // 1 day
  },
  signInReturnToPath: "/member-user/dashboard",
  routes: {
    callback: "/callback", // Match your Auth0 configuration
  },
});
