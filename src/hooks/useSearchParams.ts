// hooks/useSearchParams.ts
import { useEffect, useState } from 'react';

interface UseSearchParamsReturn {
  searchParams: URLSearchParams | null;
  get: (key: string) => string | null;
  getAll: (key: string) => string[];
  has: (key: string) => boolean;
  toString: () => string;
  isReady: boolean;
}

export function useSearchParams(): UseSearchParamsReturn {
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // This effect only runs on the client side
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setSearchParams(params);
      setIsReady(true);

      // Optional: Listen for URL changes (for SPA navigation)
      const handleUrlChange = () => {
        const newParams = new URLSearchParams(window.location.search);
        setSearchParams(newParams);
      };

      // Listen for popstate events (back/forward navigation)
      window.addEventListener('popstate', handleUrlChange);

      // For Next.js router changes, you might want to listen to router events
      // This requires importing useRouter, but we'll handle it differently
      
      return () => {
        window.removeEventListener('popstate', handleUrlChange);
      };
    }
  }, []);

  // Helper functions
  const get = (key: string): string | null => {
    return searchParams?.get(key) ?? null;
  };

  const getAll = (key: string): string[] => {
    return searchParams?.getAll(key) ?? [];
  };

  const has = (key: string): boolean => {
    return searchParams?.has(key) ?? false;
  };

  const toString = (): string => {
    return searchParams?.toString() ?? '';
  };

  return {
    searchParams,
    get,
    getAll,
    has,
    toString,
    isReady,
  };
}

// Enhanced version that works with Next.js router (optional)
// Only use this if you need to detect route changes
export function useSearchParamsWithRouter(): UseSearchParamsReturn {
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initial setup
    const updateParams = () => {
      const params = new URLSearchParams(window.location.search);
      setSearchParams(params);
      setIsReady(true);
    };

    updateParams();

    // Dynamic import to avoid SSR issues
    import('next/router').then(({ default: Router }) => {
      const handleRouteChange = () => {
        updateParams();
      };

      Router.events.on('routeChangeComplete', handleRouteChange);
      
      return () => {
        Router.events.off('routeChangeComplete', handleRouteChange);
      };
    }).catch(() => {
      // Fallback if Next.js router is not available
      console.warn('Next.js router not available, using basic URL detection');
    });

    // Handle browser navigation
    const handlePopState = () => {
      updateParams();
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  const get = (key: string): string | null => {
    return searchParams?.get(key) ?? null;
  };

  const getAll = (key: string): string[] => {
    return searchParams?.getAll(key) ?? [];
  };

  const has = (key: string): boolean => {
    return searchParams?.has(key) ?? false;
  };

  const toString = (): string => {
    return searchParams?.toString() ?? '';
  };

  return {
    searchParams,
    get,
    getAll,
    has,
    toString,
    isReady,
  };
}

// Simple version for one-time parameter reading
export function useSearchParam(key: string): string | null {
  const [value, setValue] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setValue(params.get(key));
      setIsReady(true);
    }
  }, [key]);

  return isReady ? value : null;
}