"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import jwt from "jsonwebtoken";
import {
  refreshTokenIfNeeded,
  refreshTokenWithNetworkAwareness,
  isAccessTokenExpired,
  getTokenExpiryInfo,
  shouldRetryTokenRefresh,
} from "@/utils/simpleTokenRefresh";
import {
  getCognitoTokensFromCookies,
  performCompleteLogout,
} from "@/utils/auth";

interface BackgroundRefreshOptions {
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)
  maxRetries?: number; // Maximum retry attempts (default: 3)
  retryDelay?: number; // Base retry delay in milliseconds (default: 1000)
  maxRetryDelay?: number; // Maximum retry delay in milliseconds (default: 10000)
  enableNetworkAwareness?: boolean; // Enable network status tracking (default: true)
}

interface NetworkStatus {
  isOnline: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
  lastNetworkError: Date | null;
}

export const useBackgroundTokenRefresh = (
  options: BackgroundRefreshOptions = {}
) => {
  const {
    checkInterval = 30000, // 30 seconds
    refreshBuffer = 5, // 5 minutes
    maxRetries = 3,
    retryDelay = 1000,
    maxRetryDelay = 10000,
    enableNetworkAwareness = true,
  } = options;

  const router = useRouter();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: typeof navigator !== "undefined" ? navigator.onLine : true,
    lastCheck: new Date(),
    consecutiveFailures: 0,
    lastNetworkError: null,
  });

  // Check if token needs refresh (within buffer time)
  const shouldRefreshSoon = (accessToken: string): boolean => {
    try {
      const decoded = jwt.decode(accessToken, { complete: true });
      if (!decoded || typeof decoded === "string" || !decoded.payload) {
        return true;
      }

      const payload = decoded.payload as any;
      const currentTime = Math.floor(Date.now() / 1000);
      const expiresAt = payload.exp;
      const timeUntilExpiry = expiresAt - currentTime;
      const bufferSeconds = refreshBuffer * 60;

      return timeUntilExpiry <= bufferSeconds;
    } catch (error) {
      return true;
    }
  };

  // Update network status
  const updateNetworkStatus = (
    isOnline: boolean,
    isNetworkError: boolean = false
  ) => {
    setNetworkStatus((prev) => ({
      ...prev,
      isOnline,
      lastCheck: new Date(),
      consecutiveFailures: isNetworkError ? prev.consecutiveFailures + 1 : 0,
      lastNetworkError: isNetworkError ? new Date() : prev.lastNetworkError,
    }));
  };

  // Enhanced silent token refresh with retry logic
  const performSilentRefresh = async (): Promise<boolean> => {
    if (isRefreshingRef.current) {
      console.log("🔄 Token refresh already in progress, skipping...");
      return false;
    }

    try {
      isRefreshingRef.current = true;
      console.log("🔄 Performing silent token refresh...");

      let success = false;
      let isNetworkError = false;

      if (enableNetworkAwareness) {
        // Use network-aware refresh
        const result = await refreshTokenWithNetworkAwareness({
          maxRetries,
          baseDelay: retryDelay,
          maxDelay: maxRetryDelay,
          backoffMultiplier: 2,
        });
        success = result.success;
        isNetworkError = result.isNetworkError;
      } else {
        // Use standard refresh with retry
        success = await refreshTokenIfNeeded({
          maxRetries,
          baseDelay: retryDelay,
          maxDelay: maxRetryDelay,
          backoffMultiplier: 2,
        });
      }

      if (success) {
        console.log("✅ Silent token refresh successful");
        updateNetworkStatus(true, false);
        return true;
      } else {
        console.error("❌ Silent token refresh failed - user needs to login");
        updateNetworkStatus(networkStatus.isOnline, isNetworkError);

        // Only logout immediately for non-network errors
        if (!isNetworkError) {
          performCompleteLogout();
          router.push("/login");
        }
        return false;
      }
    } catch (error: any) {
      console.error("❌ Silent refresh error:", error);
      const isNetwork =
        error.message?.toLowerCase().includes("network") ||
        error.message?.toLowerCase().includes("fetch") ||
        error.message?.toLowerCase().includes("timeout");

      updateNetworkStatus(networkStatus.isOnline, isNetwork);

      // Only logout immediately for non-network errors
      if (!isNetwork) {
        performCompleteLogout();
        router.push("/login");
      }
      return false;
    } finally {
      isRefreshingRef.current = false;
    }
  };

  // Enhanced token check with network awareness
  const checkAndRefreshTokens = async (): Promise<void> => {
    try {
      // Check network status first
      if (enableNetworkAwareness && !navigator.onLine) {
        console.log("🌐 Network is offline, skipping token check");
        updateNetworkStatus(false);
        return;
      }

      const tokens = getCognitoTokensFromCookies();

      if (!tokens.accessToken) {
        return; // No token, user not logged in
      }

      // Get detailed token info for debugging
      const tokenInfo = getTokenExpiryInfo(tokens.accessToken);
      console.log("🔍 Token status:", {
        isExpired: tokenInfo.isExpired,
        expiresAt: tokenInfo.expiresAt?.toISOString(),
        timeUntilExpiryMinutes: tokenInfo.timeUntilExpiryMinutes,
        willExpireSoon: shouldRefreshSoon(tokens.accessToken),
        refreshBuffer: `${refreshBuffer} minutes`,
        networkStatus: networkStatus.isOnline ? "online" : "offline",
        consecutiveFailures: networkStatus.consecutiveFailures,
      });

      const isExpired = tokenInfo.isExpired;
      const willExpireSoon = shouldRefreshSoon(tokens.accessToken);

      // If token is already expired, refresh immediately
      if (isExpired) {
        console.log("🔄 Token expired, triggering background refresh...");
        await performSilentRefresh();
        return;
      }

      // If token will expire soon, refresh proactively
      if (willExpireSoon) {
        console.log(
          "🔄 Token will expire soon, triggering proactive refresh..."
        );
        await performSilentRefresh();
      } else {
        console.log("✅ Token is valid, no refresh needed");
      }
    } catch (error) {
      console.error("❌ Error checking tokens in background:", error);
      updateNetworkStatus(networkStatus.isOnline, true);
    }
  };

  // Retry failed token refresh with exponential backoff
  const retryFailedRefresh = async (): Promise<void> => {
    if (networkStatus.consecutiveFailures === 0) {
      return; // No failures to retry
    }

    const maxRetryAttempts = Math.min(networkStatus.consecutiveFailures, 5);
    const baseDelay =
      retryDelay * Math.pow(2, networkStatus.consecutiveFailures - 1);
    const delay = Math.min(baseDelay, maxRetryDelay);

    console.log(
      `🔄 Retrying failed token refresh in ${delay}ms (attempt ${networkStatus.consecutiveFailures}/${maxRetryAttempts})`
    );

    retryTimeoutRef.current = setTimeout(async () => {
      await checkAndRefreshTokens();
    }, delay);
  };

  // Set up background token checking
  useEffect(() => {
    console.log("🔧 Setting up background token refresh:", {
      checkInterval: `${checkInterval}ms`,
      refreshBuffer: `${refreshBuffer} minutes`,
      maxRetries,
      retryDelay: `${retryDelay}ms`,
      maxRetryDelay: `${maxRetryDelay}ms`,
      enableNetworkAwareness,
    });

    // Set up interval to check tokens periodically
    intervalRef.current = setInterval(() => {
      checkAndRefreshTokens();
    }, checkInterval);

    // Initial check
    checkAndRefreshTokens();

    return () => {
      console.log("🧹 Cleaning up background token refresh interval");
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [
    checkInterval,
    refreshBuffer,
    maxRetries,
    retryDelay,
    maxRetryDelay,
    enableNetworkAwareness,
  ]);

  // Retry failed refreshes when network status changes
  useEffect(() => {
    if (networkStatus.consecutiveFailures > 0 && networkStatus.isOnline) {
      retryFailedRefresh();
    }
  }, [networkStatus.isOnline, networkStatus.consecutiveFailures]);

  // Check tokens when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      console.log("👁️ Window focus detected, checking tokens...");
      checkAndRefreshTokens();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [checkAndRefreshTokens]);

  // Listen for network status changes
  useEffect(() => {
    if (!enableNetworkAwareness) return;

    const handleOnline = () => {
      console.log("🌐 Network came online");
      updateNetworkStatus(true);
      // Retry any failed refreshes when network comes back
      if (networkStatus.consecutiveFailures > 0) {
        retryFailedRefresh();
      }
    };

    const handleOffline = () => {
      console.log("🌐 Network went offline");
      updateNetworkStatus(false);
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [enableNetworkAwareness, networkStatus.consecutiveFailures]);

  // Listen for storage changes (tokens updated in another tab)
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key && event.key.includes("cognito")) {
        console.log("🔄 Token change detected in another tab:", event.key);
        setTimeout(() => {
          checkAndRefreshTokens();
        }, 1000);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [checkAndRefreshTokens]);

  return {
    performSilentRefresh,
    checkAndRefreshTokens,
    networkStatus,
    retryFailedRefresh,
  };
};
