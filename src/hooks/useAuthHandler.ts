import { useAuth, AuthR<PERSON>ult } from '@/hooks/useAuth';
import { LoginFormData, MFAFormData } from '@/types/auth';
import { useRouter } from 'next/navigation';

export interface AuthHandler {
  handleLogin: (loginData: LoginFormData) => Promise<void>;
  handleMFASetup: (result: AuthResult) => Promise<void>;
  handleConfirmMFA: (mfaData: MFAFormData) => Promise<void>;
  handleTotpSetup: (code: string) => Promise<void>;
}

export const useAuthHandler = (): AuthHandler => {
  const { handleSignIn, handleMFAConfirmation, handleTOTPSetup, setupTOTPAfterLogin } = useAuth();
  const router = useRouter();

  const handleLogin = async (loginData: LoginFormData): Promise<void> => {
    const result = await handleSignIn(loginData);
    
    if (result.status === "SIGNED_IN") {
      // Check for pending TOTP setup
      const pendingTOTP = localStorage.getItem('pendingTOTPSetup');
      if (pendingTOTP) {
        try {
          const totpData = JSON.parse(pendingTOTP);
          if (totpData.username === loginData.username) {
            localStorage.removeItem('pendingTOTPSetup');
            await setupTOTPAfterLogin(loginData.username);
            return;
          }
        } catch (e) {
          console.error('Error parsing pending TOTP data:', e);
          localStorage.removeItem('pendingTOTPSetup');
        }
      }
      router.push("/dashboard");
    }
  };

  const handleMFASetup = async (result: AuthResult): Promise<void> => {
    if (result.status === "SIGNED_IN") {
      router.push("/dashboard");
    } else if (result.status === "MFA_REQUIRED") {
      const step = result.nextStep?.signInStep;
      if (step === "CONFIRM_SIGN_IN_WITH_TOTP_CODE") {
        // Handle TOTP code verification
        return;
      } else if (step === "CONFIRM_SIGN_IN_WITH_SMS_CODE") {
        // Handle SMS code verification
        return;
      } else if (step === "CONTINUE_SIGN_IN_WITH_TOTP_SETUP") {
        // Handle TOTP setup step
        return;
      } else {
        throw new Error("Unsupported MFA step: " + step);
      }
    }
  };

  const handleConfirmMFA = async (mfaData: MFAFormData): Promise<void> => {
    await handleMFAConfirmation(mfaData);
  };

  const handleTotpSetup = async (code: string): Promise<void> => {
    await handleTOTPSetup(code);
  };

  return {
    handleLogin,
    handleMFASetup,
    handleConfirmMFA,
    handleTotpSetup,
  };
};
