import { useState, useCallback } from 'react';
import { LoginFormData, MFAFormData } from '@/types/auth';

export interface LoginState {
  loginData: LoginFormData;
  mfaData: MFAFormData;
  mfaRequired: boolean;
  mfaType: "TOTP" | "SMS" | null;
  totpSetup: { required: boolean; sharedSecret?: string };
}

export const useLoginState = () => {
  const [loginData, setLoginData] = useState<LoginFormData>({ username: "", password: "" });
  const [mfaData, setMfaData] = useState<MFAFormData>({ code: "", rememberDevice: false });
  const [mfaRequired, setMfaRequired] = useState(false);
  const [mfaType, setMfaType] = useState<"TOTP" | "SMS" | null>(null);
  const [totpSetup, setTotpSetup] = useState<{ required: boolean; sharedSecret?: string }>({ required: false });

  const resetMfaData = useCallback(() => {
    setMfaData({ code: "", rememberDevice: false });
  }, []);

  const resetAllState = useCallback(() => {
    setLoginData({ username: "", password: "" });
    setMfaData({ code: "", rememberDevice: false });
    setMfaRequired(false);
    setMfaType(null);
    setTotpSetup({ required: false });
  }, []);

  const handleMfaStep = useCallback((step: string, result?: any) => {
    switch (step) {
      case "CONFIRM_SIGN_IN_WITH_TOTP_CODE":
        setMfaType("TOTP");
        setMfaRequired(true);
        setTotpSetup({ required: false });
        break;
      case "CONFIRM_SIGN_IN_WITH_SMS_CODE":
        setMfaType("SMS");
        setMfaRequired(true);
        setTotpSetup({ required: false });
        break;
      case "CONTINUE_SIGN_IN_WITH_TOTP_SETUP":
        const sharedSecret = result?.nextStep?.totpSetupDetails?.sharedSecret;
        if (sharedSecret) {
          setTotpSetup({ required: true, sharedSecret });
          setMfaRequired(false);
        }
        break;
      default:
        console.warn("Unsupported MFA step:", step);
    }
  }, []);

  return {
    loginData,
    setLoginData,
    mfaData,
    setMfaData,
    mfaRequired,
    setMfaRequired,
    mfaType,
    setMfaType,
    totpSetup,
    setTotpSetup,
    resetMfaData,
    resetAllState,
    handleMfaStep,
  };
};
