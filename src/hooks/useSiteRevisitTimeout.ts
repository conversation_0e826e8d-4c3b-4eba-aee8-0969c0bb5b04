"use client";

import { useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  checkLastActivityTimeout,
  updateLastActivity,
  clearLastActivity,
  performCompleteLogout,
} from "@/utils/auth";

interface SiteRevisitTimeoutOptions {
  timeoutHours?: number; // Timeout in hours (default: 1)
  enabled?: boolean; // Enable/disable the feature (default: true)
  checkOnMount?: boolean; // Check timeout when component mounts (default: true)
  checkOnFocus?: boolean; // Check timeout when window gains focus (default: true)
  checkOnVisibilityChange?: boolean; // Check timeout when tab becomes visible (default: true)
}

/**
 * Hook that handles automatic logout when users revisit the site after being away
 * for more than a specified time period (default: 1 hour)
 */
export const useSiteRevisitTimeout = (
  options: SiteRevisitTimeoutOptions = {}
) => {
  const {
    timeoutHours = 1, // 1 hour default
    enabled = true,
    checkOnMount = true,
    checkOnFocus = true,
    checkOnVisibilityChange = true,
  } = options;

  const router = useRouter();

  // Check if user should be logged out due to inactivity timeout
  const checkTimeoutAndLogout = useCallback(async () => {
    // console.log("🔍 DEBUG: checkTimeoutAndLogout called with options:", {
    //   enabled,
    //   timeoutHours,
    //   checkOnMount,
    //   checkOnFocus,
    //   checkOnVisibilityChange,
    // });

    if (!enabled) {
      console.log("🔍 Site revisit timeout disabled, skipping check");
      return;
    }

    try {
      console.log("🔍 Checking site revisit timeout...");

      const shouldLogout = checkLastActivityTimeout(timeoutHours);

      console.log("🔍 DEBUG: shouldLogout result:", shouldLogout);

      if (shouldLogout) {
        console.log(
          `⏰ User away for more than ${timeoutHours} hour(s), logging out...`
        );

        performCompleteLogout();
        clearLastActivity();


        router.push("/");
      } else {
        console.log(
          "✅ User activity within timeout period, continuing session"
        );

      }
    } catch (error) {

    }
  }, [enabled, timeoutHours, router]);

  // Check timeout when component mounts
  useEffect(() => {
    console.log("🔍 DEBUG: useEffect mount triggered with:", {
      checkOnMount,
      enabled,
    });

    if (checkOnMount && enabled) {
      console.log("🚀 Checking site revisit timeout on mount...");
      checkTimeoutAndLogout();
    }
  }, [checkOnMount, enabled, checkTimeoutAndLogout]);

  // Check timeout when window gains focus
  useEffect(() => {
    if (!checkOnFocus || !enabled) return;

    const handleFocus = () => {
      console.log("👁️ Window focus detected, checking site revisit timeout...");
      checkTimeoutAndLogout();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [checkOnFocus, enabled, checkTimeoutAndLogout]);

  // Check timeout when tab becomes visible
  useEffect(() => {
    if (!checkOnVisibilityChange || !enabled) return;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("👁️ Tab became visible, checking site revisit timeout...");
        checkTimeoutAndLogout();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [checkOnVisibilityChange, enabled, checkTimeoutAndLogout]);

  // Update last activity when user is active
  const updateActivity = useCallback(() => {
    if (enabled) {
      updateLastActivity();
    }
  }, [enabled]);

  // Expose the check function globally for debugging
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).debugSiteRevisitTimeout = {
        checkTimeoutAndLogout,
        updateActivity,
        getDebugStatus: () => ({
          enabled,
          timeoutHours,
          checkOnMount,
          checkOnFocus,
          checkOnVisibilityChange,
        }),
        manualCheck: () => {
          console.log(
            "🔍 MANUAL CHECK: Triggering site revisit timeout check..."
          );
          checkTimeoutAndLogout();
        },
      };
      console.log(
        "🔍 DEBUG: Site revisit timeout debug functions available on window.debugSiteRevisitTimeout"
      );
    }
  }, [
    checkTimeoutAndLogout,
    updateActivity,
    enabled,
    timeoutHours,
    checkOnMount,
    checkOnFocus,
    checkOnVisibilityChange,
  ]);

  return {
    checkTimeoutAndLogout,
    updateActivity,
    // Debug function to get current status
    getDebugStatus: () => ({
      enabled,
      timeoutHours,
      checkOnMount,
      checkOnFocus,
      checkOnVisibilityChange,
    }),
  };
};
