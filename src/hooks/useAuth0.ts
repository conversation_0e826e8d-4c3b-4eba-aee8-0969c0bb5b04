"use client";

import { useUser, getAccessToken } from "@auth0/nextjs-auth0";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export const useAuth0 = () => {
  const { user, isLoading, error } = useUser();
  const router = useRouter();
  const [accessToken, setAccessToken] = useState<string | null>(null);

  const login = useCallback(() => {
    // Redirect to Auth0 login route
    window.location.href = "/auth/login";
  }, []);

  const logout = useCallback(() => {
    // Redirect to Auth0 logout route
    window.location.href = "/auth/logout";
  }, []);

  const getUserToken = useCallback(async () => {
    try {
      const token = await getAccessToken();
      setAccessToken(token);
      return token;
    } catch (error) {
      console.error("Error getting user access token:", error);
      return null;
    }
  }, []);

  return {
    user,
    isLoading,
    error,
    login,
    logout,
    isAuthenticated: !!user,
    accessToken,
    getUserToken,
  };
};
