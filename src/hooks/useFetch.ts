// hooks/useFetch.ts
import { useEffect, useState, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import axios, { AxiosRequestConfig, AxiosResponse, CancelTokenSource, AxiosError } from 'axios';
import { getAuthToken } from '@/utils/auth';
import { createAxiosInstance } from '@/services/apiClient';

// Types
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

interface BaseUseFetchOptions {
  headers?: Record<string, string>;
  isCredentials?: boolean;
  timeout?: number;
  params?: Record<string, any>;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  baseURL?: string;
}

interface UseFetchOptions extends BaseUseFetchOptions {
  method?: HttpMethod;
  body?: any;
  immediate?: boolean;
}

interface UseFetchState<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
  status: number | null;
}

interface UseFetchError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

// Enhanced options for TanStack Query integration
interface UseFetchQueryOptions<T = any> extends BaseUseFetchOptions {
  queryKey?: (string | number | boolean)[];
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retry?: boolean | number | ((failureCount: number, error: any) => boolean);
  retryDelay?: number | ((retryAttempt: number) => number);
  refetchOnWindowFocus?: boolean;
  refetchOnMount?: boolean;
  refetchOnReconnect?: boolean;
  select?: (data: any) => T;
  initialData?: T;
}

interface UseFetchMutationOptions<T = any, TVariables = any> extends BaseUseFetchOptions {
  method?: Exclude<HttpMethod, 'GET' | 'HEAD'>;
  onSuccess?: (data: T, variables: TVariables) => void;
  onError?: (error: UseFetchError, variables: TVariables) => void;
  onSettled?: (data: T | undefined, error: UseFetchError | null, variables: TVariables) => void;
  retry?: boolean | number | ((failureCount: number, error: any) => boolean);
  retryDelay?: number | ((retryAttempt: number) => number);
  // Optimistic updates
  onMutate?: (variables: TVariables) => Promise<any> | any;
  invalidateQueries?: (string | number | boolean)[][];
  updateQueries?: Array<{
    queryKey: (string | number | boolean)[];
    updater: (oldData: any, newData: T) => any;
  }>;
}


// Error handling utility
const handleAxiosError = (err: any): UseFetchError => {
  let errorDetails: UseFetchError = {
    message: 'Unknown error occurred',
  };

  if (axios.isAxiosError(err)) {
    const axiosError = err as AxiosError;
    errorDetails = {
      message: axiosError.message,
      status: axiosError.response?.status,
      code: axiosError.code,
      response: axiosError.response?.data,
    };

    // Custom error messages based on status codes
    if (axiosError.response?.status === 401) {
      errorDetails.message = 'Unauthorized - Please check your credentials';
    } else if (axiosError.response?.status === 403) {
      errorDetails.message = 'Forbidden - You do not have permission';
    } else if (axiosError.response?.status === 404) {
      errorDetails.message = 'Resource not found';
    } else if (axiosError.response?.status && axiosError.response.status >= 500) {
      errorDetails.message = 'Server error - Please try again later';
    } else if (axiosError.code === 'ECONNABORTED') {
      errorDetails.message = 'Request timeout - Please try again';
    } else if (axiosError.response?.data && typeof axiosError.response.data === 'object' && 'message' in axiosError.response.data) {
      errorDetails.message = (axiosError.response.data as any)?.message || 'An error occurred';
    }
  } else {
    errorDetails.message = err.message || 'Network error occurred';
  }

  return errorDetails;
};

// Build request config utility
const buildRequestConfig = (
  url: string,
  options: UseFetchOptions | UseFetchMutationOptions,
  method: HttpMethod = 'GET',
  body?: any
): AxiosRequestConfig => {
  const {
    headers: customHeaders = {},
    isCredentials = false,
    timeout = 10000,
    params,
    responseType = 'json',
    baseURL,
  } = options;

  const requestConfig: AxiosRequestConfig = {
    method,
    url: url.startsWith('http') ? url : url,
    headers: { ...customHeaders },
    timeout,
    params,
    responseType,
  };

  // Set baseURL if provided
  if (baseURL) {
    requestConfig.baseURL = baseURL;
  }

  // Add authorization header if credentials are required
  if (isCredentials) {
    const token = getAuthToken();
    if (token) {
      requestConfig.headers = {
        ...requestConfig.headers,
        Authorization: `Bearer ${token}`,
      };
    }
  }

  // Add body for methods that support it
  if (method !== 'GET' && method !== 'HEAD' && body !== undefined) {
    if (body instanceof FormData) {
      requestConfig.data = body;
      // Remove Content-Type for FormData (let browser set it)
      delete requestConfig.headers!['Content-Type'];
    } else {
      requestConfig.data = body;
    }
  }

  return requestConfig;
};

// Main useFetch hook (legacy/manual mode)
export function useFetch<T = any>(
  url: string,
  options: UseFetchOptions = {}
) {
  const {
    method = 'GET',
    body,
    immediate = true,
    ...restOptions
  } = options;

  const [state, setState] = useState<UseFetchState<T>>({
    data: null,
    error: null,
    isLoading: false,
    status: null,
  });

  const isMountedRef = useRef(true);
  const cancelTokenRef = useRef<CancelTokenSource | null>(null);
  const axiosInstanceRef = useRef(createAxiosInstance(options.baseURL));

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Component unmounted');
      }
    };
  }, []);

  const fetchData = useCallback(
    async (overrideOptions?: Partial<UseFetchOptions>) => {
      // Cancel previous request if still pending
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('New request initiated');
      }

      // Create new cancel token for this request
      const cancelTokenSource = axios.CancelToken.source();
      cancelTokenRef.current = cancelTokenSource;

      if (!isMountedRef.current) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        const finalMethod = overrideOptions?.method || method;
        const finalBody = overrideOptions?.body || body;
        const mergedOptions = { ...restOptions, ...overrideOptions };

        const requestConfig = buildRequestConfig(url, mergedOptions, finalMethod, finalBody);
        requestConfig.cancelToken = cancelTokenSource.token;

        const response: AxiosResponse<T> = await axiosInstanceRef.current.request(requestConfig);

        if (axios.isCancel(response) || !isMountedRef.current) {
          return;
        }

        setState({
          data: response.data,
          error: null,
          isLoading: false,
          status: response.status,
        });

        return response.data;
      } catch (err: any) {
        if (axios.isCancel(err) || !isMountedRef.current) {
          return;
        }

        const errorDetails = handleAxiosError(err);

        setState({
          data: null,
          error: errorDetails.message,
          isLoading: false,
          status: errorDetails.status || null,
        });

        throw errorDetails;
      }
    },
    [url, method, body, restOptions]
  );

  // Effect for immediate fetching
  useEffect(() => {
    if (immediate && method === 'GET') {
      fetchData();
    }
  }, [immediate, method, url]); // Added url to dependencies

  const abort = useCallback(() => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.cancel('Request aborted by user');
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  const reset = useCallback(() => {
    setState({
      data: null,
      error: null,
      isLoading: false,
      status: null,
    });
  }, []);

  return {
    data: state.data,
    error: state.error,
    isLoading: state.isLoading,
    status: state.status,
    refetch: () => fetchData(),
    send: (sendOptions?: Partial<UseFetchOptions>) => fetchData(sendOptions),
    abort,
    reset,
  };
}

// TanStack Query integrated hooks
export function useFetchQuery<T = any>(
  url: string,
  options: UseFetchQueryOptions<T> = {}
) {
  const {
    queryKey = [url],
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    gcTime = 10 * 60 * 1000, // 10 minutes
    retry = 1,
    retryDelay,
    refetchOnWindowFocus = false,
    refetchOnMount = true,
    refetchOnReconnect = true,
    select,
    initialData,
    ...restOptions
  } = options;

  const axiosInstance = useRef(createAxiosInstance(options.baseURL)).current;

  const queryFn = async (): Promise<T> => {
    try {
      const requestConfig = buildRequestConfig(url, restOptions, 'GET');
      const response: AxiosResponse<T> = await axiosInstance.request(requestConfig);
      return response.data;
    } catch (err) {
      const errorDetails = handleAxiosError(err);
      throw errorDetails;
    }
  };

  const queryOptions: UseQueryOptions<T, UseFetchError> = {
    queryKey,
    queryFn,
    enabled,
    staleTime,
    gcTime,
    retry,
    retryDelay,
    refetchOnWindowFocus,
    refetchOnMount,
    refetchOnReconnect,
    select,
    initialData,
  };

  return useQuery(queryOptions);
}

export function useFetchMutation<T = any, TVariables = any>(
  url: string,
  options: UseFetchMutationOptions<T, TVariables> = {}
) {
  const {
    method = 'POST',
    onSuccess,
    onError,
    onSettled,
    onMutate,
    retry = 1,
    retryDelay,
    invalidateQueries = [],
    updateQueries = [],
    ...restOptions
  } = options;

  const axiosInstance = useRef(createAxiosInstance(options.baseURL)).current;
  const queryClient = useQueryClient();

  const mutationFn = async (variables: TVariables): Promise<T> => {
    try {
      const requestConfig = buildRequestConfig(url, restOptions, method, variables);
      const response: AxiosResponse<T> = await axiosInstance.request(requestConfig);
      return response.data;
    } catch (err) {
      const errorDetails = handleAxiosError(err);
      throw errorDetails;
    }
  };

  const mutationOptions: UseMutationOptions<T, UseFetchError, TVariables> = {
    mutationFn,
    retry,
    retryDelay,
    onMutate,
    onSuccess: (data, variables, context) => {
      // Handle query invalidation
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });

      // Handle query updates
      updateQueries.forEach(({ queryKey, updater }) => {
        queryClient.setQueryData(queryKey, (oldData: any) => updater(oldData, data));
      });

      onSuccess?.(data, variables);
    },
    onError: (error, variables, context) => {
      onError?.(error, variables);
    },
    onSettled: (data, error, variables, context) => {
      onSettled?.(data, error, variables);
    },
  };

  return useMutation(mutationOptions);
}

// Specialized query hooks
export function useGet<T = any>(
  url: string, 
  options: UseFetchQueryOptions<T> = {}
) {
  return useFetchQuery<T>(url, options);
}

export function usePost<T = any, TVariables = any>(
  url: string,
  options: Omit<UseFetchMutationOptions<T, TVariables>, 'method'> = {}
) {
  return useFetchMutation<T, TVariables>(url, { ...options, method: 'POST' });
}

export function usePut<T = any, TVariables = any>(
  url: string,
  options: Omit<UseFetchMutationOptions<T, TVariables>, 'method'> = {}
) {
  return useFetchMutation<T, TVariables>(url, { ...options, method: 'PUT' });
}

export function usePatch<T = any, TVariables = any>(
  url: string,
  options: Omit<UseFetchMutationOptions<T, TVariables>, 'method'> = {}
) {
  return useFetchMutation<T, TVariables>(url, { ...options, method: 'PATCH' });
}

export function useDelete<T = any, TVariables = any>(
  url: string,
  options: Omit<UseFetchMutationOptions<T, TVariables>, 'method'> = {}
) {
  return useFetchMutation<T, TVariables>(url, { ...options, method: 'DELETE' });
}

// Utility hook for prefetching
export function usePrefetch() {
  const queryClient = useQueryClient();
  const axiosInstance = useRef(createAxiosInstance()).current;

  const prefetch = useCallback(async <T = any>(
    url: string,
    options: UseFetchQueryOptions<T> = {}
  ) => {
    const { queryKey = [url], staleTime = 5 * 60 * 1000, ...restOptions } = options;

    const queryFn = async (): Promise<T> => {
      const requestConfig = buildRequestConfig(url, restOptions, 'GET');
      const response: AxiosResponse<T> = await axiosInstance.request(requestConfig);
      return response.data;
    };

    return queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime,
    });
  }, [queryClient, axiosInstance]);

  return { prefetch };
}

// Export types for external use
export type {
  UseFetchOptions,
  UseFetchQueryOptions,
  UseFetchMutationOptions,
  UseFetchError,
  UseFetchState,
  HttpMethod,
};