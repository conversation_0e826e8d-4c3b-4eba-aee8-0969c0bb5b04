import { Member } from "@/types/member";
import { apiClient } from "./apiClient";
import { CreateAdminUserResponse } from "@/types/adminUser";

export const membersApiService = {
  async getMembers(): Promise<{members: Member[] }& CreateAdminUserResponse> {
    const response = await apiClient.get<{members: Member[]} & CreateAdminUserResponse>("/api/members");
    return {
      members: response.data.members,
      message: response.data.message,
      status_code: response.data.status_code,
      success: response.data.success,
    };
  },

  async getMember(id: string): Promise<Member> {
    const response = await apiClient.get<Member>(`/api/members/uuid/${id}`);
    return response.data;
  },

  async createMember(data: any): Promise<Member> {
    const response = await apiClient.post<Member>("/api/members", data);
    return response.data;
  },

  async updateMember(id: string, data: any): Promise<Member> {
    const response = await apiClient.put<Member>(`/api/members/${id}`, data);
    return response.data;
  },

  async deleteMember(id: string): Promise<void> {
    await apiClient.delete(`/api/members/${id}`);
  },
};
