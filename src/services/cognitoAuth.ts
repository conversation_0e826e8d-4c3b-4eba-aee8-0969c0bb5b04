import {
  signIn,
  signUp,
  confirmSignUp,
  signOut,
  getCurrentUser,
  resendSignUpCode,
  type AuthUser,
  confirmSignIn,
  rememberDevice,
} from "aws-amplify/auth";
import { LoginFormData, User, UserRole, Permission } from "@/types/auth";

export interface CognitoSignUpParams {
  username: string;
  password: string;
  email: string;
  attributes?: Record<string, string>;
}

export interface CognitoConfirmSignUpParams {
  username: string;
  confirmationCode: string;
}

export interface CognitoUser {
  userId: string;
  username: string;
  attributes: {
    email?: string;
    email_verified?: string;
    given_name?: string;
    family_name?: string;
    [key: string]: string | undefined;
  };
}

// Convert Cognito user to our User type
const mapCognitoUserToUser = (cognitoUser: AuthUser): User => {
  const email = cognitoUser.signInDetails?.loginId || "";

  return {
    id: cognitoUser.userId,
    email: email,
    username: cognitoUser.username || email,
    firstName: undefined, // These would need to be fetched from user attributes
    lastName: undefined,
    roles: [UserRole.MEMBER], // Default role, can be customized based on your needs
    permissions: [Permission.READ_DASHBOARD], // Default permissions
    mfaEnabled: false, // This would need to be determined from Cognito MFA settings
    emailVerified: true, // Assume verified if user is signed in
    createdAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
  };
};

export const cognitoAuthService = {
  // Sign in user
  signIn: async ({
    username,
    password,
  }: {
    username: string;
    password: string;
  }) => {
    const response = await signIn({ username, password });

    const { isSignedIn, nextStep } = response;

    if (isSignedIn) {
      return { status: "SIGNED_IN" };
    }

    return {
      status: "MFA_REQUIRED",
      nextStep,
    };
  },

  confirmMFA: async (code: string, remember = false) => {
    const result = await confirmSignIn({ challengeResponse: code });
    if (remember) {
      await rememberDevice(); // optional
    }
    return result;
  },

  // Sign up user
  async signUp(
    params: CognitoSignUpParams
  ): Promise<{ userId: string; isConfirmed: boolean }> {
    try {
      const result = await signUp({
        username: params.username,
        password: params.password,
        options: {
          userAttributes: {
            email: params.email,
            ...params.attributes,
          },
        },
      });

      return {
        userId: result.userId || "",
        isConfirmed: result.isSignUpComplete,
      };
    } catch (error: any) {
      console.error("Sign up error:", error);
      throw new Error(error.message || "Failed to sign up");
    }
  },

  // Confirm sign up
  async confirmSignUp(params: CognitoConfirmSignUpParams): Promise<void> {
    try {
      await confirmSignUp({
        username: params.username,
        confirmationCode: params.confirmationCode,
      });
    } catch (error: any) {
      console.error("Confirm sign up error:", error);
      throw new Error(error.message || "Failed to confirm sign up");
    }
  },

  // Resend confirmation code
  async resendConfirmationCode(username: string): Promise<void> {
    try {
      await resendSignUpCode({ username });
    } catch (error: any) {
      console.error("Resend confirmation code error:", error);
      throw new Error(error.message || "Failed to resend confirmation code");
    }
  },

  // Sign out user
  async signOut(): Promise<void> {
    try {
      await signOut();
    } catch (error: any) {
      console.error("Sign out error:", error);
      throw new Error(error.message || "Failed to sign out");
    }
  },

  // Get current authenticated user
  async getCurrentUser(): Promise<User | null> {
    try {
      const currentUser = await getCurrentUser();
      return mapCognitoUserToUser(currentUser);
    } catch (error) {
      console.error("Get current user error:", error);
      return null;
    }
  },

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      await getCurrentUser();
      return true;
    } catch (error) {
      return false;
    }
  },
};
