import { IModules, ModuleType } from "@/store/module/redux";
import { apiClient } from "./apiClient";
import { CreateAdminUserResponse } from "@/types/adminUser";

export const ModuleApi = {
  async fetch(): Promise<{ modules: ModuleType[] } & CreateAdminUserResponse> {
    try {
      const response = await apiClient.get("/api/rbac/modules");
      return {
        modules: response.data.modules,
        ...response.data,
      };
    } catch (error: any) {
      console.error("Module API fetch error:", error);
      throw error;
    }
  },
};
