import { getAuthToken, getCognitoTokensFromCookies } from "@/utils/auth";
import axios from "axios";

export const createAxiosInstance = (baseURL?: string) => {
  const instance = axios.create({
    baseURL: baseURL || process.env.NEXT_PUBLIC_BACKEND_API_URL || "",
    timeout: 20000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor for auth tokens and global config
  instance.interceptors.request.use(
    (config) => {
      // Add timestamp to prevent caching for GET requests
      if (config.method === "get" && !config.params) {
        config.params = {};
      }
      if (config.method === "get") {
        config.params._t = Date.now();
      }

      // Add explicit CORS headers
      if (typeof window !== "undefined") {
        config.headers.Origin = window.location.origin;
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for global error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // Global error handling logic can be added here
      return Promise.reject(error);
    }
  );

  return instance;
};

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  try {
    // Try to get token from cookies first (synced from Cognito)
    const cognitoTokens = getCognitoTokensFromCookies();
    if (
      cognitoTokens.accessToken &&
      cognitoTokens.accessToken.trim() !== "" &&
      cognitoTokens.accessToken !== "undefined" &&
      cognitoTokens.accessToken !== "null"
    ) {
      return cognitoTokens.accessToken;
    }

    // Fallback to legacy token
    const legacyToken = getAuthToken();
    if (
      legacyToken &&
      legacyToken.trim() !== "" &&
      legacyToken !== "undefined" &&
      legacyToken !== "null"
    ) {
      return legacyToken;
    }

    return null;
  } catch (error) {
    console.error("Error getting access token:", error);
    return null;
  }
}

// Create axios instance using the shared utility
export const apiClient = createAxiosInstance();

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = getAccessToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    console.log(
      `🔑 API Request: ${config.method?.toUpperCase()} ${
        config.url
      } - Token: ${token.substring(0, 20)}...`
    );
  } else {
    console.warn(
      `⚠️ API Request: ${config.method?.toUpperCase()} ${
        config.url
      } - No token available`
    );
  }
  return config;
});
