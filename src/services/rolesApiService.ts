import { Role, CreateRoleData, UpdateRoleData, AllRolesPermissionsResponse } from "@/types/role";
import { apiClient } from "./apiClient";



export const rolesApiService = {
  async getRoles(): Promise<Role[]> {
    const response = await apiClient.get<Role[]>("/api/rbac/roles");
    return response.data;
  },

  async upsertRole(data: CreateRoleData | UpdateRoleData): Promise<Role> {
    const response = await apiClient.post<Role>(
      "/api/rbac/roles/upsert-with-permissions",
      data
    );
    return response.data;
  },

  async deleteRole(slug: string): Promise<void> {
    await apiClient.delete(`/api/rbac/roles/${slug}`);
  },

  async bulkDeleteRoles(roleSlugs: string[]): Promise<void> {
    await apiClient.post("/api/rbac/roles/bulk-delete", roleSlugs);
  },

  // Fetch all roles and their permissions
  async getAllRolesPermissions(): Promise<AllRolesPermissionsResponse> {
    const response = await apiClient.get<AllRolesPermissionsResponse>("/api/rbac/permissions");
    return response.data;
  },
};
