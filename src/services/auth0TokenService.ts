import axios from "axios";

export interface Auth0TokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  scope?: string;
}

export interface Auth0TokenRequest {
  grant_type: string;
  client_id: string;
  client_secret: string;
  audience: string;
}

/**
 * Get Auth0 access token using client credentials flow
 */
export const getAuth0AccessToken = async (): Promise<Auth0TokenResponse> => {
  try {
    const options = {
      method: "POST",
      url: `https://${process.env.AUTH0_DOMAIN}/oauth/token`,
      headers: {
        "content-type": "application/x-www-form-urlencoded",
      },
      data: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: process.env.AUTH0_CLIENT_ID!,
        client_secret: process.env.AUTH0_CLIENT_SECRET!,
        audience: process.env.AUTH0_AUDIENCE || process.env.AUTH0_CLIENT_ID!,
      }),
    };

    const response = await axios.request(options);
    return response.data;
  } catch (error) {
    console.error("Error getting Auth0 access token:", error);
    throw new Error("Failed to get Auth0 access token");
  }
};

/**
 * Get Auth0 access token for client-side use
 */
export const getAuth0TokenClientSide =
  async (): Promise<Auth0TokenResponse> => {
    try {
      const response = await fetch("/api/auth0/token", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to get Auth0 token");
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting Auth0 token:", error);
      throw new Error("Failed to get Auth0 token");
    }
  };

/**
 * Get user's Auth0 token from client side and validate it
 */
export const getUserAuth0Token = async (
  userToken: string
): Promise<Auth0TokenResponse> => {
  try {
    const response = await fetch("/api/auth0/token", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userToken}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to validate user token");
    }

    return await response.json();
  } catch (error) {
    console.error("Error validating user token:", error);
    throw new Error("Failed to validate user token");
  }
};
