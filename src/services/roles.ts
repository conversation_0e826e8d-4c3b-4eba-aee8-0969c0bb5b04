import { useGet, usePost, useDelete } from "@/hooks/useFetch";
import { Role, CreateRoleData, UpdateRoleData } from "@/types/role";

// List all roles
export function useRoles(options = {}) {
  return useGet<Role[]>(
    `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/rbac/roles`,
    options
  );
}

// Get a single role by ID
export function useRole(roleId: string, options = {}) {
  return useGet<Role>(
    `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/rbac/roles/${roleId}`,
    options
  );
}

// Upsert (create or update) a role with permissions
export function useUpsertRoleWithPermissions(options = {}) {
  return usePost<Role, CreateRoleData | UpdateRoleData>(
    `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/rbac/roles/upsert-with-permissions`,
    options
  );
}

// Delete a role by ID
export function useDeleteRole(options = {}) {
  return useDelete<void, string>(
    `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/rbac/roles`,
    options
  );
}

// Bulk delete roles
export function useBulkDeleteRoles(options = {}) {
  return usePost<void, string[]>(
    `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/rbac/roles/bulk-delete`,
    options
  );
}
