import { User } from "@/types/auth";
import { LogEntry } from "@/types/log";
import { logApiService } from "./logApiService";
import { apiClient } from "./apiClient";

export interface DashboardStats {
  totalMembers: number;
  verifiedMembers: number;
  activeMembers: number;
  recentRegistrations: number;
  totalActivities: number;
  memberGrowth: number; // percentage
  activityGrowth: number; // percentage
}

export interface ActivityItem {
  id: string;
  type:
    | "member_joined"
    | "event_registered"
    | "profile_updated"
    | "login"
    | "password_changed";
  title: string;
  description: string;
  timestamp: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  href: string;
  color: "primary" | "secondary" | "success" | "warning" | "error";
  role?: "super_admin";
}

export class DashboardService {
  // Real API calls for dashboard stats
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Fetch verified members and total members in parallel
      const [verifiedResponse, totalResponse] = await Promise.all([
        apiClient.get("/api/member-verification/total-verified-members"),
        apiClient.get("/api/members/total-member-count"),
      ]);

      const verifiedMembers = verifiedResponse.data.totalCount || 0;
      const totalMembers = totalResponse.data.totalCount || 0;

      // For now, we'll keep some mock data for other stats
      // These can be replaced with real APIs when available
      return {
        totalMembers,
        verifiedMembers,
        activeMembers: Math.floor(totalMembers * 0.7), // Estimate 70% active
        recentRegistrations: Math.floor(totalMembers * 0.02), // Estimate 2% recent
        totalActivities: Math.floor(totalMembers * 0.5), // Estimate 50% have activities
        memberGrowth: 12.5, // Mock growth percentage
        activityGrowth: 8.3, // Mock activity growth
      };
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      // Return fallback data if API calls fail
      return {
        totalMembers: 0,
        verifiedMembers: 0,
        activeMembers: 0,
        recentRegistrations: 0,
        totalActivities: 0,
        memberGrowth: 0,
        activityGrowth: 0,
      };
    }
  }

  async getRecentLogs(limit: number = 10): Promise<LogEntry[]> {
    try {
      // Fetch recent logs with pagination
      const response = await logApiService.getLogs({
        page: 1,
        page_size: limit,
        sort_order: "desc", // Get newest first
      });

      return response.logs || [];
    } catch (error) {
      console.error("Error fetching recent logs:", error);
      return [];
    }
  }

  async getRecentActivities(limit: number = 10): Promise<ActivityItem[]> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    const activities: ActivityItem[] = [
      {
        id: "1",
        type: "member_joined",
        title: "New Member Joined",
        description: "John Smith from TechCorp joined the chamber",
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        userId: "user1",
        userName: "John Smith",
        userAvatar: "/api/avatars/user1.jpg",
      },
      {
        id: "2",
        type: "event_registered",
        title: "Event Registration",
        description: "Sarah Johnson registered for Annual Business Summit",
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
        userId: "user2",
        userName: "Sarah Johnson",
      },
      {
        id: "3",
        type: "profile_updated",
        title: "Profile Updated",
        description: "Mike Wilson updated their business profile",
        timestamp: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
        userId: "user3",
        userName: "Mike Wilson",
      },
      {
        id: "4",
        type: "login",
        title: "User Login",
        description: "Emily Davis logged into the dashboard",
        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
        userId: "user4",
        userName: "Emily Davis",
      },
      {
        id: "5",
        type: "password_changed",
        title: "Password Changed",
        description: "David Brown updated their password",
        timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
        userId: "user5",
        userName: "David Brown",
      },
      {
        id: "6",
        type: "member_joined",
        title: "New Member Joined",
        description: "Lisa Anderson from MarketingPro joined the chamber",
        timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
        userId: "user6",
        userName: "Lisa Anderson",
      },
      {
        id: "7",
        type: "event_registered",
        title: "Event Registration",
        description: "Robert Taylor registered for Networking Mixer",
        timestamp: new Date(Date.now() - 1000 * 60 * 300).toISOString(), // 5 hours ago
        userId: "user7",
        userName: "Robert Taylor",
      },
    ];

    return activities.slice(0, limit);
  }

  async getQuickActions(): Promise<QuickAction[]> {
    return [
      {
        id: "add-member",
        title: "Add Member",
        description: "Register a new chamber member",
        icon: "PersonAdd",
        href: "/members/add",
        color: "primary",
      },
      {
        id: "create-event",
        title: "Create Event",
        description: "Schedule a new chamber event",
        icon: "Event",
        href: "/events/create",
        color: "secondary",
      },
      {
        id: "view-reports",
        title: "View Reports",
        description: "Access analytics and reports",
        icon: "Assessment",
        href: "/reports",
        color: "success",
      },
      {
        id: "manage-admin-users",
        title: "Manage Admin Users",
        description: "Manage system administrators",
        icon: "AdminPanelSettings",
        href: "/admin-users",
        color: "warning",
        role: "super_admin",
      },
      {
        id: "manage-roles",
        title: "Manage Roles",
        description: "Configure user roles and permissions",
        icon: "Security",
        href: "/roles",
        color: "error",
        role: "super_admin",
      },
      {
        id: "manage-settings",
        title: "Manage Settings",
        description: "Update system configuration",
        icon: "Settings",
        href: "/settings",
        color: "warning",
      },
    ];
  }

  async getMemberGrowthData(
    days: number = 30
  ): Promise<{ date: string; count: number }[]> {
    // Mock data for member growth chart
    const data = [];
    const today = new Date();

    for (let i = days; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split("T")[0],
        count: Math.floor(Math.random() * 50) + 1200, // Random count around 1200
      });
    }

    return data;
  }
}

export const dashboardService = new DashboardService();
