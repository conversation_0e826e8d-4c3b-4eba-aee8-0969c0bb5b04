import { apiClient } from "@/services/apiClient";
import {
  Award,
  CreateAwardData,
  UpdateAwardData,
  AwardSearchParams,
  AwardResponse,
  AwardListing,
  CreateAwardListingData,
  UpdateAwardListingData,
  AwardListingResponse,
} from "@/types/award";
import {
  MemberAward,
  CreateMemberAwardData,
  UpdateMemberAwardData,
  MemberAwardResponse,
} from "@/types/organization";

class AwardApiService {
  // Award CRUD Operations
  async getAwards(params?: AwardSearchParams): Promise<AwardResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/awards/awards?${queryParams.toString()}`
    );
    return response.data;
  }

  async getAward(awardId: number): Promise<AwardResponse> {
    const response = await apiClient.get(`/api/awards/awards/${awardId}`);
    return response.data;
  }

  async createAward(data: CreateAwardData): Promise<AwardResponse> {
    const response = await apiClient.post("/api/awards/awards", data);
    return response.data;
  }

  async updateAward(
    awardId: number,
    data: UpdateAwardData
  ): Promise<AwardResponse> {
    const response = await apiClient.put(`/api/awards/awards/${awardId}`, data);
    return response.data;
  }

  async deleteAward(awardId: number): Promise<AwardResponse> {
    const response = await apiClient.delete(`/api/awards/awards/${awardId}`);
    return response.data;
  }

  // Member-specific Award Operations
  async getAwardsByMember(memberId: number): Promise<AwardResponse> {
    const response = await apiClient.get(
      `/api/awards/member/${memberId}/awards`
    );
    return response.data;
  }

  async getAwardsByOrganization(
    organizationId: number
  ): Promise<AwardResponse> {
    const response = await apiClient.get(
      `/api/awards/organization/${organizationId}/awards`
    );
    return response.data;
  }

  async searchAwards(
    query: string,
    params?: AwardSearchParams
  ): Promise<AwardResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/awards/awards/search/${query}?${queryParams.toString()}`
    );
    return response.data;
  }

  // Award Listing CRUD Operations
  async getAwardListings(
    params?: AwardSearchParams
  ): Promise<AwardListingResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/awards/award-listings?${queryParams.toString()}`
    );
    return response.data;
  }

  async getAwardListing(listingId: number): Promise<AwardListingResponse> {
    const response = await apiClient.get(
      `/api/awards/award-listings/${listingId}`
    );
    return response.data;
  }

  async createAwardListing(
    data: CreateAwardListingData
  ): Promise<AwardListingResponse> {
    const response = await apiClient.post("/api/awards/award-listings", data);
    return response.data;
  }

  async updateAwardListing(
    listingId: number,
    data: UpdateAwardListingData
  ): Promise<AwardListingResponse> {
    const response = await apiClient.put(
      `/api/awards/award-listings/${listingId}`,
      data
    );
    return response.data;
  }

  async deleteAwardListing(listingId: number): Promise<AwardListingResponse> {
    const response = await apiClient.delete(
      `/api/awards/award-listings/${listingId}`
    );
    return response.data;
  }

  async searchAwardListings(
    query: string,
    params?: AwardSearchParams
  ): Promise<AwardListingResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/awards/award-listings/search/${query}?${queryParams.toString()}`
    );
    return response.data;
  }

  // Member Award Operations (from Organization API)
  async createMemberAward(
    data: CreateMemberAwardData
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.post("/api/organizations/awards", data);
    return response.data;
  }

  async getMemberAwards(
    memberId?: number,
    organizationId?: number
  ): Promise<MemberAwardResponse> {
    const queryParams = new URLSearchParams();
    if (memberId) queryParams.append("memberId", memberId.toString());
    if (organizationId)
      queryParams.append("organizationId", organizationId.toString());

    const response = await apiClient.get(
      `/api/organizations/awards?${queryParams.toString()}`
    );
    return response.data;
  }

  async getMemberAward(
    memberId: number,
    organizationId: number,
    awardListingElementId: number
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.get(
      `/api/organizations/awards/${memberId}/${organizationId}/${awardListingElementId}`
    );
    return response.data;
  }

  async updateMemberAward(
    memberId: number,
    organizationId: number,
    awardListingElementId: number,
    data: UpdateMemberAwardData
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.put(
      `/api/organizations/awards/${memberId}/${organizationId}/${awardListingElementId}`,
      data
    );
    return response.data;
  }

  async deleteMemberAward(
    memberId: number,
    organizationId: number,
    awardListingElementId: number
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.delete(
      `/api/organizations/awards/${memberId}/${organizationId}/${awardListingElementId}`
    );
    return response.data;
  }

  async getAwardsByOrganizationFromOrg(
    organizationId: number
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.get(
      `/api/organizations/${organizationId}/awards`
    );
    return response.data;
  }

  async getAwardsByMemberFromOrg(
    memberId: number
  ): Promise<MemberAwardResponse> {
    const response = await apiClient.get(
      `/api/organizations/member/${memberId}/awards`
    );
    return response.data;
  }
}

export const awardApiService = new AwardApiService();
