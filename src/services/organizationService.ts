// Organization Service - API calls using existing API client pattern
import { apiClient } from "./apiClient";
import {
  Organization,
  CreateOrganizationData,
  UpdateOrganizationData,
  OrganizationVerifiedData,
  MemberRelation,
  MemberAward,
  PaginationParams,
  SearchParams,
  OrganizationsResponse,
  OrganizationResponse,
  OrganizationWithVerifiedDataResponse,
  OrganizationWithRelationsResponse,
  ApiResponse,
  OrganizationApiResponse,
  OrganizationsApiResponse,
  OrganizationWithVerifiedDataApiResponse,
  OrganizationWithRelationsApiResponse,
  MemberAwardsApiResponse,
  MemberOrganizationsApiResponse,
  OrganizationVerifiedDataApiResponse,
  MemberRelationApiResponse,
  MemberRelationsApiResponse,
  MemberAwardApiResponse,
  OrganizationMember,
} from "../types/organization";

export const organizationService = {
  // Core CRUD Operations
  createOrganization: async (
    data: CreateOrganizationData
  ): Promise<Organization> => {
    const response = await apiClient.post<OrganizationApiResponse>(
      "/api/organizations",
      data
    );
    return response.data.organization;
  },

  getOrganizations: async (
    params?: PaginationParams
  ): Promise<OrganizationsResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get<OrganizationsApiResponse>(
      `/api/organizations?${queryParams}`
    );
    return {
      organizations: response.data.organizations,
      pagination: {
        page: response.data.pagination.currentPage,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.totalCount,
      },
    };
  },

  getOrganization: async (uuid: string): Promise<Organization> => {
    const response = await apiClient.get<OrganizationApiResponse>(
      `/api/organizations/${uuid}`
    );
    return response.data.organization;
  },

  updateOrganization: async (
    uuid: string,
    data: UpdateOrganizationData
  ): Promise<Organization> => {
    const response = await apiClient.put<OrganizationApiResponse>(
      `/api/organizations/${uuid}`,
      data
    );
    return response.data.organization;
  },

  deleteOrganization: async (uuid: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(`/api/organizations/${uuid}`);
  },

  // Search Operations
  searchOrganizations: async (
    query: string,
    params?: PaginationParams
  ): Promise<OrganizationsResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("perPage", params.page.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get<OrganizationsApiResponse>(
      `/api/organizations/search/${query}?${queryParams}`
    );
    return {
      organizations: response.data.organizations,
      pagination: {
        page: response.data.pagination?.currentPage || 1,
        pageSize: response.data.pagination?.pageSize || 10,
        total:
          response.data.pagination?.totalCount ||
          response.data.organizations.length,
      },
    };
  },

  // Advanced Queries
  getOrganizationWithVerifiedData: async (
    uuid: string
  ): Promise<OrganizationWithVerifiedDataResponse> => {
    const response =
      await apiClient.get<OrganizationWithVerifiedDataApiResponse>(
        `/api/organizations/${uuid}/verified-data`
      );
    return {
      organization: response.data.organization,
      verified_data: response.data.verified_data,
    };
  },

  // NEW: Get Organization Members
  getOrganizationMembers: async (
    uuid: string,
    params?: { perPage?: number; pageSize?: number }
  ): Promise<{ members: OrganizationMember[]; pagination: any }> => {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get<{
      status_code: number;
      success: boolean;
      message: string;
      members: OrganizationMember[];
      pagination: {
        totalCount: number;
        currentPage: number;
        pageSize: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
      };
    }>(`/api/organizations/${uuid}/members?${queryParams}`);

    return {
      members: response.data.members,
      pagination: response.data.pagination,
    };
  },

  // NEW: Get Organization Awards
  getOrganizationAwards: async (uuid: string): Promise<MemberAward[]> => {
    const response = await apiClient.get<MemberAwardsApiResponse>(
      `/api/organizations/${uuid}/awards`
    );
    return response.data.awards;
  },

  // DEPRECATED: Get Organization with Relations (use separate calls instead)
  getOrganizationWithRelations: async (
    uuid: string
  ): Promise<OrganizationWithRelationsResponse> => {
    const response = await apiClient.get<OrganizationWithRelationsApiResponse>(
      `/api/organizations/${uuid}/relations`
    );
    return {
      organization: response.data.organization,
      member_relations: response.data.member_relations,
      awards: response.data.awards,
    };
  },

  getOrganizationsByMember: async (
    memberId: string
  ): Promise<Organization[]> => {
    const response = await apiClient.get<MemberOrganizationsApiResponse>(
      `/api/organizations/member/${memberId}/organizations`
    );
    return response.data.organizations;
  },

  getAwardsByMember: async (memberId: string): Promise<MemberAward[]> => {
    const response = await apiClient.get<MemberAwardsApiResponse>(
      `/api/organizations/member/${memberId}/awards`
    );
    return response.data.awards;
  },

  // Bulk Operations
  bulkCreateOrganizations: async (
    organizations: CreateOrganizationData[]
  ): Promise<Organization[]> => {
    const response = await apiClient.post<MemberOrganizationsApiResponse>(
      "/api/organizations/bulk",
      organizations
    );
    return response.data.organizations;
  },

  // Verified Data Operations
  createOrganizationVerifiedData: async (data: {
    organizationId: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    phone: string;
    ein: string;
    industry: string;
    foundingyear: number;
    verificationStatus: string;
    verificationType: string;
  }): Promise<OrganizationVerifiedData> => {
    const response = await apiClient.post<OrganizationVerifiedDataApiResponse>(
      "/api/organizations/verified-data",
      data
    );
    return response.data.verified_data;
  },

  getOrganizationVerifiedData: async (
    uuid: string
  ): Promise<OrganizationVerifiedData> => {
    const response = await apiClient.get<OrganizationVerifiedDataApiResponse>(
      `/api/organizations/verified-data/${uuid}`
    );
    return response.data.verified_data;
  },

  updateOrganizationVerifiedData: async (
    uuid: string,
    data: Partial<OrganizationVerifiedData>
  ): Promise<OrganizationVerifiedData> => {
    const response = await apiClient.put<OrganizationVerifiedDataApiResponse>(
      `/api/organizations/verified-data/${uuid}`,
      data
    );
    return response.data.verified_data;
  },

  deleteOrganizationVerifiedData: async (uuid: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(
      `/api/organizations/verified-data/${uuid}`
    );
  },

  // Member Relations Operations
  createMemberRelation: async (data: {
    memberUuid: string;
    organizationUuid: string;
  }): Promise<MemberRelation> => {
    const response = await apiClient.post<MemberRelationApiResponse>(
      "/api/organizations/relations",
      data
    );
    return response.data.relation;
  },

  deleteMemberRelation: async (uuid: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(
      `/api/organizations/relations/${uuid}`
    );
  },

  // Awards Operations
  createMemberAward: async (data: {
    memberId: string;
    organizationId: string;
    awardListingElementId: number;
    status: string;
    progress: number;
    categories: Record<string, any>;
    isDisQualified: boolean;
    isPreviousWinner: boolean;
    isQualified: boolean;
    isJudged: boolean;
    isPaid: boolean;
    isWinner: boolean;
    winnerTypes: string;
    applicationLink?: string;
  }): Promise<MemberAward> => {
    const response = await apiClient.post<MemberAwardApiResponse>(
      "/api/organizations/awards",
      data
    );
    return response.data.award;
  },

  getMemberAwards: async (params?: {
    memberId?: string;
    organizationId?: string;
  }): Promise<MemberAward[]> => {
    const queryParams = new URLSearchParams();
    if (params?.memberId) queryParams.append("memberId", params.memberId);
    if (params?.organizationId)
      queryParams.append("organizationId", params.organizationId);

    const response = await apiClient.get<MemberAwardsApiResponse>(
      `/api/organizations/awards?${queryParams}`
    );
    return response.data.awards;
  },

  getMemberAward: async (uuid: string): Promise<MemberAward> => {
    const response = await apiClient.get<MemberAwardApiResponse>(
      `/api/organizations/awards/${uuid}`
    );
    return response.data.award;
  },

  updateMemberAward: async (
    uuid: string,
    data: Partial<MemberAward>
  ): Promise<MemberAward> => {
    const response = await apiClient.put<MemberAwardApiResponse>(
      `/api/organizations/awards/${uuid}`,
      data
    );
    return response.data.award;
  },

  deleteMemberAward: async (uuid: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(
      `/api/organizations/awards/${uuid}`
    );
  },
};

// Assign an organization to a member
export async function assignOrganizationToMember(
  memberUuid: string,
  organizationUuid: string
) {
  const response = await apiClient.post("/api/organizations/relations", {
    memberUuid,
    organizationUuid,
  });
  return response.data;
}

// Get all organizations related to a member
export async function getOrganizationsForMember(memberUuid: string) {
  const response = await apiClient.get(
    `/api/organizations/member/${memberUuid}/organizations`
  );
  return response.data.organizations;
}

// Remove an organization from a member
export async function removeOrganizationFromMember(
  memberUuid: string,
  organizationUuid: string
) {
  const response = await apiClient.delete("/api/organizations/relations/", {
    data: { memberUuid, organizationUuid },
  });
  return response.data;
}
