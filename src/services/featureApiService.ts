import { apiClient } from "@/services/apiClient";
import {
  FeatureFlag,
  CreateFeatureFlagData,
  UpdateFeatureFlagData,
  FeatureFlagSearchParams,
  FeatureFlagResponse,
  Bookmark,
  CreateBookmarkData,
  UpdateBookmarkData,
  BookmarkSearchParams,
  BookmarkResponse,
} from "@/types/feature";

class FeatureApiService {
  // Feature Flag CRUD Operations
  async getFeatureFlags(
    params?: FeatureFlagSearchParams
  ): Promise<FeatureFlagResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/feature/flags?${queryParams.toString()}`
    );
    return response.data;
  }

  async getFeatureFlag(flagId: number): Promise<FeatureFlagResponse> {
    const response = await apiClient.get(`/api/feature/flags/${flagId}`);
    return response.data;
  }

  async createFeatureFlag(
    data: CreateFeatureFlagData
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.post("/api/feature/flags", data);
    return response.data;
  }

  async updateFeatureFlag(
    flagId: number,
    data: UpdateFeatureFlagData
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.put(`/api/feature/flags/${flagId}`, data);
    return response.data;
  }

  async deleteFeatureFlag(flagId: number): Promise<FeatureFlagResponse> {
    const response = await apiClient.delete(`/api/feature/flags/${flagId}`);
    return response.data;
  }

  // Bulk Feature Flag Operations
  async bulkCreateFeatureFlags(
    featureFlags: CreateFeatureFlagData[]
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.post(
      "/api/feature/flags/bulk",
      featureFlags
    );
    return response.data;
  }

  async bulkUpdateFeatureFlags(
    updates: { id: number; data: UpdateFeatureFlagData }[]
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.put("/api/feature/flags/bulk", updates);
    return response.data;
  }

  async bulkDeleteFeatureFlags(
    flagIds: number[]
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.delete("/api/feature/flags/bulk", {
      data: { flag_ids: flagIds },
    });
    return response.data;
  }

  // Member-specific Feature Flag Operations
  async getFeatureFlagsByMember(
    memberId: number
  ): Promise<FeatureFlagResponse> {
    const response = await apiClient.get(
      `/api/feature/member/${memberId}/flags`
    );
    return response.data;
  }

  async searchFeatureFlags(
    query: string,
    params?: FeatureFlagSearchParams
  ): Promise<FeatureFlagResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/feature/flags/search/${query}?${queryParams.toString()}`
    );
    return response.data;
  }

  // Bookmark CRUD Operations
  async getBookmarks(params?: BookmarkSearchParams): Promise<BookmarkResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/feature/bookmarks?${queryParams.toString()}`
    );
    return response.data;
  }

  async getBookmark(bookmarkId: number): Promise<BookmarkResponse> {
    const response = await apiClient.get(
      `/api/feature/bookmarks/${bookmarkId}`
    );
    return response.data;
  }

  async createBookmark(data: CreateBookmarkData): Promise<BookmarkResponse> {
    const response = await apiClient.post("/api/feature/bookmarks", data);
    return response.data;
  }

  async updateBookmark(
    bookmarkId: number,
    data: UpdateBookmarkData
  ): Promise<BookmarkResponse> {
    const response = await apiClient.put(
      `/api/feature/bookmarks/${bookmarkId}`,
      data
    );
    return response.data;
  }

  async deleteBookmark(bookmarkId: number): Promise<BookmarkResponse> {
    const response = await apiClient.delete(
      `/api/feature/bookmarks/${bookmarkId}`
    );
    return response.data;
  }

  // Member-specific Bookmark Operations
  async getBookmarksByMember(memberId: number): Promise<BookmarkResponse> {
    const response = await apiClient.get(
      `/api/feature/member/${memberId}/bookmarks`
    );
    return response.data;
  }

  async searchBookmarks(
    query: string,
    params?: BookmarkSearchParams
  ): Promise<BookmarkResponse> {
    const queryParams = new URLSearchParams();
    if (params?.perPage)
      queryParams.append("perPage", params.perPage.toString());
    if (params?.pageSize)
      queryParams.append("pageSize", params.pageSize.toString());

    const response = await apiClient.get(
      `/api/feature/bookmarks/search/${query}?${queryParams.toString()}`
    );
    return response.data;
  }
}

export const featureApiService = new FeatureApiService();
