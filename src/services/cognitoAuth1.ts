// services/cognitoAuth.ts
import { signIn, confirmSignIn, rememberDevice } from "aws-amplify/auth";

export const cognitoAuthService = {
  signIn: async ({ username, password }: { username: string; password: string }) => {
    const response = await signIn({ username, password });

    const { isSignedIn, nextStep } = response;
    console.log(isSignedIn, nextStep);

    if (isSignedIn) {
      return { status: "SIGNED_IN" };
    }

    return {
      status: "MFA_REQUIRED",
      nextStep,
    };
  },

  confirmMFA: async (code: string, remember = false) => {
    const result = await confirmSignIn({ challengeResponse: code });
    if (remember) {
      await rememberDevice(); // optional
    }
    return result;
  },
};
