import { apiClient } from "@/services/apiClient";
import {
  Organization,
  CreateOrganizationData,
  UpdateOrganizationData,
  OrganizationFilterParams,
  OrganizationResponse,
} from "@/types/organization";

class OrganizationApiService {
  // Organization CRUD Operations
  async getOrganizations(
    params?: OrganizationFilterParams
  ): Promise<OrganizationResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      // Pagination
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.pageSize)
        queryParams.append("pageSize", params.pageSize.toString());

      // Text filters (partial, case-insensitive)
      if (params.name) queryParams.append("name", params.name);
      if (params.city) queryParams.append("city", params.city);
      if (params.state) queryParams.append("state", params.state);
      if (params.zip) queryParams.append("zip", params.zip);
      if (params.companySize)
        queryParams.append("companySize", params.companySize);
      if (params.industry) queryParams.append("industry", params.industry);
      if (params.yearFounded)
        queryParams.append("yearFounded", params.yearFounded);
      if (params.phone) queryParams.append("phone", params.phone);
      if (params.email) queryParams.append("email", params.email);

      // Date filters (format: YYYY-MM-DD)
      if (params.dateCreatedFrom)
        queryParams.append("dateCreatedFrom", params.dateCreatedFrom);
      if (params.dateCreatedTo)
        queryParams.append("dateCreatedTo", params.dateCreatedTo);

      // Range filters
      if (params.annualRevenueMin)
        queryParams.append("annualRevenueMin", params.annualRevenueMin);
      if (params.annualRevenueMax)
        queryParams.append("annualRevenueMax", params.annualRevenueMax);
      if (params.yearFoundedMin)
        queryParams.append("yearFoundedMin", params.yearFoundedMin);
      if (params.yearFoundedMax)
        queryParams.append("yearFoundedMax", params.yearFoundedMax);

      // Exact match filter
      if (params.memberCount)
        queryParams.append("memberCount", params.memberCount.toString());

      // Sorting
      if (params.sortBy) queryParams.append("sortBy", params.sortBy);
      if (params.sortOrder) queryParams.append("sortOrder", params.sortOrder);
    }

    const response = await apiClient.get(
      `/api/organizations/?${queryParams.toString()}`
    );
    return response.data;
  }

  async getOrganization(uuid: string): Promise<OrganizationResponse> {
    const response = await apiClient.get(`/api/organizations/${uuid}`);
    return response.data;
  }

  async createOrganization(
    data: CreateOrganizationData
  ): Promise<OrganizationResponse> {
    const response = await apiClient.post("/api/organizations/", data);
    return response.data;
  }

  async updateOrganization(
    uuid: string,
    data: UpdateOrganizationData
  ): Promise<OrganizationResponse> {
    const response = await apiClient.put(`/api/organizations/${uuid}`, data);
    return response.data;
  }

  async deleteOrganization(uuid: string): Promise<OrganizationResponse> {
    const response = await apiClient.delete(`/api/organizations/${uuid}`);
    return response.data;
  }
}

export const organizationApiService = new OrganizationApiService();
