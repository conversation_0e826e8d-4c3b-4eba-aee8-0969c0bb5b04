import { apiClient } from "@/services/apiClient";
import {
  Member,
  MemberWithRelations,
  MemberSearchFilters,
  MemberBulkAction,
  VerificationRequest,
  MemberWithOrganizationsResponse,
  MemberExportRequest,
  MemberExportResponse,
} from "@/types/member";
import {
  CreateMemberData,
  UpdateMemberData,
  type OrganizationData,
  type NewOrganizationData,
} from "@/lib/validations/member";

export interface OrganizationResponse {
  status_code: number;
  success: boolean;
  message: string;
  organizations: Organization[];
  pagination: Pagination;
}

export interface Organization {
  name: string;
  address1?: string;
  address2?: string;
  city: string;
  state?: string;
  zip?: string;
  phone?: string;
  annualRevenue?: string;
  industry: string;
  yearFounded?: string;
  businessProfileElementId?: number;
  email?: string;
  companySize?: string;
  uuid: string;
  createdBy: string;
  updatedBy: string;
  dateCreated: string;
  dateUpdated: string;
}

export interface Pagination {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Enhanced members service using apiClient
export const membersService = {
  // Get members with pagination and filters - UPDATED for new API
  async getMembers(params: {
    page: number;
    pageSize: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    filters: MemberSearchFilters;
  }) {
    const queryParams = new URLSearchParams();
    queryParams.append("page", params.page.toString());
    queryParams.append("pageSize", params.pageSize.toString());
    if (params.sortBy) queryParams.append("sortBy", params.sortBy);
    if (params.sortOrder) queryParams.append("sortOrder", params.sortOrder);

    // Add filters for new API
    if (params.filters.search)
      queryParams.append("search", params.filters.search);
    if (params.filters.firstName)
      queryParams.append("firstName", params.filters.firstName);
    if (params.filters.lastName)
      queryParams.append("lastName", params.filters.lastName);
    if (params.filters.email) queryParams.append("email", params.filters.email);
    if (params.filters.membershipTier)
      queryParams.append("membershipTier", params.filters.membershipTier);
    if (params.filters.communityStatus)
      queryParams.append("communityStatus", params.filters.communityStatus);
    if (params.filters.verificationStatus)
      queryParams.append(
        "verificationStatus",
        params.filters.verificationStatus
      );
    if (params.filters.organizationName)
      queryParams.append("organizationName", params.filters.organizationName);
    if (params.filters.organizationCity)
      queryParams.append("organizationCity", params.filters.organizationCity);
    if (params.filters.organizationState)
      queryParams.append("organizationState", params.filters.organizationState);
    if (params.filters.organizationZip)
      queryParams.append("organizationZip", params.filters.organizationZip);
    if (params.filters.companySize)
      queryParams.append("companySize", params.filters.companySize);
    if (params.filters.industry)
      queryParams.append("industry", params.filters.industry);

    // Add date filters
    if (params.filters.dateCreatedFrom)
      queryParams.append("dateCreatedFrom", params.filters.dateCreatedFrom);
    if (params.filters.dateCreatedTo)
      queryParams.append("dateCreatedTo", params.filters.dateCreatedTo);

    const response = await apiClient.get<MemberWithOrganizationsResponse>(
      `/api/admin/bulk/with-organizations?${queryParams.toString()}`
    );

    // Return response with proper pagination structure
    return {
      members: response.data.members || [],
      pagination: {
        totalCount: response.data.pagination?.totalCount || 0,
        currentPage: response.data.pagination?.currentPage || params.page,
        pageSize: response.data.pagination?.pageSize || params.pageSize,
        totalPages: response.data.pagination?.totalPages || 0,
        hasNext: response.data.pagination?.hasNext || false,
        hasPrevious: response.data.pagination?.hasPrevious || false,
      },
    };
  },

  // Get member by ID with all relations
  async getMember(id: string) {
    const response = await apiClient.get<{
      status_code: number;
      success: boolean;
      message: string;
      member: MemberWithRelations;
    }>(`/api/members/uuid/${id}`);

    // Extract the member data from the response structure
    return response.data.member;
  },

  // Create new member
  async createMember(memberData: CreateMemberData) {
    const response = await apiClient.post<Member>(
      "/api/admin/create-member",
      memberData
    );
    return response.data;
  },

  // Update member
  async updateMember(id: string, memberData: UpdateMemberData) {
    const response = await apiClient.put<Member>(
      `/api/admin/update-member/${id}`,
      memberData
    );
    return response.data;
  },

  // Delete member
  async deleteMember(id: string) {
    const response = await apiClient.delete<{ deleted: boolean }>(
      `/api/members/${id}`
    );
    return response.data;
  },

  // Bulk actions
  async bulkAction(params: MemberBulkAction) {
    const response = await apiClient.post<{
      success: string[];
      failed: string[];
      message: string;
    }>("/api/members/bulk", params);
    return response.data;
  },

  // Verification management
  async verifyMember(request: VerificationRequest) {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
    }>(`/api/members/${request.memberId}/verify`, request);
    return response.data;
  },

  // email verification
  async verifyemail(memberId: string) {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
    }>(`/api/members/${memberId}/verify-email`, {});
    return response.data;
  },

  // Status management
  async updateMemberStatus(memberId: string, status: string) {
    const response = await apiClient.patch<{
      success: boolean;
      message: string;
    }>(`/api/members/${memberId}/status`, { status });
    return response.data;
  },

  // Get organizations for autocomplete
  async getOrganizations(search?: string | null) {
    const response = await apiClient.get<OrganizationResponse>(
      search
        ? `/api/organizations?name=${search}&page=1&pageSize=25`
        : "/api/organizations"
    );
    return response.data;
  },

  // Create new organization
  async createOrganization(orgData: NewOrganizationData) {
    const response = await apiClient.post<OrganizationData>(
      "/api/organizations",
      orgData
    );
    return response.data;
  },

  // NEW: Export members to CSV
  async exportMembers(
    exportRequest: MemberExportRequest
  ): Promise<MemberExportResponse> {
    const response = await apiClient.post<Blob>(
      "/api/admin/members/export",
      exportRequest,
      {
        responseType: "blob",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    // Create download link for the CSV file
    const blob = new Blob([response.data], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `member_export_${
      new Date().toISOString().split("T")[0]
    }.csv`;

    return {
      success: true,
      message: "Export completed successfully",
      downloadUrl: url,
      filename: link.download,
    };
  },
};
