import { createTheme } from "@mui/material/styles";

// Extend the theme interface to include custom colors
declare module '@mui/material/styles' {
  interface Palette {
    custom: {
      maroon: string;
      tomato: string;
      navy: string;
      teal: string;
    };
  }

  interface PaletteOptions {
    custom?: {
      maroon?: string;
      tomato?: string;
      navy?: string;
      teal?: string;
    };
  }
}

const theme = createTheme({
  palette: {
    primary: {
      main: '#1e3a8a', // US Chamber blue
      light: '#3b82f6',
      dark: '#1e40af',
    },
    secondary: {
      main: '#dc2626', // US Chamber red
      light: '#ef4444',
      dark: '#b91c1c',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },


    error: {
      main: '#D9453C', // Tomato Red - for subscribe/enroll CTAs
      contrastText: '#FFFFFF',
    },

    text: {
      primary: '#333333', // Dark Gray/Blackish - main text color
      secondary: '#6C1D2B', // Deep Maroon - text highlighting
    },
    custom: {
      maroon: '#6C1D2B', // Deep Maroon - occasional text highlighting
      tomato: '#D9453C', // Tomato Red - CTAs
      navy: '#002E5D', // Dark Navy
      teal: '#008C9E', // Teal/Peacock Blue
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*': {
          margin: 0,
          padding: 0,
          boxSizing: 'border-box',
        },
        html: {
          margin: 0,
          padding: 0,
        },
        body: {
          margin: 0,
          padding: 0,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          margin: 0,
          padding: 0,
          borderRadius: 12,
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          '@media (max-width:600px)': {
            borderRadius: 8,
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          '@media (max-width:600px)': {
            fontSize: '0.875rem',
            padding: '6px 12px',
          },
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          '@media (max-width:960px)': {
            overflowX: 'auto',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          '@media (max-width:960px)': {
            padding: '8px 4px',
            fontSize: '0.75rem',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            fontSize: '0.875rem',
          },
        },
      },
    },
    MuiFormControl: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            fontSize: '0.875rem',
          },
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          '@media (max-width:600px)': {
            margin: 16,
            width: 'calc(100% - 32px)',
            maxWidth: 'none',
          },
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          '@media (max-width:960px)': {
            width: '100%',
            maxWidth: 320,
          },
        },
      },
    },
  },
});

export { theme };


