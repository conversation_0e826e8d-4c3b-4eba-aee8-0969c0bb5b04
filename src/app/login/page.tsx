"use client";

import { useAuth0 } from "@/hooks/useAuth0";
import { showToast } from "@/utils/toast";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  CircularProgress,
  Container,
  Paper,
  Typography,
} from "@mui/material";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function LoginPage() {
  const { user, isLoading, error, login, isAuthenticated } = useAuth0();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      showToast.success("Welcome back!");
      router.push("/member-user/dashboard");
    }
  }, [isAuthenticated, user, router]);

  // Handle Auth0 callback errors
  useEffect(() => {
    const error = searchParams.get("error");
    if (error) {
      console.error("Login page - Auth0 error:", error);
      showToast.error(`Login failed: ${error}`);
      // Clean up the URL
      router.replace("/login");
    }
  }, [searchParams, router]);

  const handleLogin = async () => {
    try {
      await login();
    } catch (error) {
      showToast.error("Login failed. Please try again.");
    }
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          height: "100vh",
          width: "100vw",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
          overflow: "hidden",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: "0 8px 32px 0 rgba(30,58,138,0.2)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "90vw", sm: 400 },
            maxWidth: 420,
            mx: "auto",
          }}
        >
          <Box sx={{ mb: 3, display: "flex", justifyContent: "center" }}>
            <Image
              src="/CO-gold.svg"
              alt="US Chamber CO Logo"
              width={120}
              height={56}
              style={{ objectFit: "contain" }}
            />
          </Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            align="center"
            sx={{ fontWeight: 700, color: "#1e3a8a" }}
          >
            Welcome to CO
          </Typography>
          <Typography
            variant="subtitle1"
            align="center"
            sx={{ mb: 3, color: "#555" }}
          >
            Member Dashboard Login
          </Typography>
          {/* 
          {error && (
            <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
              {error.message || "An error occurred during authentication"}
            </Alert>
          )} */}

          <Button
            fullWidth
            variant="contained"
            onClick={handleLogin}
            disabled={isLoading}
            sx={{
              mt: 2,
              mb: 2,
              fontWeight: 700,
              background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
              color: "#fff",
              boxShadow: "0 2px 8px 0 rgba(30,58,138,0.10)",
              "&:hover": {
                background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                opacity: 0.95,
              },
              "&:disabled": {
                opacity: 0.6,
              },
            }}
          >
            {isLoading ? "Signing in..." : "Sign in with Auth0"}
          </Button>

          <Typography
            variant="body2"
            align="center"
            sx={{ color: "#666", mt: 2 }}
          >
            Secure authentication powered by Auth0
          </Typography>
        </Paper>
      </Container>
    </Box>
  );
}
