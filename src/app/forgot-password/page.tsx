"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Alert,
  CircularProgress,
} from "@mui/material";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  initiateForgotPasswordRequest,
  confirmForgotPasswordRequest,
  resetForgotPassword,
} from "@/store/auth/redux";
import { ForgotPasswordForm, ResetPasswordForm } from "@/components/auth";
import { ForgotPasswordFormData, ResetPasswordFormData } from "@/types/auth";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { forgotPassword } = useAppSelector((state) => state.auth);

  const [forgotPasswordData, setForgotPasswordData] =
    useState<ForgotPasswordFormData>({
      email: "",
    });

  const [resetPasswordData, setResetPasswordData] =
    useState<ResetPasswordFormData>({
      email: "",
      code: "",
      newPassword: "",
      confirmPassword: "",
    });

  // Reset forgot password state when component mounts
  useEffect(() => {
    dispatch(resetForgotPassword());
  }, [dispatch]);

  // Update reset password email when we get the username from initiate step
  useEffect(() => {
    if (forgotPassword.username) {
      setResetPasswordData((prev) => ({
        ...prev,
        email: forgotPassword.username,
      }));
    }
  }, [forgotPassword.username]);

  const handleInitiateForgotPassword = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(initiateForgotPasswordRequest(forgotPasswordData));
  };

  const handleConfirmForgotPassword = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(confirmForgotPasswordRequest(resetPasswordData));
  };

  const handleBackToInitial = () => {
    dispatch(resetForgotPassword());
    setResetPasswordData({
      email: "",
      code: "",
      newPassword: "",
      confirmPassword: "",
    });
  };

  const handleBackToLogin = () => {
    router.push("/");
  };

  const renderStep = () => {
    switch (forgotPassword.step) {
      case "initial":
        return (
          <ForgotPasswordForm
            formData={forgotPasswordData}
            setFormData={setForgotPasswordData}
            onSubmit={handleInitiateForgotPassword}
            isLoading={forgotPassword.isLoading}
            error={forgotPassword.error}
          />
        );

      case "verification":
        return (
          <ResetPasswordForm
            formData={resetPasswordData}
            setFormData={setResetPasswordData}
            onSubmit={handleConfirmForgotPassword}
            onBack={handleBackToInitial}
            isLoading={forgotPassword.isLoading}
            error={forgotPassword.error}
            codeDeliveryDetails={forgotPassword.codeDeliveryDetails}
          />
        );

      case "completed":
        return (
          <Box sx={{ textAlign: "center" }}>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ fontWeight: 600, color: "#1e3a8a", mb: 2 }}
            >
              Password Reset Successful!
            </Typography>
            <Alert severity="success" sx={{ mb: 3 }}>
              Your password has been successfully reset. You can now login with
              your new password.
            </Alert>
            <Button
              variant="contained"
              onClick={handleBackToLogin}
              sx={{
                fontWeight: 700,
                background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                color: "#fff",
                "&:hover": {
                  opacity: 0.95,
                },
              }}
            >
              Back to Login
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  if (forgotPassword.isLoading && forgotPassword.step === "initial") {
    return (
      <Box
        sx={{
          height: "100vh",
          width: "100vw",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
          overflow: "hidden",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: "0 8px 32px 0 rgba(30,58,138,0.2)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "90vw", sm: 400 },
            maxWidth: 420,
            mx: "auto",
          }}
        >
          <Box sx={{ mb: 3, display: "flex", justifyContent: "center" }}>
            <Image
              src="/CO-gold.svg"
              alt="US Chamber CO Logo"
              width={120}
              height={56}
              style={{ objectFit: "contain" }}
            />
          </Box>

          {renderStep()}

          {/* Back to Login link - only show on initial step */}
          {forgotPassword.step === "initial" && (
            <Box sx={{ mt: 2, textAlign: "center" }}>
              <Button
                variant="text"
                onClick={handleBackToLogin}
                sx={{
                  color: "#1e3a8a",
                  textTransform: "none",
                  fontWeight: 500,
                  "&:hover": {
                    backgroundColor: "rgba(30, 58, 138, 0.04)",
                  },
                }}
              >
                ← Back to Login
              </Button>
            </Box>
          )}
        </Paper>
      </Container>
    </Box>
  );
}
