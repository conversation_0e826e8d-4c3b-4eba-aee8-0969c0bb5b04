"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Box,
  Container,
  Paper,
  Typography,
  CircularProgress,
} from "@mui/material";
import { ChangePasswordForm } from "@/components/auth/ChangePasswordForm";
import { MFAForm, TOTPSetupForm } from "@/components/auth";
import { authApiService } from "@/services/authApiService";
import {
  ChangePasswordWithTokenFormData,
  MFASetupFormData,
  TOTPSetupResponse,
} from "@/types/auth";
import globalErrorHandler from "@/lib/errorHandler";
import { AUTH_CONSTANTS } from "@/constants/auth";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  loginRequest,
  confirmMFARequest,
  setupTOTPRequest,
  clearError,
} from "@/store/auth/redux";

type FlowStep = "password" | "mfa-setup" | "loading" | "error";

export default function ChangePasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const accessToken = searchParams.get("accessToken");
  const dispatch = useAppDispatch();
  const auth = useAppSelector((state) => state.auth);

  const [currentStep, setCurrentStep] = useState<FlowStep>("loading");
  const [error, setError] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [userCredentials, setUserCredentials] = useState<{
    username: string;
    password: string;
  } | null>(null);
  const [mfaData, setMfaData] = useState<MFASetupFormData>({ code: "" });

  useEffect(() => {
    // Check if access token is present
    if (!accessToken) {
      setError(AUTH_CONSTANTS.MESSAGES.INVALID_ACCESS_TOKEN);
      setCurrentStep("error");
      return;
    }

    // If we have access token, show password form
    setCurrentStep("password");
  }, [accessToken]);

  // Handle auth state changes after login
  useEffect(() => {
    // If we have credentials (password was changed) and auth state shows MFA/TOTP required
    if (userCredentials && (auth.mfaRequired || auth.totpSetup.required)) {
      setCurrentStep("mfa-setup");
      setIsLoading(false);
    }
    // If user is fully authenticated after completing MFA/TOTP
    else if (auth.isAuthenticated && userCredentials) {
      setIsLoading(false);
      globalErrorHandler.showSuccess(
        AUTH_CONSTANTS.MESSAGES.MFA_SETUP_SUCCESS,
        "Setup Complete"
      );
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    }
    // Handle auth errors during login
    else if (auth.error && userCredentials) {
      setError(auth.error);
      setIsLoading(false);
      globalErrorHandler.showToast({
        id: `auth-error-${Date.now()}`,
        type: "error",
        title: "Login Failed",
        message: auth.error,
        duration: 5000,
      });
    }
  }, [
    auth.isAuthenticated,
    auth.mfaRequired,
    auth.totpSetup.required,
    auth.error,
    userCredentials,
    router,
  ]);

  const handlePasswordChange = async (
    data: ChangePasswordWithTokenFormData
  ) => {
    setIsLoading(true);
    setError("");

    try {
      // Change password with token
      const response = await authApiService.changePasswordWithToken(data);

      // Show success message
      globalErrorHandler.showSuccess(
        AUTH_CONSTANTS.MESSAGES.PASSWORD_CHANGE_SUCCESS,
        "Password Updated"
      );

      // Store credentials for automatic login
      setUserCredentials({
        username: response.username,
        password: data.newPassword,
      });

      // Automatically login with new credentials
      dispatch(clearError());
      dispatch(
        loginRequest({
          username: response.username,
          password: data.newPassword,
        })
      );

      // Don't immediately change step - wait for auth state to update
      // Keep loading state active until auth responds
    } catch (err: any) {
      let errorMessage: string = AUTH_CONSTANTS.MESSAGES.PASSWORD_CHANGE_FAILED;
      let errorTitle = "Password Change Failed";

      // Handle specific error cases
      if (err.message) {
        errorMessage = err.message;
      }

      // Check if password has already been changed
      if (
        err.message &&
        err.message.includes("Password has already been changed")
      ) {
        errorTitle = "Password Already Changed";
        errorMessage = AUTH_CONSTANTS.MESSAGES.PASSWORD_ALREADY_CHANGED;

        // Optionally redirect to login after showing the message
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      }

      setError(errorMessage);
      setIsLoading(false); // Set loading false on error
      globalErrorHandler.showToast({
        id: `password-error-${Date.now()}`,
        type: "error",
        title: errorTitle,
        message: errorMessage,
        duration: errorTitle === "Password Already Changed" ? 6000 : 5000,
      });
    }
  };

  const handleMFASubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    dispatch(clearError());
    if (userCredentials) {
      dispatch(
        confirmMFARequest({
          username: userCredentials.username,
          code: mfaData.code,
        })
      );
      setMfaData({ code: "" });
    }
  };

  const handleTOTPSetup = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    dispatch(clearError());
    dispatch(setupTOTPRequest({ code: mfaData.code }));
    setMfaData({ code: "" });
  };

  const renderContent = () => {
    switch (currentStep) {
      case "loading":
        return (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="400px"
          >
            <CircularProgress />
          </Box>
        );

      case "error":
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="error" gutterBottom>
              Error
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {error}
            </Typography>
          </Box>
        );

      case "password":
        return (
          <ChangePasswordForm
            accessToken={accessToken!}
            onSubmit={handlePasswordChange}
            isLoading={isLoading}
            error={error}
          />
        );

      case "mfa-setup":
        return auth.totpSetup.required ? (
          <TOTPSetupForm
            sharedSecret={auth.totpSetup.sharedSecret || ""}
            username={userCredentials?.username || ""}
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleTOTPSetup}
            isLoading={isLoading}
          />
        ) : auth.mfaRequired ? (
          <MFAForm
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleMFASubmit}
            isLoading={isLoading}
          />
        ) : (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="400px"
          >
            <CircularProgress />
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container component="main" maxWidth="sm">
        <Box
          sx={{
            marginTop: 8,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Paper
            elevation={3}
            sx={{
              padding: 4,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              width: "100%",
              maxWidth: 400,
            }}
          >
            {currentStep === "password" && (
              <Typography component="h1" variant="h5" gutterBottom>
                Set Your Password
              </Typography>
            )}

            {currentStep === "mfa-setup" && (
              <Typography component="h1" variant="h5" gutterBottom>
                {auth.totpSetup.required
                  ? "Set Up Two-Factor Authentication"
                  : auth.mfaRequired
                  ? auth.mfaType === "TOTP"
                    ? "Enter your Authenticator App code"
                    : "Enter the SMS code sent to your phone"
                  : "Completing Setup..."}
              </Typography>
            )}

            {renderContent()}
          </Paper>
        </Box>
      </Container>
    </Box>
  );
}
