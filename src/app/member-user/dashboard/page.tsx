"use client";

import { Box, Button, Typography, Paper } from "@mui/material";
import { useAuth0 } from "@/hooks/useAuth0";
import { useEffect, useState } from "react";
import { getUserAuth0Token } from "@/services/auth0TokenService";

export default function MemberDashboard() {
  const { user, logout, getUserToken } = useAuth0();
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getToken = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Get the user's Auth0 token
        const userToken = await getUserToken();

        if (!userToken) {
          setError("Failed to get user token");
          return;
        }

        // Validate the token with our API
        const tokenData = await getUserAuth0Token(userToken);
        setAccessToken(tokenData.access_token);
      } catch (err: any) {
        console.error("Error getting Auth0 token:", err);
        setError(err.message || "Failed to get token");
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      getToken();
    }
  }, [user, getUserToken]);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Member Dashboard
      </Typography>

      {user && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="h6">User Info</Typography>
          <Typography>Email: {user.email}</Typography>
          <Typography>Name: {user.name}</Typography>
        </Paper>
      )}

      {isLoading && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography>Loading token...</Typography>
        </Paper>
      )}

      {error && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: "error.light" }}>
          <Typography color="error">Error: {error}</Typography>
        </Paper>
      )}

      {accessToken && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: "success.light" }}>
          <Typography variant="h6">Access Token</Typography>
          <Typography variant="body2" sx={{ wordBreak: "break-all" }}>
            {accessToken}
          </Typography>
        </Paper>
      )}

      <Button variant="contained" onClick={logout}>
        Logout
      </Button>
    </Box>
  );
}
