import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'

const Loading = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)',
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: 9999,
      }}
    >
      <CircularProgress 
        size={60} 
        thickness={4}
        sx={{ 
          color: '#ffffff',
          mb: 2 
        }} 
      />
      <Typography 
        variant="h6" 
        sx={{ 
          color: '#ffffff',
          fontWeight: 500,
          textAlign: 'center'
        }}
      >
        Loading...
      </Typography>
    </Box>
  )
}

export default Loading