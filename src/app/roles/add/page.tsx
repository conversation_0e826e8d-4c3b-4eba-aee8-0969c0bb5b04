"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Typography,
  Card,
  Card<PERSON>ontent,
  Al<PERSON>,
  But<PERSON>,
  Container,
} from "@mui/material";
import { RoleForm } from "@/components/roles/RoleForm";
import AppLayout from "@/layout/AppLayout";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { useRoles } from "@/context/RolesContext";
import { showToast } from "@/utils/toast";

export default function AddRolePage() {
  const router = useRouter();
  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);
  const { createRole, loading } = useRoles();

  // Find permissions for Roles module for current user's role
  let rolePermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const rolesModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "roles"
      );
      if (rolesModule) {
        rolePermissions = rolesModule;
      }
    }
  }

  // Block access if user does not have create permission
  useEffect(() => {
    if (!rolePermissions.create) {
      router.replace("/roles?error=permission");
    }
  }, [rolePermissions.create, router]);

  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const handleFormSubmit = async (data: any) => {
    setSubmitting(true);
    setFormError(null);
    try {
      await createRole(data);
      showToast.success("Role created successfully!");
      setTimeout(() => {
        router.push("/roles");
      }, 1200);
    } catch (err: any) {
      const errorMessage = err.message || "Failed to create role";
      setFormError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <AppLayout sx={{ mt: "-100px" }}>
      <Box sx={{ width: "100%", px: 0 }}>
        <Box sx={{ mb: 3, px: 0 ,mt:15 }}>
          <Typography variant="h4" fontWeight={700} gutterBottom>
            Add New Role
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create a new role and assign permissions to it
          </Typography>
        </Box>

        <RoleForm
          onSubmit={handleFormSubmit}
          onCancel={() => router.push("/roles")}
          loading={submitting || loading}
          error={formError}
          currentUserRole={currentUserRole}
        />

      </Box>
    </AppLayout>
  );
}
