"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { RoleForm } from "@/components/roles/RoleForm";
import { Box, CircularProgress, Typography, Button } from "@mui/material";
import { useRoles } from "@/context/RolesContext";
import { showToast } from "@/utils/toast";
import { useAppSelector } from "@/store/hooks";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import AppLayout from "@/layout/AppLayout";

export default function EditRolePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const uuid = searchParams.get("uuid");
  const { roles, loading, error, fetchRoles, updateRole } = useRoles();
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [localRole, setLocalRole] = useState<any>(null);
  const initialFetched = useRef(false);
  const currentUserRole = useCurrentUserRole();

  // Show form immediately with Redux data
  useEffect(() => { 
    if (roles.length > 0) {
      const found = roles.find((r) => r.uuid === uuid);
      setLocalRole(found || null);
    }
  }, [roles, uuid]);

  // Only fetch if roles are empty
  useEffect(() => {
    if (roles.length === 0 && !initialFetched.current) {
      fetchRoles();
      initialFetched.current = true;
    }
  }, [roles.length, fetchRoles]);

  // Manual refresh
  const handleManualRefresh = async () => {
    await fetchRoles();
    showToast.success("Data refreshed");
  };

  if (loading && !localRole) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!localRole) {
    return (
      <Box sx={{ p: 4 }}>
        <Typography color="error">Role not found.</Typography>
        <Button onClick={handleManualRefresh} variant="outlined" sx={{ mt: 2 }}>
          Refresh
        </Button>
      </Box>
    );
  }

  return (
    <AppLayout sx={{ mt: "-100px" }}>
      <Box sx={{ mb: 3, px: 0, mt: 15 }}>
        <Typography variant="h4" fontWeight={700} gutterBottom>
          Edit Role
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Edit the role and assign permissions to it
        </Typography>
      </Box>

      <RoleForm
        role={localRole}
        isRoleNameDisable
        onSubmit={async (data) => {
          setSubmitting(true);
          setFormError(null);
          try {
            await updateRole(localRole, data);
            showToast.success("Role updated successfully!");
            await fetchRoles();
            setTimeout(() => {
              router.push("/roles");
            }, 1200);
          } catch (err: any) {
            setFormError(err.message || "Failed to update role");
            showToast.error(err.message || "Failed to update role");
          } finally {
            setSubmitting(false);
          }
        }}
        onCancel={() => router.push("/roles")}
        error={formError}
        loading={submitting}
        currentUserRole={currentUserRole}
      />
    </AppLayout>
  );
}
