"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  Alert,
  <PERSON>readc<PERSON><PERSON>,
  Link,
} from "@mui/material";
import {
  Add as AddIcon,
  Search as SearchIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
} from "@mui/icons-material";
import { useRoles } from "@/context/RolesContext";
import { RoleTable } from "@/components/roles/RoleTable";
import { RoleForm } from "@/components/roles/RoleForm";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import { PageHeader } from "@/components/ui/PageHeader";
import { RoleGuard } from "@/components/ui/RoleGuard";
import { Role as RoleType } from "@/types/role";
import AppLayout from "@/layout/AppLayout";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { filterRoles } from "@/utils/rolesUtils";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { useRouter } from "next/navigation";

export default function RolesPage() {
  const { roles, loading, error, fetchRoles, updateRole, deleteRole } =
    useRoles();

  const currentUserRole = useCurrentUserRole();
  const router = useRouter();

  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleType | null>(null);
  const [deletingRole, setDeletingRole] = useState<RoleType | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // Filter roles using utility function
  const filteredRoles = roles.filter((role) => role?.name);

  const handleEditRole = (role: RoleType) => {
    setEditingRole(role);
    setShowEditDialog(true);
    setFormError(null);
  };

  // Handle role delete
  const handleDeleteRole = (role: RoleType) => {
    setDeletingRole(role);
    setShowDeleteDialog(true);
  };

  // Handle assign permissions (placeholder for future implementation)
  const handleAssignPermissions = (role: RoleType) => {
    // This would open a permissions management dialog
    console.log("Assign permissions for role:", role.name);
  };

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      if (editingRole) {
        const updateData = {
          ...data,
          role: {
            ...data.role,
            name: editingRole.name,
          },
        };
        await updateRole(editingRole, updateData);
      }
      setShowEditDialog(false);
      setEditingRole(null);
      setFormError(null);
    } catch (err: any) {
      setFormError(err.message || "Failed to save role");
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedRoles.length > 0) {
      for (const slug of selectedRoles) {
        await deleteRole(slug);
      }
      setSelectedRoles([]);
    }
  };

  // Use a fallback for stats if not available
  const roleStats = {
    totalRoles: roles.length,
    customRoles: roles.length - 3,
    systemRoles: 0,
  };

  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Roles module for current user's role
  let rolePermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const rolesModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "roles"
      );
      if (rolesModule) {
        rolePermissions = rolesModule;
      }
    }
  }

  return (
    <AppLayout>
      <Box sx={{ p: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 3 }}>
          <Link href="/dashboard" color="inherit" underline="hover">
            Dashboard
          </Link>
          <Typography color="text.primary">Roles</Typography>
        </Breadcrumbs>

        {/* Page Header */}
        <PageHeader
          title="Role Management"
          subtitle="Manage user roles and permissions"
          icon={<SecurityIcon />}
          actions={
            <Box sx={{ display: "flex", gap: 1 }}>
        
              {rolePermissions.create && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push("/roles/add")}
                >
                  Add Role
                </Button>
              )}
            </Box>
          }
        />

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error + " Please try again."}
          </Alert>
        )}

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <SecurityIcon color="primary" />
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {roleStats.totalRoles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Roles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Search and Actions */}
        <Box sx={{ display: "flex", gap: 2, mb: 3, flexWrap: "wrap" }}>
          {/* <TextField
            placeholder="Search roles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
          /> */}

          {selectedRoles.length > 0 && (
            <RoleGuard requiredPermissions={["roles.delete"]}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleBulkDelete}
                disabled={loading}
              >
                Delete Selected ({selectedRoles.length})
              </Button>
            </RoleGuard>
          )}
        </Box>

        {/* Roles Table */}
        <RoleTable
          roles={filteredRoles}
          onEdit={handleEditRole}
          onDelete={handleDeleteRole}
          onAssignPermissions={handleAssignPermissions}
          selectedRoles={selectedRoles}
          onSelectionChange={setSelectedRoles}
          loading={loading}
          currentUserRole={currentUserRole}
          rolePermissions={rolePermissions}
        />

        {/* Edit Role Dialog */}
        <Dialog
          open={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Edit Role</DialogTitle>
          <DialogContent>
            {editingRole && (
              <RoleForm
                role={editingRole}
                onSubmit={handleFormSubmit}
                onCancel={() => setShowEditDialog(false)}
                loading={loading}
                error={formError}
                currentUserRole={currentUserRole}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          open={showDeleteDialog}
          title="Delete Role"
          message={
            deletingRole
              ? `Are you sure you want to delete the role "${deletingRole.name}"? This action cannot be undone.`
              : ""
          }
          onConfirm={() => {
            if (deletingRole) {
              deleteRole(deletingRole.uuid || deletingRole.name);
              setShowDeleteDialog(false);
              setDeletingRole(null);
            }
          }}
          onCancel={() => setShowDeleteDialog(false)}
          loading={loading}
          confirmText="Delete"
          cancelText="Cancel"
          severity="error"
        />
      </Box>
    </AppLayout>
  );
}
