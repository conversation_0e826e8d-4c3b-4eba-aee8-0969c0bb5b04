"use client";

import React, { useState, useEffect } from "react";
import {
  Container,
  Box,
  Paper,
  Typography,
  Button,
  Avatar,
  Grid,
  Card,
  CardContent,
  Divider,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from "@mui/material";
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Security as SecurityIcon,
  VpnKey as VpnKeyIcon,
  ContentCopy as CopyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from "@mui/icons-material";
import AppLayout from "@/layout/AppLayout";
import { PageHeader } from "@/components/common/PageHeader";
import { useAppSelector } from "@/store/hooks";
import {
  getUserTokens,
  copyTokenToClipboard,
} from "@/services/userTokenService";
import { showToast } from "@/utils/toast";
import { format } from "date-fns";

interface UserProfile {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  permissions: string[];
  mfaEnabled: boolean;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tokenDialogOpen, setTokenDialogOpen] = useState(false);
  const [userTokens, setUserTokens] = useState<any>(null);
  const [showToken, setShowToken] = useState(false);
  const [copyingToken, setCopyingToken] = useState(false);
  const currentAdminUser = useAppSelector((state) => state.adminUser.user);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use current admin user from state
        if (currentAdminUser) {
          setProfile({
            id: currentAdminUser.uuid || "",
            username: currentAdminUser.username || "",
            email: currentAdminUser.email || "",
            firstName: currentAdminUser.firstName || undefined,
            lastName: currentAdminUser.lastName || undefined,
            roles: Array.isArray(currentAdminUser.roles)
              ? currentAdminUser.roles
              : [currentAdminUser.roles],
            permissions:
              currentAdminUser.permissions?.map((p) =>
                Object.keys(p).join(", ")
              ) || [],
            mfaEnabled: false, // Admin type doesn't have mfaEnabled
            emailVerified: currentAdminUser.emailVerified || false,
            createdAt: currentAdminUser.dateCreated || "",
            lastLoginAt: currentAdminUser.lastlogin || undefined,
          });
        } else {
          throw new Error("No user data available");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load profile");
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [currentAdminUser]);

  const handleGetToken = async () => {
    try {
      setLoading(true);
      const tokens = await getUserTokens();
      setUserTokens(tokens);
      setTokenDialogOpen(true);
    } catch (err) {
      showToast.error("Failed to get user tokens");
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToken = async () => {
    if (!userTokens?.accessToken) return;

    try {
      setCopyingToken(true);
      await copyTokenToClipboard(userTokens.accessToken);
      showToast.success("Token copied to clipboard");
    } catch (err) {
      showToast.error("Failed to copy token");
    } finally {
      setCopyingToken(false);
    }
  };

  const getDisplayName = () => {
    if (profile?.firstName && profile?.lastName) {
      return `${profile.firstName} ${profile.lastName}`;
    }
    return profile?.username || "User";
  };

  const getInitials = () => {
    if (profile?.firstName && profile?.lastName) {
      return `${profile.firstName[0]}${profile.lastName[0]}`.toUpperCase();
    }
    return profile?.username?.substring(0, 2).toUpperCase() || "U";
  };

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            <Box sx={{ display: "flex", justifyContent: "center", py: 8 }}>
              <CircularProgress />
            </Box>
          </Box>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="lg">
        <Box sx={{ py: 3 }}>
          <PageHeader
            title="User Profile"
            subtitle="Manage your account information and access tokens"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Profile" },
            ]}
          />

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {profile && (
            <Grid container spacing={3}>
              {/* Profile Overview Card */}
              <Grid item xs={12} md={6}>
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                    <Avatar
                      sx={{
                        width: 80,
                        height: 80,
                        bgcolor: "#1e3a8a",
                        fontSize: "2rem",
                        mr: 3,
                      }}
                    >
                      {getInitials()}
                    </Avatar>
                    <Box>
                      <Typography variant="h4" component="h1" gutterBottom>
                        {getDisplayName()}
                      </Typography>
                      <Typography variant="body1" color="text.secondary">
                        {profile.email}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        {profile.roles.map((role) => (
                          <Chip
                            key={role}
                            label={role}
                            size="small"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  </Box>


                </Paper>
              </Grid>

              {/* Actions Card */}
              <Grid item xs={12} md={6}>
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Quick Actions
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<VpnKeyIcon />}
                    onClick={handleGetToken}
                    disabled={loading}
                    sx={{ mb: 2 }}
                  >
                    Get Access Token
                  </Button>

                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 2 }}
                  >
                    Use this token for API authentication in external
                    applications.
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          )}
        </Box>
      </Container>

      {/* Token Dialog */}
      <Dialog
        open={tokenDialogOpen}
        onClose={() => setTokenDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <VpnKeyIcon sx={{ mr: 1, color: "#1e3a8a" }} />
            Access Token
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Your access token for API authentication. Keep this token secure and
            do not share it.
          </Typography>

          <Box sx={{ position: "relative" }}>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={
                showToken
                  ? userTokens?.accessToken || ""
                  : "••••••••••••••••••••••••••••••••"
              }
              variant="outlined"
              InputProps={{
                readOnly: true,
                endAdornment: (
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Tooltip title={showToken ? "Hide token" : "Show token"}>
                      <IconButton
                        onClick={() => setShowToken(!showToken)}
                        size="small"
                      >
                        {showToken ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy token">
                      <IconButton
                        onClick={handleCopyToken}
                        disabled={copyingToken}
                        size="small"
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                ),
              }}
            />
          </Box>

          {userTokens && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Token Type:</strong> {userTokens.tokenType}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTokenDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </AppLayout>
  );
}
