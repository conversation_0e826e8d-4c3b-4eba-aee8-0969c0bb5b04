// Organizations List Page
"use client";

import React, { useState, useEffect } from "react";
import {
  Container,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
} from "@mui/material";
import { Add as AddIcon, Business as BusinessIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

// Components
import OrganizationList from "@/components/organizations/OrganizationList";
import OrganizationFilters from "@/components/organizations/OrganizationFilters";
import { PageHeader } from "@/components/common/PageHeader";
import AppLayout from "@/layout/AppLayout";

// Redux imports
import { AppDispatch, RootState } from "@/store";
import { organizationsActions } from "@/store/organizations/redux";
import { organizationsSelectors } from "@/store/organizations/selector";

// Types
import { NextPage } from "next";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { OrganizationFilterParams } from "@/types/organization";

const OrganizationsPage: NextPage = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const error = useSelector(organizationsSelectors.selectError);
  const loading = useSelector(organizationsSelectors.selectLoading);
  const filters = useSelector(
    (state: RootState) => state.organizations.filters
  );
  const currentUserRole = useCurrentUserRole();
  const organizationsByIndustry = useSelector(
    organizationsSelectors.selectOrganizationsByIndustry
  );

  const organizationSummary = useSelector(
    organizationsSelectors.selectOrganizationSummary
  );

  // Handle filter changes
  const handleFiltersChange = (newFilters: OrganizationFilterParams) => {
    dispatch(organizationsActions.setFilters(newFilters));
    dispatch(organizationsActions.fetchOrganizations(newFilters));
  };

  // Handle refresh
  const handleRefresh = () => {
    dispatch(organizationsActions.fetchOrganizations(filters));
  };

  // Handle create organization
  const handleCreateOrganization = () => {
    router.push("/organizations/create");
  };

  // Load organizations on mount
  useEffect(() => {
    dispatch(organizationsActions.fetchOrganizations(filters));
  }, [dispatch]);

  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let orgPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const orgModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "organizations"
      );
      if (orgModule) {
        orgPermissions = orgModule;
      }
    }
  }

  const pagination = useSelector(organizationsSelectors.selectPagination);

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          {/* Page Header */}
          <PageHeader
            title="Organization Management"
            subtitle="Manage your organizations and their information"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Organizations" },
            ]}
            actions={
              orgPermissions.create && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateOrganization}
                  sx={{ minWidth: 150 }}
                >
                  Create Organization
                </Button>
              )
            }
          />

          {error && (
            <Alert
              severity="error"
              sx={{ mb: 3 }}
              onClose={() => dispatch(organizationsActions.clearError())}
            >
              {error}
            </Alert>
          )}

          {/* Statistics Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h4"
                    sx={{ fontWeight: 600, color: "primary.main" }}
                  >
                    {pagination?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Organizations
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h4"
                    sx={{ fontWeight: 600, color: "info.main" }}
                  >
                    {Object.keys(organizationsByIndustry).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Industries
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent sx={{ textAlign: "center" }}>
                  <Typography
                    variant="h4"
                    sx={{ fontWeight: 600, color: "secondary.main" }}
                  >
                    <BusinessIcon sx={{ fontSize: "inherit" }} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Filters:{" "}
                    {
                      Object.keys(filters).filter(
                        (key) =>
                          filters[key as keyof OrganizationFilterParams] &&
                          !["page", "pageSize", "sortBy", "sortOrder"].includes(
                            key
                          )
                      ).length
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Organization Filters */}
          <OrganizationFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onRefresh={handleRefresh}
            loading={loading}
          />

          {/* Organizations Table */}
          <Box sx={{ minHeight: "calc(100vh - 400px)" }}>
            <OrganizationList />
          </Box>
        </Box>
      </Container>
    </AppLayout>
  );
};

export default OrganizationsPage;
