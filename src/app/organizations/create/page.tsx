// Create Organization Page
"use client";

import React, { useEffect, useState } from "react";
import { Container, Box, Breadcrumbs, Link, Alert } from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";

// Components
import OrganizationForm from "@/components/organizations/OrganizationForm";
import AppLayout from "@/layout/AppLayout";

// Redux imports
import { RootState } from "@/store";
import { organizationsSelectors } from "@/store/organizations/selector";

// Types
import { NextPage } from "next";

const CreateOrganizationPage: NextPage = () => {
  const router = useRouter();
  const createLoading = useSelector(organizationsSelectors.selectCreateLoading);
  const error = useSelector(organizationsSelectors.selectError);

  // Local state to track if form has been submitted
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Breadcrumb navigation
  const breadcrumbs = [
    {
      label: "Home",
      icon: <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/dashboard",
    },
    {
      label: "Organizations",
      icon: <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/organizations",
    },
    {
      label: "Create Organization",
      icon: <AddIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/organizations/create",
    },
  ];

  const handleBreadcrumbClick = (href: string) => {
    router.push(href);
  };

  // Handle cancel
  const handleCancel = () => {
    router.push("/organizations");
  };

  // Handle form submission
  const handleFormSubmit = () => {
    setHasSubmitted(true);
  };

  // Handle successful creation - only redirect after successful submission
  useEffect(() => {
    if (hasSubmitted && !createLoading && !error) {
      setShowSuccess(true);
      // Delay redirect to show success message
      setTimeout(() => {
        router.push("/organizations");
      }, 2000);
    }
  }, [hasSubmitted, createLoading, error, router]);

  return (
    <AppLayout>
      <Container maxWidth="lg" sx={{ py: 3 }}>
        {/* Breadcrumbs */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs aria-label="breadcrumb">
            {breadcrumbs.map((breadcrumb, index) => (
              <Link
                key={index}
                color={
                  index === breadcrumbs.length - 1 ? "text.primary" : "inherit"
                }
                href={breadcrumb.href}
                onClick={(e) => {
                  e.preventDefault();
                  handleBreadcrumbClick(breadcrumb.href);
                }}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration:
                      index === breadcrumbs.length - 1 ? "none" : "underline",
                  },
                }}
              >
                {breadcrumb.icon}
                {breadcrumb.label}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>

        {/* Success Alert */}
        {showSuccess && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Organization created successfully! Redirecting to organizations
            list...
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Page Content */}
        <Box sx={{ minHeight: "calc(100vh - 200px)" }}>
          <OrganizationForm
            mode="create"
            onCancel={handleCancel}
            onSubmit={handleFormSubmit}
          />
        </Box>
      </Container>
    </AppLayout>
  );
};

export default CreateOrganizationPage;
