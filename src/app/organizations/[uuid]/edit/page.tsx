// Edit Organization Page
"use client";

import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import {
  Container,
  Box,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
} from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
} from "@mui/icons-material";

// Components
import OrganizationForm from "../../../../components/organizations/OrganizationForm";
import AppLayout from "../../../../layout/AppLayout";

// Redux imports
import { AppDispatch, RootState } from "../../../../store";
import { organizationsActions } from "../../../../store/organizations/redux";
import { organizationsSelectors } from "../../../../store/organizations/selector";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { useAppSelector } from "@/store/hooks";
import { selectAllRolesPermissions } from "@/store/roles/selector";

// Types
import { NextPage } from "next";

const EditOrganizationPage: NextPage = () => {
  const params = useParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const uuid = params.uuid as string;

  // Redux state
  const organization = useSelector(
    organizationsSelectors.selectCurrentOrganization
  );
  const updateLoading = useSelector(organizationsSelectors.selectUpdateLoading);
  const loading = useSelector(organizationsSelectors.selectLoading);
  const error = useSelector(organizationsSelectors.selectError);

  // Local state
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Load organization data on mount
  useEffect(() => {
    if (uuid) {
      dispatch(organizationsActions.fetchOrganization(uuid));
    }
  }, [uuid, dispatch]);

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let orgPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const orgModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "organizations"
      );
      if (orgModule) {
        orgPermissions = orgModule;
      }
    }
  }

  // Block access if user does not have update permission
  useEffect(() => {
    if (!orgPermissions.update) {
      router.replace(`/organizations/${uuid}?error=permission`);
    }
  }, [orgPermissions.update, router, uuid]);

  // Handle form submission
  const handleFormSubmit = () => {
    setHasSubmitted(true);
  };

  // Handle successful update
  useEffect(() => {
    if (hasSubmitted && !updateLoading && !error) {
      setShowSuccess(true);
      // Delay redirect to show success message
      setTimeout(() => {
        router.push(`/organizations/${uuid}`);
      }, 2000);
    }
  }, [hasSubmitted, updateLoading, error, router, uuid]);

  // Handle cancel
  const handleCancel = () => {
    router.push(`/organizations/${uuid}`);
  };

  // Breadcrumb navigation
  const breadcrumbs = [
    {
      label: "Home",
      icon: <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/dashboard",
    },
    {
      label: "Organizations",
      icon: <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: "/organizations",
    },
    {
      label: organization?.name || "Loading...",
      icon: <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: `/organizations/${uuid}`,
    },
    {
      label: "Edit",
      icon: <EditIcon sx={{ mr: 0.5 }} fontSize="inherit" />,
      href: `/organizations/${uuid}/edit`,
    },
  ];

  const handleBreadcrumbClick = (href: string) => {
    router.push(href);
  };

  // Loading state
  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="400px"
          >
            <CircularProgress />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Link
              href="/organizations"
              sx={{ textDecoration: "none" }}
              onClick={(e) => {
                e.preventDefault();
                router.push("/organizations");
              }}
            >
              Back to Organizations
            </Link>
            <Link
              href={`/organizations/${uuid}`}
              sx={{ textDecoration: "none" }}
              onClick={(e) => {
                e.preventDefault();
                router.push(`/organizations/${uuid}`);
              }}
            >
              View Organization
            </Link>
          </Box>
        </Container>
      </AppLayout>
    );
  }

  // Not found state
  if (!organization) {
    return (
      <AppLayout>
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            Organization not found
          </Alert>
          <Link
            href="/organizations"
            sx={{ textDecoration: "none" }}
            onClick={(e) => {
              e.preventDefault();
              router.push("/organizations");
            }}
          >
            Back to Organizations
          </Link>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="lg" sx={{ py: 3 }}>
        {/* Breadcrumbs */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs aria-label="breadcrumb">
            {breadcrumbs.map((breadcrumb, index) => (
              <Link
                key={index}
                color={
                  index === breadcrumbs.length - 1 ? "text.primary" : "inherit"
                }
                href={breadcrumb.href}
                onClick={(e) => {
                  e.preventDefault();
                  handleBreadcrumbClick(breadcrumb.href);
                }}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration:
                      index === breadcrumbs.length - 1 ? "none" : "underline",
                  },
                }}
              >
                {breadcrumb.icon}
                {breadcrumb.label}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>

        {/* Success Alert */}
        {showSuccess && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Organization updated successfully! Redirecting to organization
            view...
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Page Content */}
        <Box sx={{ minHeight: "calc(100vh - 200px)" }}>
          <OrganizationForm
            organization={organization}
            mode="edit"
            onCancel={handleCancel}
            onSubmit={handleFormSubmit}
          />
        </Box>
      </Container>
    </AppLayout>
  );
};

export default EditOrganizationPage;
