"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Autocomplete,
  FormControlLabel,
  Checkbox,
  Divider,
} from "@mui/material";
import { Search as SearchIcon, Add as AddIcon } from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { MemberTable } from "@/components/members/MemberTable";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import { membersService } from "@/services/members";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import {
  StatsCardSkeleton,
  PageLoading,
  useLoading,
} from "@/components/common";
import {
  Member,
  MemberWithRelations,
  MemberWithOrganizations,
  MemberFilters,
  MemberSearchFilters,
  MemberSearchParams,
  MemberExportRequest,
} from "@/types/member";
import AppLayout from "@/layout/AppLayout";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchMembersRequest,
  exportMembersRequest,
  selectMembersArray,
  selectMembersLoading,
  selectMembersError,
  selectMembersLastFetched,
  selectMembersPagination,
  setCurrentPage,
  setPageSize,
} from "@/store/members/redux";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import { selectAllRolesPermissions } from "@/store/roles/selector";
import { MemberFilters as MemberFiltersComponent } from "@/components/members/MemberFilters";
import { ExportModal } from "@/components/members/ExportModal";

export default function MembersPage() {
  const { setIsLoading, setLoadingMessage } = useLoading();

  const dispatch = useAppDispatch();
  const members = useAppSelector(selectMembersArray);
  const loading = useAppSelector(selectMembersLoading);
  const error = useAppSelector(selectMembersError);
  const lastFetched = useAppSelector(selectMembersLastFetched);
  const pagination = useAppSelector(selectMembersPagination);
  console.log(members, "membersmembers");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const router = useRouter();

  const currentUserRole = useCurrentUserRole();
  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Members module for current user's role
  let memberPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const memberModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "members"
      );
      if (memberModule) {
        memberPermissions = memberModule;
      }
    }
  }

  // Table state - now managed by Redux
  const [sortBy, setSortBy] = useState<string>("dateCreated");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Filter state using new API structure
  const [filters, setFilters] = useState<MemberSearchFilters>({
    search: "",
    firstName: "",
    lastName: "",
    email: "",
    membershipTier: "",
    communityStatus: "",
    verificationStatus: "",
    organizationName: "",
    organizationCity: "",
    organizationState: "",
    organizationZip: "",
    companySize: "",
    industry: "",
    dateCreatedFrom: "",
    dateCreatedTo: "",
  });

  useEffect(() => {
    dispatch(fetchMembersRequest({ filters }));
  }, [dispatch]);

  const handleFiltersChange = (newFilters: MemberSearchFilters) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    dispatch(fetchMembersRequest({ filters, page: 1 }));
  };

  const handleClearFilters = () => {
    const clearedFilters: MemberSearchFilters = {
      search: "",
      firstName: "",
      lastName: "",
      email: "",
      membershipTier: "",
      communityStatus: "",
      verificationStatus: "",
      organizationName: "",
      organizationCity: "",
      organizationState: "",
      organizationZip: "",
      companySize: "",
      industry: "",
      dateCreatedFrom: "",
      dateCreatedTo: "",
    };
    setFilters(clearedFilters);
    dispatch(fetchMembersRequest({ filters: clearedFilters, page: 1 }));
  };

  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    dispatch(
      fetchMembersRequest({
        filters,
        sortBy: newSortBy,
        sortOrder: newSortOrder,
      })
    );
  };

  const handlePageChange = (newPage: number) => {
    dispatch(setCurrentPage(newPage));
    dispatch(fetchMembersRequest({ filters, page: newPage }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    dispatch(setPageSize(newPageSize));
    dispatch(fetchMembersRequest({ filters, pageSize: newPageSize, page: 1 }));
  };

  const handleSelectionChange = (newSelectedIds: string[]) => {
    setSelectedIds(newSelectedIds);
  };

  const handleEdit = (member: Member) => {
    router.push(`/members/edit/${member.uuid}`);
  };

  const handleDelete = (member: Member) => {
    setMemberToDelete(member);
    setShowDeleteDialog(true);
  };

  const handleView = (member: Member) => {
    router.push(`/members/${member.uuid}`);
  };

  const handleVerify = async (memberId: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying member...");

      await membersService.verifyMember({
        memberId,
        verificationType: "manual",
        data: {},
      });

      dispatch(fetchMembersRequest());
    } catch (err: any) {
      console.error("Failed to verify member:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handlEemailVerify = async (memberId: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying email...");

      await membersService.verifyemail(memberId);
      dispatch(fetchMembersRequest());
    } catch (err: any) {
      console.error("Failed to verify email:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleStatusChange = async (memberId: string, status: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Updating member status...");

      await membersService.updateMemberStatus(memberId, status);
      dispatch(fetchMembersRequest());
    } catch (err: any) {
      console.error("Failed to update member status:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) return;
    // TODO: Implement bulk delete with confirmation
    console.log("Bulk delete:", selectedIds);
  };

  const handleBulkExport = async () => {
    if (selectedIds.length === 0) return;
    setShowExportModal(true);
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const handleExportSubmit = (exportData: {
    filters: any;
    selectedFields: string[];
    notes: string;
  }) => {
    // Merge current page filters with export data
    const exportPayload = {
      filters: { ...filters, ...exportData.filters },
      selectedFields: exportData.selectedFields,
      notes: exportData.notes,
    };

    dispatch(exportMembersRequest(exportPayload));
    setShowExportModal(false);
  };

  const handleBulkVerify = async () => {
    if (selectedIds.length === 0) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Verifying selected members...");

      await membersService.bulkAction({
        memberIds: selectedIds,
        action: "verify",
      });

      setSelectedIds([]);
      dispatch(fetchMembersRequest());
    } catch (err: any) {
      console.error("Failed to verify selected members:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleAddMember = () => {
    router.push("/members/add");
  };

  const confirmDelete = async () => {
    if (!memberToDelete) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Deleting member...");

      await membersService.deleteMember(memberToDelete.uuid);
      setShowDeleteDialog(false);
      setMemberToDelete(null);
      dispatch(fetchMembersRequest());
    } catch (err: any) {
      console.error("Failed to delete member:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };
  const hasActiveFilters = Object.values(filters).some(
    (value) => value && value !== ""
  );

  // Show loading state
  if (loading && !members) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <PageLoading message="Loading members..." />
        </Container>
      </AppLayout>
    );
  }

  return (
    <ErrorBoundary isPageLevel={true}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                alignItems: { xs: "flex-start", md: "center" },
                justifyContent: "space-between",
                mb: 4,
                gap: { xs: 2, md: 0 },
              }}
            >
              <Box>
                <Typography
                  variant="h3"
                  component="h1"
                  gutterBottom
                  sx={{
                    color: "#1e3a8a",
                    fontWeight: 700,
                    fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.5rem" },
                  }}
                >
                  Members Management
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: "0.875rem", md: "1rem" },
                    display: { xs: "none", sm: "block" },
                  }}
                >
                  Manage and organize your chamber members with advanced
                  filtering and bulk operations
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  flexDirection: { xs: "column", sm: "row" },
                  width: { xs: "100%", sm: "auto" },
                }}
              >
                {memberPermissions.create && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddMember}
                    fullWidth={false}
                    sx={{ minWidth: { xs: "100%", sm: "auto" } }}
                  >
                    <Box sx={{ display: { xs: "none", sm: "block" } }}>
                      Add Member
                    </Box>
                    <Box sx={{ display: { xs: "block", sm: "none" } }}>Add</Box>
                  </Button>
                )}
              </Box>
            </Box>

            {/* Stats cards removed due to missing 'status' property on Member */}

            {/* Search and Filters */}
            <MemberFiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onApplyFilters={handleApplyFilters}
              onClearFilters={handleClearFilters}
              loading={loading}
            />

            {/* Members Table */}
            <MemberTable
              members={members as any}
              loading={loading}
              hasActiveFilters={hasActiveFilters}
              selectedIds={selectedIds}
              onSelectionChange={handleSelectionChange}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
              onVerify={handleVerify}
              onemailVerify={handlEemailVerify}
              onStatusChange={handleStatusChange}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={pagination.currentPage}
              pageSize={pagination.pageSize}
              total={pagination.totalCount}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onBulkDelete={handleBulkDelete}
              onBulkExport={handleBulkExport}
              onBulkVerify={handleBulkVerify}
              onExport={handleExport}
              memberPermissions={memberPermissions}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Member"
              message={`Are you sure you want to delete ${memberToDelete?.firstName} ${memberToDelete?.lastName}? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setMemberToDelete(null);
              }}
            />

            {/* Export Modal */}
            <ExportModal
              open={showExportModal}
              onClose={() => setShowExportModal(false)}
              onExport={handleExportSubmit}
              currentFilters={filters}
              loading={loading}
            />
          </Box>
        </Container>
      </AppLayout>
    </ErrorBoundary>
  );
}
