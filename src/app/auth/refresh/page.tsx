'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { refreshTokenIfNeeded } from '@/utils/simpleTokenRefresh';
import { Box, CircularProgress, Typography, Al<PERSON>, Button } from '@mui/material';

export default function RefreshPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get('returnUrl') || '/dashboard';
  
  const [status, setStatus] = useState<'refreshing' | 'success' | 'error'>('refreshing');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const performRefresh = async () => {
      try {
        setStatus('refreshing');
        console.log('🔄 Attempting to refresh tokens...');
        
        // Attempt to refresh tokens
        const success = await refreshTokenIfNeeded();
        
        if (success) {
          setStatus('success');
          console.log('✅ Tokens refreshed successfully');
          
          // Wait a moment for tokens to be synced, then redirect
          setTimeout(() => {
            router.replace(returnUrl);
          }, 1000);
        } else {
          setStatus('error');
          setError('Failed to refresh tokens. Please login again.');
          console.error('❌ Token refresh failed');
        }
      } catch (error: any) {
        console.error('Refresh error:', error);
        setStatus('error');
        setError(error.message || 'An unexpected error occurred');
      }
    };

    performRefresh();
  }, [router, returnUrl]);

  const handleGoToLogin = () => {
    router.replace(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      padding={3}
      bgcolor="background.default"
    >
      <Box
        maxWidth={400}
        width="100%"
        textAlign="center"
        bgcolor="background.paper"
        borderRadius={2}
        padding={4}
        boxShadow={3}
      >
        {status === 'refreshing' && (
          <>
            <CircularProgress size={48} sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>
              Refreshing your session...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Please wait while we update your authentication tokens.
            </Typography>
          </>
        )}

        {status === 'success' && (
          <>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                bgcolor: 'success.main',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 24px',
                color: 'white'
              }}
            >
              ✓
            </Box>
            <Typography variant="h6" gutterBottom>
              Session refreshed successfully!
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Redirecting you back to your page...
            </Typography>
          </>
        )}

        {status === 'error' && (
          <>
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Session Refresh Failed
              </Typography>
              <Typography variant="body2">
                {error}
              </Typography>
            </Alert>
            
            <Button
              variant="contained"
              onClick={handleGoToLogin}
              fullWidth
            >
              Go to Login
            </Button>
          </>
        )}
      </Box>
    </Box>
  );
}
