"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import {
  Search as SearchIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { AdminListTable } from "@/components/admin/AdminListTable";
import { AdminUserForm } from "@/components/admin/AdminUserForm";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import { RoleGuard } from "@/components/auth/RoleGuard";
import { PageHeader } from "@/components/common/PageHeader";
import AppLayout from "@/layout/AppLayout";
import { navigate } from "@/utils/routerService";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchAdminUsersListRequest,
  deleteAdminUserRequest,
  setSelectedAdminUser,
  clearAdminUsersError,
  setCurrentPageAdmin,
  setPageSizeAdmin,
} from "@/store/adminUser/redux";
import {
  selectAdminUsersList,
  selectAdminUsersLoading,
  selectAdminUsersError,
  selectAdminUsersStats,
  selectDeleteLoading,
  selectShouldRefreshAdminUsers,
  selectAdminUsersPagination,
  useCurrentUserRole,
} from "@/store/adminUser/selector";
import { showToast } from "@/utils/toast";
import { Admin } from "@/types/adminUser";
import { selectAllRolesPermissions } from "@/store/roles/selector";

export default function AdminUsersPage() {
  const dispatch = useAppDispatch();

  // Redux state
  const adminUsersList = useAppSelector(selectAdminUsersList);
  const loading = useAppSelector(selectAdminUsersLoading);
  const error = useAppSelector(selectAdminUsersError);
  const deleteLoading = useAppSelector(selectDeleteLoading);
  const pagination = useAppSelector(selectAdminUsersPagination);

  // Local UI state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [adminUserToDelete, setAdminUserToDelete] = useState<Admin | null>(
    null
  );

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = useCurrentUserRole();


  const allRolesPermissions = useAppSelector(selectAllRolesPermissions);

  // Find permissions for Organizations module for current user's role
  let adminPermissions = {
    view: false,
    create: false,
    update: false,
    delete: false,
  };
  if (allRolesPermissions && allRolesPermissions.roles) {
    const role = allRolesPermissions.roles.find(
      (r) => r.roleSlug === currentUserRole
    );
    if (role) {
      const adminModule = role.modulePermissions.find(
        (m: any) => m.moduleSlug === "admin_users"
      );
      if (adminModule) {
        adminPermissions = adminModule;
      }
    }
  }

  // Table state - now managed by Redux
  const [sortBy, setSortBy] = useState<string>("username");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Filter state
  const [filters, setFilters] = useState({
    search: "",
    role: "all",
  });

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchAdminUsersListRequest());
  }, [dispatch]);

  
  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    dispatch(setCurrentPageAdmin(1)); // Reset to first page when filtering
    dispatch(fetchAdminUsersListRequest());
  };

  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    dispatch(fetchAdminUsersListRequest());
  };

  const handlePageChange = (newPage: number) => {
    dispatch(setCurrentPageAdmin(newPage));
    dispatch(fetchAdminUsersListRequest());
  };

  const handlePageSizeChange = (newPageSize: number) => {
    dispatch(setPageSizeAdmin(newPageSize));
    dispatch(fetchAdminUsersListRequest());
  };

  const handleCreateAdminUser = async () => {
      setShowAddDialog(false);
  };

  const confirmDelete = async () => {
    if (!adminUserToDelete) return;

    try {
      const loadingToast = showToast.loading("Deleting admin user...");

      // Use Redux action for delete
      dispatch(deleteAdminUserRequest(adminUserToDelete.uuid));

      setTimeout(() => {
        showToast.dismiss(loadingToast);
        if (!deleteLoading) {
          showToast.success("Admin user deleted successfully!");
          setShowDeleteDialog(false);
          setAdminUserToDelete(null);
          // Data will be automatically updated via Redux
        }
      }, 1000);
    } catch (err: any) {
      showToast.error("Failed to delete admin user");
    }
  };

  const handleEdit = (user: Admin) => {
    // Set as selected admin user
    dispatch(setSelectedAdminUser(user));
    navigate(`/admin-users/${user.uuid}`);
  };

  const handleDelete = (user: Admin) => {
    setAdminUserToDelete(user);
    setShowDeleteDialog(true);
  };

  return (
    <RoleGuard requiredRole="super_admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Page Header */}
            <PageHeader
              title="Admin User Management"
              subtitle="Manage system administrators and their permissions"
              breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Admin Users" },
              ]}
              actions={
                <Button
                  variant="contained"
                  disabled={!adminPermissions.create}
                  startIcon={<AddIcon />}
                  onClick={() => navigate("/admin-users/create")}
                  sx={{ minWidth: 150 }}
                >
                  Add Admin User
                </Button>
              }
            />

            {error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                onClose={() => dispatch(clearAdminUsersError())}
              >
                {error}
              </Alert>
            )}

            {/* Statistics Cards */}
 

            {/* Search and Filters */}
            {/* <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="Search admin users..."
                      value={filters.search}
                      onChange={handleSearchChange}
                      InputProps={{
                        startAdornment: (
                          <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={8}>
                    <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                      <FormControl size="small" sx={{ minWidth: 120 }}>
                        <InputLabel>Role</InputLabel>
                        <Select
                          value={filters.role}
                          label="Role"
                          onChange={(e) =>
                            handleFilterChange("role", e.target.value)
                          }
                        >
                          <MenuItem value="all">All Roles</MenuItem>
                          <MenuItem value="super_admin">Super Admin</MenuItem>
                          <MenuItem value="admin">Admin</MenuItem>
                          <MenuItem value="moderator">Moderator</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card> */}

            {/* Admin Users Table */}
            <AdminListTable
              adminPermissions={adminPermissions}
              users={adminUsersList.map((user) => ({
                uuid: user.uuid,
                username: user.username,
                email: user.email,
                roles: Array.isArray(user.roles) ? user.roles : [user.roles],
                last_login: null, // AdminUser doesn't have this field
                phone: user.phone || undefined,
                countrycode: user.countrycode || undefined,
                firstName: user.firstName,
                lastName: user.lastName,
                createdBy: {
                  uuid: user.createdBy,
                  username: "Unknown", // AdminUser doesn't have creator username
                },
              }))}
              loading={loading}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={pagination.currentPage}
              pageSize={pagination.pageSize}
              total={pagination.totalCount}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onEdit={(user) =>
                handleEdit(adminUsersList.find((u) => u.uuid === user.uuid)!)
              }
              onDelete={(user) =>
                handleDelete(adminUsersList.find((u) => u.uuid === user.uuid)!)
              }
              currentUserRole={currentUserRole}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Admin User"
              message={`Are you sure you want to delete ${adminUserToDelete?.username} (${adminUserToDelete?.email})? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setAdminUserToDelete(null);
              }}
            />

            {/* Add Admin User Dialog */}
            <Dialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              maxWidth="md"
              fullWidth
              PaperProps={{
                sx: {
                  minHeight: "80vh",
                  maxHeight: "90vh",
                },
              }}
            >
              <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <AddIcon color="primary" />
                  Add New Admin User
                </Box>
              </DialogTitle>
              <DialogContent sx={{ pb: 0 }}>
                <AdminUserForm
                  currentUserRole={currentUserRole}
                  onSubmit={handleCreateAdminUser}
                  onCancel={() => setShowAddDialog(false)}
                />
              </DialogContent>
            </Dialog>
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
}
