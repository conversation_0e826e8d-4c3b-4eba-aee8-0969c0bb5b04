"use client";

import React, { useC<PERSON>back, useEffect, useState } from "react";
import { Container, Box, Typography, Alert } from "@mui/material";
import { AdminUserForm } from "@/components/admin/AdminUserForm";
import { PageHeader } from "@/components/common/PageHeader";
import { RoleGuard } from "@/components/auth/RoleGuard";
import AppLayout from "@/layout/AppLayout";
import { navigate } from "@/utils/routerService";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  createAdminUserRequest,
  clearAdminUsersError,
} from "@/store/adminUser/redux";
import {
  selectCreateLoading,
  selectAdminUsersError,
} from "@/store/adminUser/selector";
import { CreateAdminUserRequest } from "@/types/adminUser";

export default function CreateAdminUserPage() {
  const dispatch = useAppDispatch();
  const [hasSubmitted, setHasSubmitted] = useState(false);

  // Redux state
  const loading = useAppSelector(selectCreateLoading);
  const error = useAppSelector(selectAdminUsersError);

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = "super_admin";

  // Simple redirect logic - triggered when admin user is successfully created
  useEffect(() => {
    // Only redirect if we actually submitted a form and it completed successfully
    if (hasSubmitted && !loading && !error) {
      // Admin user creation completed successfully
      const timer = setTimeout(() => {
        navigate("/admin-users");
      }, 3000); // Give time for saga toasts to show

      return () => clearTimeout(timer);
    }
  }, [hasSubmitted, loading, error]);

  const handleSubmit = useCallback(
    async (data: CreateAdminUserRequest) => {
      try {
        // Clear any previous errors
        dispatch(clearAdminUsersError());

        // Mark that we've attempted a submission
        setHasSubmitted(true);

        // Ensure roles is always provided
        const createData = {
          ...data,
          roles: data.roles || ["admin"],
        };

        // Dispatch Redux action to create admin user
        dispatch(createAdminUserRequest(createData));
      } catch (err: any) {
        console.error("Submit error:", err);
      }
    },
    [dispatch]
  );

  const handleCancel = useCallback(() => {
    navigate("/admin-users");
  }, []);

  return (
    <RoleGuard requiredRole="super_admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Page Header */}
            <PageHeader
              title="Create Admin User"
              subtitle="Add a new administrator to the system"
              breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Admin Users", href: "/admin-users" },
                { label: "Create Admin User" },
              ]}
            />

            {/* Error Alert - only for validation errors now, saga handles API errors */}
            {error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                onClose={() => dispatch(clearAdminUsersError())}
              >
                {error}
              </Alert>
            )}

            {/* Admin User Form */}
            <AdminUserForm
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              loading={loading}
              error={error}
              currentUserRole={currentUserRole}
            />
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
}
