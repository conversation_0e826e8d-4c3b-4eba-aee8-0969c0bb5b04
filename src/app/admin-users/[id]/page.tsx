
"use client";

import React, { useEffect, useCallback, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { Container, Box, Paper, CircularProgress, Alert, Button } from "@mui/material";
import AppLayout from "@/layout/AppLayout";
import { PageHeader } from "@/components/common/PageHeader";
import { RoleGuard } from "@/components/auth/RoleGuard";
import { AdminUserForm } from "@/components/admin/AdminUserForm";
import { showToast } from "@/utils/toast";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  updateAdminUserRequest,
  fetchSingleAdminUserRequest,
} from "@/store/adminUser/redux";
import {
  selectAdminUserById,
  selectUpdateLoading,
  selectAdminUsersError,
  selectAdminUsersLoading,
} from "@/store/adminUser/selector";

export default function AdminUserPage() {
  const router = useRouter();
  const params = useParams();
  const dispatch = useAppDispatch();

  const id = params?.id as string | undefined;
  const isCreateMode = id === "create";

  // Redux state using optimized selectors
  const adminUser = useAppSelector(state =>
    id && !isCreateMode ? selectAdminUserById(state, id) : null
  );
  const loading = useAppSelector(selectAdminUsersLoading);
  const updateLoading = useAppSelector(selectUpdateLoading);
  const error = useAppSelector(selectAdminUsersError);

  // Mock current user role - replace with real auth context if available
  const currentUserRole = "super_admin";

  // Track if we're currently fetching data for this specific user
  const [isFetchingUser, setIsFetchingUser] = useState(false);



  // Fetch user for edit mode - always fetch when ID is present
  useEffect(() => {
    if (!isCreateMode && id) {
      // Always fetch data when ID is present, regardless of cache
      setIsFetchingUser(true);
      dispatch(fetchSingleAdminUserRequest(id));
    }
  }, [id, isCreateMode, dispatch]);

  // Update fetching state when loading changes
  useEffect(() => {
    if (!loading && isFetchingUser) {
      setIsFetchingUser(false);
    }
  }, [loading, isFetchingUser]);

  // Handle form submit
  const handleSubmit = useCallback(
    async (data: any) => {
      try {
        if (isCreateMode) {
          // AdminUserForm handles create logic internally
          return;
        } else if (id) {
          const loadingToast = showToast.loading("Updating admin user...");

          // Use Redux action for update
          dispatch(updateAdminUserRequest({ uuid: id, data }));

          // Note: In a real app, you'd wait for the saga to complete
          // For now, we'll show success and redirect after a delay
          setTimeout(() => {
            showToast.dismiss(loadingToast);
            if (!updateLoading) {
              showToast.success("Admin user updated successfully!");
              router.push("/admin-users");
            }
          }, 1000);
        }
      } catch (err: any) {
        showToast.error(err.message || "An error occurred");
      }
    },
    [id, isCreateMode, router, dispatch, updateLoading]
  );

  const handleCancel = useCallback(() => {
    router.push("/admin-users");
  }, [router]);

  const handleRetry = useCallback(() => {
    if (id && !isCreateMode) {
      dispatch(fetchSingleAdminUserRequest(id));
    }
  }, [id, isCreateMode, dispatch]);

  return (
    <RoleGuard requiredRole="admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            <PageHeader
              title={isCreateMode ? "Create Admin User" : "Edit Admin User"}
              subtitle={
                isCreateMode
                  ? "Add a new administrator to the system"
                  : "Edit administrator details"
              }
              breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Admin Users", href: "/admin-users" },
                { label: isCreateMode ? "Create User" : "Edit User" },
              ]}
            />
            <Paper elevation={3} sx={{ p: 4, mt: 3 }}>
              {(loading || isFetchingUser) ? (
                <Box sx={{ display: "flex", justifyContent: "center", py: 8 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Box>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                  </Alert>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleRetry}
                  >
                    Retry
                  </Button>
                </Box>
              ) : (
                <AdminUserForm
                  adminUser={isCreateMode ? undefined : (adminUser || undefined)}
                  currentUserRole={currentUserRole}
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={updateLoading || isFetchingUser}
                  error={error}
                />
              )}
            </Paper>
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
}
