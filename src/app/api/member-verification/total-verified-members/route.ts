/**
 * Member Verification API endpoint - Simple test endpoint
 * GET /api/member-verification/total-verified-members
 */

import { NextRequest, NextResponse } from 'next/server';

// This endpoint forwards requests to the Python backend

/**
 * Simple authentication check using cookies
 */
function isAuthenticated(request: NextRequest): boolean {
  // Check for Cognito tokens (admin users)
  const cognitoAccessToken = request.cookies.get('cognito_access_token')?.value;
  const cognitoIdToken = request.cookies.get('cognito_id_token')?.value;

  // Check for legacy token
  const legacyToken = request.cookies.get('token')?.value;

  return !!(cognitoAccessToken && cognitoIdToken) || !!legacyToken;
}

/**
 * GET /api/member-verification/total-verified-members
 * Returns the total count of verified members
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Processing request for total verified members');

    // Simple authentication check
    if (!isAuthenticated(request)) {
      console.warn('🔐 Authentication failed: No valid tokens found');
      return NextResponse.json(
        {
          error: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated, proceeding with backend API call');

    // Get authentication token for backend call
    const cognitoAccessToken = request.cookies.get('cognito_access_token')?.value;
    const legacyToken = request.cookies.get('token')?.value;
    const accessToken = cognitoAccessToken || legacyToken;

    if (!accessToken) {
      return NextResponse.json(
        {
          error: 'No access token available',
          code: 'NO_TOKEN'
        },
        { status: 401 }
      );
    }

    // Make actual API call to Python backend
    const backendUrl = process.env.BACKEND_API_URL || process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';
    const apiEndpoint = `${backendUrl}/api/member-verification/total-verified-members`;

    console.log(`🔗 Making request to backend: ${apiEndpoint}`);

    try {
      const backendResponse = await fetch(apiEndpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      console.log(`📡 Backend response status: ${backendResponse.status}`);

      if (!backendResponse.ok) {
        const errorText = await backendResponse.text();
        console.error(`❌ Backend error: ${backendResponse.status} - ${errorText}`);

        return NextResponse.json(
          {
            error: 'Backend API error',
            code: 'BACKEND_ERROR',
            details: `Backend returned ${backendResponse.status}: ${errorText}`
          },
          { status: backendResponse.status }
        );
      }

      const backendData = await backendResponse.json();
      console.log(`✅ Successfully received data from backend:`, backendData);

      return NextResponse.json(backendData, {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });

    } catch (fetchError: any) {
      console.error('❌ Error calling backend API:', fetchError);

      if (fetchError.code === 'ECONNREFUSED' || fetchError.code === 'ENOTFOUND') {
        return NextResponse.json(
          {
            error: 'Backend service unavailable',
            code: 'SERVICE_UNAVAILABLE',
            details: 'Unable to connect to backend service'
          },
          { status: 503 }
        );
      }

      return NextResponse.json(
        {
          error: 'Failed to call backend API',
          code: 'FETCH_ERROR',
          details: fetchError.message
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('❌ Error processing request:', error);

    const isDevelopment = process.env.NODE_ENV === 'development';

    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: isDevelopment ? error.message : 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
