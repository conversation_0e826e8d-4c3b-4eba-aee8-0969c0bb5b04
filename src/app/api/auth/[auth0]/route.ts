import { NextRequest, NextResponse } from "next/server";
import { auth0 } from "@/lib/auth0";
import { validateAuth0Config, getAuth0Config } from "@/utils/auth";

export async function GET(request: NextRequest) {
  try {
    const auth0Action = request.nextUrl.pathname.split("/").pop();
    console.log("Auth0 API - Processing action:", auth0Action);

    // Validate Auth0 configuration
    const configValidation = validateAuth0Config();
    if (!configValidation.isValid) {
      console.error(
        "Auth0 API - Configuration validation failed:",
        configValidation.missing
      );
      return NextResponse.json(
        {
          error: "Auth0 configuration error",
          missing: configValidation.missing,
        },
        { status: 500 }
      );
    }

    // Log configuration for debugging (without sensitive data)
    const config = getAuth0Config();
    console.log("Auth0 API - Configuration:", {
      domain: config.domain,
      hasClientId: !!config.clientId,
      hasClientSecret: config.hasClientSecret,
      hasSecret: config.hasSecret,
      audience: config.audience,
      connection: config.connection,
      appBaseUrl: config.appBaseUrl,
      environment: config.environment,
    });

    if (auth0Action === "login") {
      // Validate required environment variables
      const auth0Domain = process.env.AUTH0_DOMAIN;
      const clientId = process.env.AUTH0_CLIENT_ID;
      const connection =
        process.env.AUTH0_CONNECTION || "Username-Password-Authentication";

      if (!auth0Domain || !clientId) {
        console.error("Auth0 API - Missing required environment variables:", {
          domain: !!auth0Domain,
          clientId: !!clientId,
        });
        return NextResponse.json(
          { error: "Auth0 configuration error" },
          { status: 500 }
        );
      }

      // Handle login
      const returnTo =
        request.nextUrl.searchParams.get("returnTo") ||
        "/member-user/dashboard";

      // Improved base URL detection for production
      let appBaseUrl: string;

      if (process.env.APP_BASE_URL) {
        appBaseUrl = process.env.APP_BASE_URL;
      } else if (process.env.NODE_ENV === "production") {
        // In production, use the request host with proper protocol
        const host = request.headers.get("host");
        const protocol = request.headers.get("x-forwarded-proto") || "https";
        appBaseUrl = `${protocol}://${host}`;
      } else {
        appBaseUrl = "http://localhost:3000";
      }

      // Ensure the callback route matches Auth0 configuration
      const redirectUri = `${appBaseUrl}/api/auth/callback`;

      console.log("Auth0 API - Environment:", process.env.NODE_ENV);
      console.log("Auth0 API - App Base URL:", appBaseUrl);
      console.log("Auth0 API - Redirect URI:", redirectUri);
      console.log("Auth0 API - Domain:", auth0Domain);
      console.log("Auth0 API - Client ID:", clientId);

      // Construct login URL with all required parameters
      const loginUrl =
        `https://${auth0Domain}/authorize?` +
        `response_type=code&` +
        `client_id=${clientId}&` +
        `connection=${encodeURIComponent(connection)}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=openid profile email&` +
        `state=${encodeURIComponent(returnTo)}`;

      console.log("Auth0 API - Login URL:", loginUrl);
      return NextResponse.redirect(loginUrl);
    } else if (auth0Action === "logout") {
      // Handle logout
      const auth0Domain = process.env.AUTH0_DOMAIN;
      const clientId = process.env.AUTH0_CLIENT_ID;
      const returnTo = request.nextUrl.searchParams.get("returnTo") || "/login";

      if (!auth0Domain || !clientId) {
        console.error(
          "Auth0 API - Missing required environment variables for logout"
        );
        return NextResponse.json(
          { error: "Auth0 configuration error" },
          { status: 500 }
        );
      }

      const logoutUrl =
        `https://${auth0Domain}/v2/logout?` +
        `client_id=${clientId}&` +
        `returnTo=${encodeURIComponent(returnTo)}`;

      console.log("Auth0 API - Logout URL:", logoutUrl);
      return NextResponse.redirect(logoutUrl);
    } else {
      return NextResponse.json(
        { error: "Invalid Auth0 action" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Auth0 API - Error:", error);
    return NextResponse.json({ error: "Auth0 error" }, { status: 500 });
  }
}
