import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Get all Auth0 related environment variables
    const auth0EnvVars = {
      AUTH0_DOMAIN: process.env.AUTH0_DOMAIN,
      AUTH0_CLIENT_ID: process.env.AUTH0_CLIENT_ID,
      AUTH0_CLIENT_SECRET: process.env.AUTH0_CLIENT_SECRET
        ? "***SET***"
        : "***NOT SET***",
      AUTH0_SECRET: process.env.AUTH0_SECRET ? "***SET***" : "***NOT SET***",
      AUTH0_AUDIENCE: process.env.AUTH0_AUDIENCE,
      AUTH0_CONNECTION:
        process.env.AUTH0_CONNECTION || "Username-Password-Authentication",
      APP_BASE_URL: process.env.APP_BASE_URL,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NODE_ENV: process.env.NODE_ENV,
    };

    // Check which required variables are missing
    const requiredVars = [
      "AUTH0_DOMAIN",
      "AUTH0_CLIENT_ID",
      "AUTH0_CLIENT_SECRET",
      "AUTH0_SECRET",
    ];

    const missingVars = requiredVars.filter((varName) => !process.env[varName]);

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      auth0EnvVars,
      missingRequiredVars: missingVars,
      allSet: missingVars.length === 0,
      requestHeaders: {
        host: request.headers.get("host"),
        "x-forwarded-proto": request.headers.get("x-forwarded-proto"),
        "x-forwarded-host": request.headers.get("x-forwarded-host"),
      },
    });
  } catch (error) {
    console.error("Auth0 ENV route error:", error);
    return NextResponse.json(
      {
        error: "Failed to get environment variables",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
