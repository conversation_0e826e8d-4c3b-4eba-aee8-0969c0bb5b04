import { NextRequest, NextResponse } from "next/server";
import { validateAuth0Config, getAuth0Config } from "@/utils/auth";

export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with a secret key
    const debugKey = request.nextUrl.searchParams.get("key");
    const isDevelopment = process.env.NODE_ENV === "development";

    if (!isDevelopment && debugKey !== process.env.DEBUG_SECRET_KEY) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const configValidation = validateAuth0Config();
    const config = getAuth0Config();

    return NextResponse.json({
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      configValidation,
      config: {
        ...config,
        // Don't expose sensitive values
        clientId: config.clientId ? "***" : null,
        domain: config.domain,
      },
      headers: {
        host: request.headers.get("host"),
        "x-forwarded-proto": request.headers.get("x-forwarded-proto"),
        "x-forwarded-host": request.headers.get("x-forwarded-host"),
      },
    });
  } catch (error) {
    console.error("Debug endpoint error:", error);
    return NextResponse.json({ error: "Debug error" }, { status: 500 });
  }
}
