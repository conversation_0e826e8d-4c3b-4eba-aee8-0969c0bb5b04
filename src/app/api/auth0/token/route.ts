import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        {
          error: "No authorization header found",
          details:
            "Please include a valid Bearer token in the Authorization header",
        },
        { status: 401 }
      );
    }

    // Extract the token from the Authorization header
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      return NextResponse.json(
        {
          error: "Invalid token format",
        },
        { status: 401 }
      );
    }

    // Return the token information
    return NextResponse.json({
      access_token: token,
      token_type: "Bearer",
      message: "Token validated successfully",
    });
  } catch (error: any) {
    console.error("Error processing token:", error);
    return NextResponse.json(
      {
        error: "Failed to process token",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
