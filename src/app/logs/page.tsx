// Main Logs Page
"use client";

import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Container,
  Typography,
  Box,
  Alert,
  Snackbar,
  Button,
  Paper,
  Breadcrumbs,
  Link,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Assessment as LogIcon,
} from "@mui/icons-material";
import { logsActions } from "@/store/logs/redux";
import { logsSelectors } from "@/store/logs/selector";
import { PageHeader } from "@/components/common";
import LogFilters from "@/components/logs/LogFilters";
import LogTable from "@/components/logs/LogTable";
import LogStats from "@/components/logs/LogStats";
import ExportModal from "@/components/logs/ExportModal";
import { ExportRequest } from "@/types/log";
import { useCurrentUserRole } from "@/store/adminUser/selector";
import AppLayout from "@/layout/AppLayout";

const LogsPage: React.FC = () => {
  const dispatch = useDispatch();
  const currentUser = useCurrentUserRole();
  const isMember = currentUser === "member";
  const isAdmin = currentUser === "admin";

  // Redux selectors
  const logs = useSelector(logsSelectors.selectLogs);
  const loading = useSelector(logsSelectors.selectLoading);
  const exportLoading = useSelector(logsSelectors.selectExportLoading);
  const error = useSelector(logsSelectors.selectError);
  const exportError = useSelector(logsSelectors.selectExportError);
  const pagination = useSelector(logsSelectors.selectPagination);
  const filters = useSelector(logsSelectors.selectFilters);
  const availableActions = useSelector(logsSelectors.selectAvailableActions);
  const exportFields = useSelector(logsSelectors.selectExportFields);
  const isLoading = useSelector(logsSelectors.selectIsLoading);

  // Tab state selectors
  const activeTab = useSelector(logsSelectors.selectActiveTab);
  const auditFilters = useSelector(logsSelectors.selectAuditFilters);
  const memberDeleteFilters = useSelector(
    logsSelectors.selectMemberDeleteFilters
  );

  // Local state
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Tab change handler
  const handleTabChange = (
    event: React.SyntheticEvent,
    newValue: "audit" | "member_delete"
  ) => {
    console.log("Tab changed from", activeTab, "to", newValue);
    dispatch(logsActions.setActiveTab(newValue));
  };

  // Load initial data
  useEffect(() => {
    // Get current filters based on active tab
    const currentFilters =
      activeTab === "audit" ? auditFilters : memberDeleteFilters;

    // Apply tab-specific filters
    let tabFilters = { ...currentFilters };

    if (activeTab === "audit") {
      // For audit tab: exclude member_delete actions (show member_export and log_export)
      tabFilters = { ...tabFilters, action_exclude: ["member_delete"] };
      console.log("Audit tab filters:", tabFilters);
    } else if (activeTab === "member_delete") {
      // For member delete tab: show only member_delete actions
      tabFilters = { ...tabFilters, action: "member_delete" };
      console.log("Member delete tab filters:", tabFilters);
    }

    // Fetch logs based on user role
    if (isMember) {
      dispatch(logsActions.fetchMyLogs(tabFilters));
    } else {
      dispatch(logsActions.fetchLogs(tabFilters));
    }

    // Fetch metadata for admin users only
    if (isAdmin) {
      dispatch(logsActions.fetchActions());
      dispatch(logsActions.fetchExportFields());
    }
  }, [
    dispatch,
    activeTab,
    auditFilters,
    memberDeleteFilters,
    isMember,
    isAdmin,
  ]);

  // Refresh data
  const handleRefresh = () => {
    // Get current filters based on active tab
    const currentFilters =
      activeTab === "audit" ? auditFilters : memberDeleteFilters;

    // Apply tab-specific filters
    let tabFilters = { ...currentFilters };

    if (activeTab === "audit") {
      // For audit tab: exclude member_delete actions (show member_export and log_export)
      tabFilters = { ...tabFilters, action_exclude: ["member_delete"] };
    } else if (activeTab === "member_delete") {
      // For member delete tab: show only member_delete actions
      tabFilters = { ...tabFilters, action: "member_delete" };
    }

    if (isMember) {
      dispatch(logsActions.fetchMyLogs(tabFilters));
    } else {
      dispatch(logsActions.fetchLogs(tabFilters));
    }
    setSuccessMessage("Logs refreshed successfully!");
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: typeof filters) => {
    if (activeTab === "audit") {
      dispatch(logsActions.setAuditFilters(newFilters));
    } else {
      dispatch(logsActions.setMemberDeleteFilters(newFilters));
    }
  };

  // Handle pagination changes
  const handlePaginationChange = (page: number, pageSize: number) => {
    if (activeTab === "audit") {
      dispatch(logsActions.updateAuditFilters({ page, page_size: pageSize }));
    } else {
      dispatch(
        logsActions.updateMemberDeleteFilters({ page, page_size: pageSize })
      );
    }
  };

  // Handle export
  const handleExport = (exportRequest: ExportRequest) => {
    // Get current filters based on active tab
    const currentFilters =
      activeTab === "audit" ? auditFilters : memberDeleteFilters;

    // Apply tab-specific filters to export request
    let tabFilters = { ...currentFilters };

    if (activeTab === "audit") {
      // For audit tab: exclude member_delete actions
      tabFilters = { ...tabFilters, action_exclude: ["member_delete"] };
    } else if (activeTab === "member_delete") {
      // For member delete tab: show only member_delete actions
      tabFilters = { ...tabFilters, action: "member_delete" };
    }

    // Create export request with tab-specific filters
    const tabExportRequest = {
      ...exportRequest,
      filters: {
        ...exportRequest.filters,
        ...tabFilters,
      },
    };

    dispatch(logsActions.exportLogs(tabExportRequest));
    setExportModalOpen(false);
    setSuccessMessage("Export started! Your download will begin shortly.");
  };

  // Clear errors
  const handleClearError = () => {
    dispatch(logsActions.clearError());
    dispatch(logsActions.clearExportError());
  };

  // Close success message
  const handleCloseSuccess = () => {
    setSuccessMessage(null);
  };

  const pageTitle = isMember
    ? "My Activity Logs"
    : activeTab === "audit"
    ? "Audit Logs"
    : "Member Delete Logs";
  const pageDescription = isMember
    ? "View your activity history and exports"
    : activeTab === "audit"
    ? "Monitor system activity, user actions, and export logs"
    : "Track member deletion events and related activities";

  return (
    <AppLayout>
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link
            color="inherit"
            href={isMember ? "/member-user/dashboard" : "/dashboard"}
            sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
          >
            <HomeIcon fontSize="small" />
            Dashboard
          </Link>
          <Typography
            color="text.primary"
            sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
          >
            <LogIcon fontSize="small" />
            {pageTitle}
          </Typography>
        </Breadcrumbs>

        {/* Tab Navigation (Admin only) */}
        

        {/* Page Header */}
        <PageHeader
          title={pageTitle}
          subtitle={pageDescription}
          actions={
            <Box sx={{ display: "flex", gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={isLoading}
              >
                Refresh
              </Button>
              {isAdmin && (
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  onClick={() => setExportModalOpen(true)}
                  disabled={isLoading || logs.length === 0}
                >
                  Export CSV
                </Button>
              )}
            </Box>
          }
        />

        {/* Error Display */}
        {/* {(error || exportError) && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          onClose={handleClearError}
          action={
            <Button color="inherit" size="small" onClick={handleClearError}>
              Dismiss
            </Button>
          }
        >
          {error || exportError}
        </Alert>
      )} */}

        {/* Statistics (show for both admin and member) */}
        <LogStats
          logs={logs}
          totalCount={pagination.total_count}
          loading={loading}
        />

        {/* Filters */}
        <LogFilters
          filters={activeTab === "audit" ? auditFilters : memberDeleteFilters}
          onFiltersChange={handleFiltersChange}
          onRefresh={handleRefresh}
          availableActions={availableActions}
          loading={isLoading}
          activeTab={activeTab}
        />

{!isMember && (
          <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="logs tabs"
            >
              <Tab
                label="Member Export & Log Export"
                value="audit"
                sx={{ textTransform: "none" }}
              />
              <Tab
                label="Member Delete"
                value="member_delete"
                sx={{ textTransform: "none" }}
              />
            </Tabs>
          </Box>
        )}

        {/* Main Content */}
        <Paper sx={{ p: 0 }}>
          {logs.length === 0 && !loading ? (
            <Box sx={{ p: 4, textAlign: "center" }}>
              <LogIcon sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No logs found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isMember
                  ? "You don't have any activity logs yet."
                  : "No logs match your current filters. Try adjusting your search criteria."}
              </Typography>
            </Box>
          ) : (
            <LogTable
              logs={logs}
              loading={loading}
              pagination={pagination}
              onPaginationChange={handlePaginationChange}
              activeTab={activeTab}
            />
          )}
        </Paper>

        {/* Export Modal (Admin only) */}
        {isAdmin && (
          <ExportModal
            open={exportModalOpen}
            onClose={() => setExportModalOpen(false)}
            onExport={handleExport}
            exportFields={exportFields}
            currentFilters={
              activeTab === "audit" ? auditFilters : memberDeleteFilters
            }
            loading={exportLoading}
            error={exportError}
            activeTab={activeTab}
          />
        )}

        {/* Success Message */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={4000}
          onClose={handleCloseSuccess}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert severity="success" onClose={handleCloseSuccess}>
            {successMessage}
          </Alert>
        </Snackbar>

        {/* Loading Overlay */}
        {/* {isLoading && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255, 255, 255, 0.7)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}
        >
          <Paper sx={{ p: 3, textAlign: "center" }}>
            <Typography variant="body1">
              {exportLoading ? "Preparing your export..." : "Loading logs..."}
            </Typography>
          </Paper>
        </Box>
      )} */}
      </Container>
    </AppLayout>
  );
};

export default LogsPage;
