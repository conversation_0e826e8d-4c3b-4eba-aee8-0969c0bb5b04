"use client";

import {
  People as PeopleIcon,
  Person as PersonIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  VerifiedUser as VerifiedUserIcon,
} from "@mui/icons-material";
import {
  Alert,
  Box,
  CircularProgress,
  Container,
  Grid,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";

import { StatCard } from "@/components/dashboard/StatCard";
import RecentLogs from "@/components/dashboard/RecentLogs";
import AppLayout from "@/layout/AppLayout";
import {
  ActivityItem,
  dashboardService,
  DashboardStats,
  QuickAction,
} from "@/services/dashboard";
import { LogEntry } from "@/types/log";
import { useAppDispatch } from "@/store/hooks";
import { clearTempRegistrationCredentials } from "@/store/auth/redux";

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        dispatch(clearTempRegistrationCredentials());

        // Fetch all dashboard data in parallel
        const [statsData, activitiesData, actionsData, logsData] =
          await Promise.all([
            dashboardService.getDashboardStats(),
            dashboardService.getRecentActivities(10),
            dashboardService.getQuickActions(),
            dashboardService.getRecentLogs(10),
          ]);

        setStats(statsData);
        setActivities(activitiesData);
        setQuickActions(actionsData);
        setRecentLogs(logsData);
      } catch (err: any) {
        console.error("Dashboard data fetch error:", err);
        setError(err.message || "Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "60vh",
            }}
          >
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          <Typography
            variant="h3"
            component="h1"
            gutterBottom
            sx={{
              mb: 4,
              color: "#1e3a8a",
              fontWeight: 700,
              fontSize: { xs: "1.75rem", sm: "2rem", md: "2.5rem" },
              textShadow: "0 1px 2px rgba(0,0,0,0.1)",
            }}
          >
            Dashboard
          </Typography>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={4} lg={2.4}>
              <StatCard
                title="Total Members"
                value={stats?.totalMembers || 0}
                subtitle="All registered members"
                icon={<PeopleIcon />}
                color="primary"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4} lg={2.4}>
              <StatCard
                title="Verified Members"
                value={stats?.verifiedMembers || 0}
                subtitle="Members with verified status"
                icon={<VerifiedUserIcon />}
                color="success"
              />
            </Grid>



            {/* <Grid item xs={12} sm={6} md={4} lg={2.4}>
              <StatCard
                title="Recent Registrations"
                value={stats?.recentRegistrations || 0}
                subtitle="New members this week"
                icon={<TrendingUpIcon />}
                color="secondary"
              />
            </Grid> */}

          
          </Grid>

          {/* Recent Logs */}
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <RecentLogs logs={recentLogs} loading={loading} />
            </Grid>
          </Grid>

          {/* <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <ActivityFeed
                activities={activities}
                title="Recent Activity"
                maxItems={8}
              />
            </Grid>

            <Grid item xs={12} lg={4}>
              <QuickActions
                actions={quickActions}
                title="Quick Actions"
              />
            </Grid>
          </Grid> */}
        </Box>
      </Container>
    </AppLayout>
  );
}
