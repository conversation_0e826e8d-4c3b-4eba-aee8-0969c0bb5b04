// src/config/cognito.ts
// Minimal AWS Amplify Cognito config using environment variables and latest supported fields
import { Amplify } from "aws-amplify";

// Get Cognito config from environment variables
const region = process.env.NEXT_PUBLIC_AWS_REGION as string;
const userPoolId = process.env.NEXT_PUBLIC_AWS_USER_POOL_ID as string;
const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID as string;

if (!region || !userPoolId || !userPoolClientId) {
  throw new Error("Missing required AWS Cognito environment variables");
}

// Only include supported fields per Amplify docs/types
const amplifyConfig = {
  Auth: {
    Cognito: {
      region,
      userPoolId,
      userPoolClientId,
      // Optional: allow email login
      loginWith: {
        email: true,
      },
      // Optional: sign-up verification method (must be 'code' or 'link')
      signUpVerificationMethod: "code" as const,
    },
  },
};

// Configure Amplify with Cognito
Amplify.configure(amplifyConfig);

export default amplifyConfig;
