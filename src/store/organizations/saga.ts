import { all, call, put, takeLatest, select } from "redux-saga/effects";
import { organizationsActions } from "./redux";
import { organizationApiService } from "../../services/organizationApiService";
import { showToast } from "@/utils/toast";
import { navigate } from "@/utils/routerService";

// Saga functions
function* fetchOrganizationsSaga(
  action: ReturnType<typeof organizationsActions.fetchOrganizations>
): Generator<any, any, any> {
  try {
    const filterParams = action.payload;
    const response = yield call(
      organizationApiService.getOrganizations,
      filterParams
    );
    yield put(
      organizationsActions.fetchOrganizationsSuccess({
        organizations: response.organizations,
        pagination: {
          page: response.pagination.page || response.pagination.currentPage,
          pageSize: response.pagination.pageSize,
          total: response.pagination.total || response.pagination.totalCount,
        },
      })
    );
  } catch (error: any) {
    yield put(
      organizationsActions.fetchOrganizationsFailure(
        error.message || "Failed to fetch organizations"
      )
    );
  }
}

function* fetchOrganizationSaga(
  action: ReturnType<typeof organizationsActions.fetchOrganization>
): Generator<any, any, any> {
  try {
    const uuid = action.payload;
    const response = yield call(organizationApiService.getOrganization, uuid);
    // Extract organization from the API response structure
    yield put(
      organizationsActions.fetchOrganizationSuccess(response.organization)
    );
  } catch (error: any) {
    yield put(
      organizationsActions.fetchOrganizationFailure(
        error.message || "Failed to fetch organization"
      )
    );
  }
}

function* createOrganizationSaga(
  action: ReturnType<typeof organizationsActions.createOrganization>
): Generator<any, any, any> {
  try {
    const data = action.payload;
    const response = yield call(
      organizationApiService.createOrganization,
      data
    );
    if (response.status_code == 201) {
      yield call(showToast.success, "Organization created successfully");
      yield call(navigate, "/organizations");
    } else {
      yield call(showToast.error, "Failed to create organization");
    }
     yield put(
      organizationsActions.createOrganizationSuccess(response.organization)
    );
   
    // Refresh the organizations list after creating with current filters
    const state = yield select((state: any) => state.organizations);
    const currentFilters = { ...state.filters, page: 1 }; // Reset to first page
    yield put(organizationsActions.fetchOrganizations(currentFilters));
  } catch (error: any) {
    yield put(
      organizationsActions.createOrganizationFailure(
        error.message || "Failed to create organization"
      )
    );
  }
}

function* updateOrganizationSaga(
  action: ReturnType<typeof organizationsActions.updateOrganization>
): Generator<any, any, any> {
  try {
    const { uuid, data } = action.payload;
    const response = yield call(
      organizationApiService.updateOrganization,
      uuid,
      data
    );
    yield put(
      organizationsActions.updateOrganizationSuccess(response.organization)
    );
    // Refresh the organizations list after updating with current filters
    const state = yield select((state: any) => state.organizations);
    const currentFilters = { ...state.filters, page: 1 }; // Reset to first page
    yield put(organizationsActions.fetchOrganizations(currentFilters));
  } catch (error: any) {
    yield put(
      organizationsActions.updateOrganizationFailure(
        error.message || "Failed to update organization"
      )
    );
  }
}

function* deleteOrganizationSaga(
  action: ReturnType<typeof organizationsActions.deleteOrganization>
): Generator<any, any, any> {
  try {
    const uuid = action.payload;
    yield call(organizationApiService.deleteOrganization, uuid);
    yield put(organizationsActions.deleteOrganizationSuccess(uuid));
    // Refresh the organizations list after deleting with current filters
    const state = yield select((state: any) => state.organizations);
    const currentFilters = { ...state.filters, page: 1 }; // Reset to first page
    yield put(organizationsActions.fetchOrganizations(currentFilters));
  } catch (error: any) {
    yield put(
      organizationsActions.deleteOrganizationFailure(
        error.message || "Failed to delete organization"
      )
    );
  }
}

// Root saga
export function* organizationsSaga() {
  yield all([
    takeLatest(
      organizationsActions.fetchOrganizations.type,
      fetchOrganizationsSaga
    ),
    takeLatest(
      organizationsActions.fetchOrganization.type,
      fetchOrganizationSaga
    ),
    takeLatest(
      organizationsActions.createOrganization.type,
      createOrganizationSaga
    ),
    takeLatest(
      organizationsActions.updateOrganization.type,
      updateOrganizationSaga
    ),
    takeLatest(
      organizationsActions.deleteOrganization.type,
      deleteOrganizationSaga
    ),
  ]);
}
