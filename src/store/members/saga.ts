import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest, select } from "redux-saga/effects";
import { membersService } from "@/services/members";
import {
  assignOrganizationToMember,
  organizationService,
  removeOrganizationFromMember,
} from "@/services/organizationService";
import { showToast } from "@/utils/toast";
import {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
  fetchMemberRequest,
  fetchMemberSuccess,
  fetchMemberFailure,
  createMemberRequest,
  createMemberSuccess,
  createMemberFailure,
  updateMemberRequest,
  updateMemberSuccess,
  updateMemberFailure,
  deleteMemberRequest,
  deleteMemberSuccess,
  deleteMemberFailure,
  exportMembersRequest,
  exportMembersSuccess,
  exportMembersFailure,
  selectMembersArray,
  selectMembersLastFetched,
  selectMembersPagination,
} from "./redux";
import { CreateMemberData, UpdateMemberData } from "@/lib/validations/member";
import { convertSnakeToCamelCase } from "@/utils/memberUtils";
import { MemberSearchFilters } from "@/types/member";
import { navigate } from "@/utils/routerService";

function* fetchMembersSaga(
  action?: PayloadAction<{
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    filters?: MemberSearchFilters;
  }>
): Generator<any, void, any> {
  try {
    const currentPagination = yield select(selectMembersPagination);

    const defaultFilters: MemberSearchFilters = {
      search: "",
      firstName: "",
      lastName: "",
      email: "",
      membershipTier: "",
      communityStatus: "",
      verificationStatus: "",
      organizationName: "",
      organizationCity: "",
      organizationState: "",
      organizationZip: "",
      companySize: "",
      industry: "",
      dateCreatedFrom: "",
      dateCreatedTo: "",
    };

    const params = {
      page: action?.payload?.page || currentPagination.currentPage,
      pageSize: action?.payload?.pageSize || currentPagination.pageSize,
      sortBy: action?.payload?.sortBy || "dateCreated",
      sortOrder: action?.payload?.sortOrder || "asc",
      filters: action?.payload?.filters || defaultFilters,
    };

    const response = yield call(membersService.getMembers, params);
    yield put(
      fetchMembersSuccess({
        members: response.members,
        pagination: response.pagination,
      })
    );
  } catch (error: any) {
    yield put(fetchMembersFailure(error.message || "Failed to fetch members"));
  }
}

function* fetchMemberSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const member = yield call(membersService.getMember, action.payload);
    yield put(fetchMemberSuccess(member));
  } catch (error: any) {
    yield put(fetchMemberFailure(error.message || "Failed to fetch member"));
  }
}

// NEW: Export members saga
function* exportMembersSaga(
  action: PayloadAction<{
    filters: any;
    selectedFields: string[];
    notes: string;
  }>
): Generator<any, void, any> {
  try {
    // Safely destructure payload with defaults
    const payload = action.payload;
    if (!payload) {
      yield call(showToast.error, "Export data is missing");
      yield put(exportMembersFailure("Export data is missing"));
      return;
    }

    const { filters, selectedFields, notes } = payload;

    // Validate required fields
    if (!selectedFields || selectedFields.length === 0) {
      yield call(showToast.error, "Please select at least one field to export");
      yield put(exportMembersFailure("No fields selected for export"));
      return;
    }

    if (!notes || notes.trim() === "") {
      yield call(showToast.error, "Please provide a reason for this export");
      yield put(exportMembersFailure("Export notes are required"));
      return;
    }

    const exportRequest = {
      filters: filters || {},
      selectedFields,
      notes: notes.trim(),
    };

    const response = yield call(membersService.exportMembers, exportRequest);

    // Trigger download
    if (response.downloadUrl) {
      const link = document.createElement("a");
      link.href = response.downloadUrl;
      link.download = response.filename || "member_export.csv";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    yield put(exportMembersSuccess("Export completed successfully"));
    yield call(showToast.success, "Export completed successfully");
  } catch (error: any) {
    yield put(exportMembersFailure(error.message || "Export failed"));
    yield call(showToast.error, error.message || "Export failed");
  }
}

function* createMemberSaga(
  action: PayloadAction<CreateMemberData>
): Generator<any, void, any> {
  try {
    const response = yield call(() =>
      membersService.createMember(action.payload)
    );
    //show tost when response is success
    const org1: any = action.payload;
    yield call(showToast.success, "Member created successfully");
    yield call(navigate, "/members");
    yield put(createMemberSuccess(response));

    // Create organization relations if organizations exist
    if (org1.selectedOrganizations && org1.selectedOrganizations.length > 0) {
      for (const org of org1.selectedOrganizations) {
        yield call(organizationService.createMemberRelation, {
          memberUuid: response.user.uuid,
          organizationUuid: org.uuid || "",
        });
      }
    }
  } catch (error: any) {
    console.log(error, "error");
    yield call(showToast.error, error.message || "Failed to create member");
    yield put(createMemberFailure(error.message || "Failed to create member"));
  }
}

function* updateMemberSaga(
  action: PayloadAction<{ id: string; data: UpdateMemberData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const updatedMember = yield call(
      membersService.updateMember,
      id,
      data as any
    );

    // Handle organization relations update if selectedOrganizations is provided
    console.log("🔍 DEBUG Update: data received:", data);
    console.log(
      "🔍 DEBUG Update: selectedOrganizations:",
      (data as any).selectedOrganizations
    );

    if ((data as any).selectedOrganizations !== undefined) {
      try {
        console.log(
          "🔍 DEBUG Update: Starting organization relations update for member:",
          id
        );

        // Get current member organizations (using the working function)
        const currentOrganizations = yield call(
          organizationService.getOrganizationsByMember,
          id
        );
        console.log(
          "🔍 DEBUG Update: Current organizations:",
          currentOrganizations
        );

        // Get current organization UUIDs from organizations
        const currentOrgUuids = currentOrganizations.map(
          (org: any) => org.uuid
        );
        console.log("🔍 DEBUG Update: Current org UUIDs:", currentOrgUuids);

        // Get new organization UUIDs
        const newOrgUuids = ((data as any).selectedOrganizations || [])
          .map((org: any) => org.uuid || "")
          .filter(Boolean);
        console.log("🔍 DEBUG Update: New org UUIDs:", newOrgUuids);

        // // Delete relations that are no longer needed
        const orgsToRemove = currentOrgUuids.filter(
          (orgUuid: any) => !newOrgUuids.includes(orgUuid)
        );

        for (const orgUuid of orgsToRemove) {
          console.log("🔍 DEBUG Update: Deleting relation:", {
            memberUuid: id,
            organizationUuid: orgUuid,
          });
          yield call(removeOrganizationFromMember, id, orgUuid);
        }

        // Create new relations
        for (const orgUuid of newOrgUuids) {
          if (!currentOrgUuids.includes(orgUuid)) {
            console.log("🔍 DEBUG Update: Creating new relation:", {
              memberUuid: id,
              organizationUuid: orgUuid,
            });
            yield call(organizationService.createMemberRelation, {
              memberUuid: id,
              organizationUuid: orgUuid,
            });
          }
        }

        console.log("🔍 DEBUG Update: Organization relations update completed");

        yield call(navigate, "/members");
      } catch (orgError: any) {
        console.error(
          "🔍 DEBUG Update: Organization relations error:",
          orgError
        );
        // Don't throw - let member update succeed even if org relations fail
        yield call(
          showToast.error,
          `Organization relations update failed: ${orgError.message}`
        );
      }
    }

    yield call(showToast.success, "Member updated successfully");
    yield put(updateMemberSuccess(updatedMember));
  } catch (error: any) {
    yield call(showToast.error, error.message || "Failed to update member");
    yield put(updateMemberFailure(error.message || "Failed to update member"));
  }
}

function* deleteMemberSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const memberId = action.payload;
    yield call(membersService.deleteMember, memberId);
    yield put(deleteMemberSuccess(memberId));
  } catch (error: any) {
    yield put(deleteMemberFailure(error.message || "Failed to delete member"));
  }
}

export default function* membersSaga() {
  yield all([
    takeLatest(fetchMembersRequest.type, fetchMembersSaga),
    takeLatest(fetchMemberRequest.type, fetchMemberSaga),
    takeLatest(createMemberRequest.type, createMemberSaga),
    takeLatest(updateMemberRequest.type, updateMemberSaga),
    takeLatest(deleteMemberRequest.type, deleteMemberSaga),
    takeLatest(exportMembersRequest.type, exportMembersSaga),
  ]);
}
