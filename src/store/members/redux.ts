import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  Member,
  MemberWithRelations,
  CreateMemberData,
  UpdateMemberData,
} from "@/types/member";

interface PaginationState {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

interface MembersState {
  members: Member[];
  membersById: Record<string, Member>;
  currentMember: MemberWithRelations | null;
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
  // Pagination state
  pagination: PaginationState;
  // Single member operations
  singleMemberLoading: boolean;
  singleMemberError: string | null;
  createMemberLoading: boolean;
  createMemberError: string | null;
  updateMemberLoading: boolean;
  updateMemberError: string | null;
  deleteMemberLoading: boolean;
  deleteMemberError: string | null;
}

const initialState: MembersState = {
  members: [],
  membersById: {},
  currentMember: null,
  loading: false,
  error: null,
  lastFetched: null,
  pagination: {
    totalCount: 0,
    currentPage: 1,
    pageSize: 25,
    totalPages: 0,
    hasNext: false,
    hasPrevious: false,
  },
  singleMemberLoading: false,
  singleMemberError: null,
  createMemberLoading: false,
  createMemberError: null,
  updateMemberLoading: false,
  updateMemberError: null,
  deleteMemberLoading: false,
  deleteMemberError: null,
};

const membersSlice = createSlice({
  name: "members",
  initialState,
  reducers: {
    // List operations
    fetchMembersRequest(
      state,
      action: PayloadAction<
        | {
            page?: number;
            pageSize?: number;
            sortBy?: string;
            sortOrder?: "asc" | "desc";
            filters?: any;
          }
        | undefined
      >
    ) {
      state.loading = true;
      state.error = null;
    },
    fetchMembersSuccess(
      state,
      action: PayloadAction<{
        members: Member[];
        pagination: PaginationState;
      }>
    ) {
      state.loading = false;
      state.members = action.payload.members;
      state.pagination = action.payload.pagination;
      state.membersById = {};
      action.payload.members.forEach((m) => {
        state.membersById[m.uuid] = m;
      });
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },

    // Single member operations
    fetchMemberRequest(state, action: PayloadAction<string>) {
      state.singleMemberLoading = true;
      state.singleMemberError = null;
    },
    fetchMemberSuccess(state, action: PayloadAction<MemberWithRelations>) {
      state.singleMemberLoading = false;
      state.currentMember = action.payload;
      state.singleMemberError = null;
    },
    fetchMemberFailure(state, action: PayloadAction<string>) {
      state.singleMemberLoading = false;
      state.singleMemberError = action.payload;
    },

    // Create member operations
    createMemberRequest(state, action: PayloadAction<CreateMemberData>) {
      state.createMemberLoading = true;
      state.createMemberError = null;
    },
    createMemberSuccess(state, action: PayloadAction<Member>) {
      state.createMemberLoading = false;
      state.members.push(action.payload);
      state.membersById[action.payload.uuid] = action.payload;
      state.createMemberError = null;
    },
    createMemberFailure(state, action: PayloadAction<string>) {
      state.createMemberLoading = false;
      state.createMemberError = action.payload;
    },

    // Update member operations
    updateMemberRequest(
      state,
      action: PayloadAction<{ id: string; data: UpdateMemberData }>
    ) {
      state.updateMemberLoading = true;
      state.updateMemberError = null;
    },
    updateMemberSuccess(state, action: PayloadAction<Member>) {
      state.updateMemberLoading = false;
      const index = state.members.findIndex(
        (m) => m.uuid === action.payload.uuid
      );
      if (index !== -1) {
        state.members[index] = action.payload;
      }
      state.membersById[action.payload.uuid] = action.payload;
      if (
        state.currentMember &&
        state.currentMember.uuid === action.payload.uuid
      ) {
        state.currentMember = { ...state.currentMember, ...action.payload };
      }
      state.updateMemberError = null;
    },
    updateMemberFailure(state, action: PayloadAction<string>) {
      state.updateMemberLoading = false;
      state.updateMemberError = action.payload;
    },

    // Delete member operations
    deleteMemberRequest(state) {
      state.deleteMemberLoading = true;
      state.deleteMemberError = null;
    },
    deleteMemberSuccess(state, action: PayloadAction<string>) {
      state.deleteMemberLoading = false;
      state.members = state.members.filter(
        (m) => String(m.uuid) !== action.payload
      );
      delete state.membersById[action.payload];
      if (
        state.currentMember &&
        String(state.currentMember.uuid) === action.payload
      ) {
        state.currentMember = null;
      }
      state.deleteMemberError = null;
    },
    deleteMemberFailure(state, action: PayloadAction<string>) {
      state.deleteMemberLoading = false;
      state.deleteMemberError = action.payload;
    },

    // Export operations
    exportMembersRequest(
      state,
      action: PayloadAction<{
        filters: any;
        selectedFields: string[];
        notes: string;
      }>
    ) {
      state.loading = true;
      state.error = null;
    },
    exportMembersSuccess(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = null;
    },
    exportMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },

    // Pagination actions
    setCurrentPage(state, action: PayloadAction<number>) {
      state.pagination.currentPage = action.payload;
    },
    setPageSize(state, action: PayloadAction<number>) {
      state.pagination.pageSize = action.payload;
      state.pagination.currentPage = 1; // Reset to first page when changing page size
    },

    // Clear operations
    clearSingleMemberError(state) {
      state.singleMemberError = null;
    },
    clearCreateMemberError(state) {
      state.createMemberError = null;
    },
    clearUpdateMemberError(state) {
      state.updateMemberError = null;
    },
  },
});

export const {
  // List operations
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,

  // Single member operations
  fetchMemberRequest,
  fetchMemberSuccess,
  fetchMemberFailure,

  // Create member operations
  createMemberRequest,
  createMemberSuccess,
  createMemberFailure,

  // Update member operations
  updateMemberRequest,
  updateMemberSuccess,
  updateMemberFailure,

  // Delete member operations
  deleteMemberRequest,
  deleteMemberSuccess,
  deleteMemberFailure,

  // Export operations
  exportMembersRequest,
  exportMembersSuccess,
  exportMembersFailure,

  // Pagination actions
  setCurrentPage,
  setPageSize,

  // Clear operations
  clearSingleMemberError,
  clearCreateMemberError,
  clearUpdateMemberError,
} = membersSlice.actions;

export default membersSlice.reducer;

// Selectors
export const selectMembersArray = (state: any) => state.members.members;
export const selectMembersMap = (state: any) => state.members.membersById;
export const selectMembersLoading = (state: any) => state.members.loading;
export const selectMembersError = (state: any) => state.members.error;
export const selectMembersLastFetched = (state: any) =>
  state.members.lastFetched;

// Single member selectors
export const selectCurrentMember = (state: any) => state.members.currentMember;
export const selectSingleMemberLoading = (state: any) =>
  state.members.singleMemberLoading;
export const selectSingleMemberError = (state: any) =>
  state.members.singleMemberError;

// Create member selectors
export const selectCreateMemberLoading = (state: any) =>
  state.members.createMemberLoading;
export const selectCreateMemberError = (state: any) =>
  state.members.createMemberError;

// Update member selectors
export const selectUpdateMemberLoading = (state: any) =>
  state.members.updateMemberLoading;
export const selectUpdateMemberError = (state: any) =>
  state.members.updateMemberError;

// Delete member selectors
export const selectDeleteMemberLoading = (state: any) =>
  state.members.deleteMemberLoading;
export const selectDeleteMemberError = (state: any) =>
  state.members.deleteMemberError;

// Pagination selectors
export const selectMembersPagination = (state: any) => state.members.pagination;
export const selectMembersCurrentPage = (state: any) =>
  state.members.pagination.currentPage;
export const selectMembersPageSize = (state: any) =>
  state.members.pagination.pageSize;
export const selectMembersTotal = (state: any) =>
  state.members.pagination.totalCount;
export const selectMembersTotalPages = (state: any) =>
  state.members.pagination.totalPages;
export const selectMembersHasNext = (state: any) =>
  state.members.pagination.hasNext;
export const selectMembersHasPrevious = (state: any) =>
  state.members.pagination.hasPrevious;
