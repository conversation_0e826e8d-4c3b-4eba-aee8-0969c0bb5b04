import { all, call, fork, put, takeLatest } from "redux-saga/effects";
import { fetchModulesFailure, fetchModulesRequest, fetchModulesSuccess, ModuleType } from "./redux";
import { ModuleApi } from "@/services/moduleAPI";
import { CreateAdminUserResponse } from "@/types/adminUser";

export interface ModuleResponse extends CreateAdminUserResponse {
    modules: ModuleType[];
}

function* fetchModule(): Generator<any, void, ModuleResponse> {
    try {
        const response = yield call(ModuleApi.fetch);
        yield put(fetchModulesSuccess(response))
    } catch (error: any) {
        yield put(fetchModulesFailure(error.message || 'Failed to fetch module'))
    }
}



export function* moduleSaga() {
    yield all([
    takeLatest(fetchModulesRequest.type, fetchModule)
    ]);
}
