import { call, put, select, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { featureApiService } from "@/services/featureApiService";
import {
  fetchFeatureFlagsRequest,
  fetchFeatureFlagsSuccess,
  fetchFeatureFlagsFailure,
  fetchFeatureFlagRequest,
  fetchFeatureFlagSuccess,
  fetchFeatureFlagFailure,
  createFeatureFlagRequest,
  createFeatureFlagSuccess,
  createFeatureFlagFailure,
  updateFeatureFlagRequest,
  updateFeatureFlagSuccess,
  updateFeatureFlagFailure,
  deleteFeatureFlagRequest,
  deleteFeatureFlagSuccess,
  deleteFeatureFlagFailure,
  fetchB<PERSON>marksRequest,
  fetchBookmarksSuccess,
  fetchBookmarksFailure,
  fetchBookmarkRequest,
  fetchBookmarkSuccess,
  fetchBookmarkFailure,
  createBookmarkRequest,
  createBookmarkSuccess,
  createBookmarkFailure,
  updateBookmarkRequest,
  updateBookmarkSuccess,
  updateBookmarkFailure,
  deleteBookmarkRequest,
  deleteBookmarkSuccess,
  deleteBookmarkFailure,
} from "./redux";
import {
  CreateFeatureFlagData,
  UpdateFeatureFlagData,
  CreateBookmarkData,
  UpdateBookmarkData,
} from "@/types/feature";

// Selectors
const selectFeatureFlagsArray = (state: any) => state.features.featureFlags;
const selectFeatureFlagsLastFetched = (state: any) =>
  state.features.lastFetched;
const selectBookmarksArray = (state: any) => state.features.bookmarks;
const selectBookmarksLastFetched = (state: any) =>
  state.features.bookmarksLastFetched;

// Feature Flags Sagas
function* fetchFeatureFlagsSaga(): Generator<any, void, any> {
  try {
    const featureFlags = yield select(selectFeatureFlagsArray);
    const lastFetched = yield select(selectFeatureFlagsLastFetched);
    const now = Date.now();
    const twentyMinutes = 20 * 60 * 1000;
    const lastFetchedTime = lastFetched ? new Date(lastFetched).getTime() : 0;

    if (featureFlags.length === 0 || now - lastFetchedTime > twentyMinutes) {
      const response = yield call(featureApiService.getFeatureFlags);
      yield put(fetchFeatureFlagsSuccess(response.feature_flags || []));
    } else {
      // Cache is fresh, dispatch success with existing data
      yield put(fetchFeatureFlagsSuccess(featureFlags));
    }
  } catch (error: any) {
    yield put(
      fetchFeatureFlagsFailure(error.message || "Failed to fetch feature flags")
    );
  }
}

function* fetchFeatureFlagSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const flagId = action.payload;
    const response = yield call(featureApiService.getFeatureFlag, flagId);
    yield put(fetchFeatureFlagSuccess(response.feature_flag));
  } catch (error: any) {
    yield put(
      fetchFeatureFlagFailure(error.message || "Failed to fetch feature flag")
    );
  }
}

function* createFeatureFlagSaga(
  action: PayloadAction<CreateFeatureFlagData>
): Generator<any, void, any> {
  try {
    const flagData = action.payload;
    const response = yield call(featureApiService.createFeatureFlag, flagData);
    yield put(createFeatureFlagSuccess(response.feature_flag));
  } catch (error: any) {
    yield put(
      createFeatureFlagFailure(error.message || "Failed to create feature flag")
    );
  }
}

function* updateFeatureFlagSaga(
  action: PayloadAction<{ id: number; data: UpdateFeatureFlagData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const response = yield call(featureApiService.updateFeatureFlag, id, data);
    yield put(updateFeatureFlagSuccess(response.feature_flag));
  } catch (error: any) {
    yield put(
      updateFeatureFlagFailure(error.message || "Failed to update feature flag")
    );
  }
}

function* deleteFeatureFlagSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const flagId = action.payload;
    yield call(featureApiService.deleteFeatureFlag, flagId);
    yield put(deleteFeatureFlagSuccess(flagId));
  } catch (error: any) {
    yield put(
      deleteFeatureFlagFailure(error.message || "Failed to delete feature flag")
    );
  }
}

// Member-specific Feature Flag Sagas
function* fetchFeatureFlagsByMemberSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const memberId = action.payload;
    const response = yield call(
      featureApiService.getFeatureFlagsByMember,
      memberId
    );
    yield put(fetchFeatureFlagsSuccess(response.feature_flags || []));
  } catch (error: any) {
    yield put(
      fetchFeatureFlagsFailure(
        error.message || "Failed to fetch member feature flags"
      )
    );
  }
}

// Bookmarks Sagas
function* fetchBookmarksSaga(): Generator<any, void, any> {
  try {
    const bookmarks = yield select(selectBookmarksArray);
    const lastFetched = yield select(selectBookmarksLastFetched);
    const now = Date.now();
    const twentyMinutes = 20 * 60 * 1000;
    const lastFetchedTime = lastFetched ? new Date(lastFetched).getTime() : 0;

    if (bookmarks.length === 0 || now - lastFetchedTime > twentyMinutes) {
      const response = yield call(featureApiService.getBookmarks);
      yield put(fetchBookmarksSuccess(response.bookmarks || []));
    } else {
      // Cache is fresh, dispatch success with existing data
      yield put(fetchBookmarksSuccess(bookmarks));
    }
  } catch (error: any) {
    yield put(
      fetchBookmarksFailure(error.message || "Failed to fetch bookmarks")
    );
  }
}

function* fetchBookmarkSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const bookmarkId = action.payload;
    const response = yield call(featureApiService.getBookmark, bookmarkId);
    yield put(fetchBookmarkSuccess(response.bookmark));
  } catch (error: any) {
    yield put(
      fetchBookmarkFailure(error.message || "Failed to fetch bookmark")
    );
  }
}

function* createBookmarkSaga(
  action: PayloadAction<CreateBookmarkData>
): Generator<any, void, any> {
  try {
    const bookmarkData = action.payload;
    const response = yield call(featureApiService.createBookmark, bookmarkData);
    yield put(createBookmarkSuccess(response.bookmark));
  } catch (error: any) {
    yield put(
      createBookmarkFailure(error.message || "Failed to create bookmark")
    );
  }
}

function* updateBookmarkSaga(
  action: PayloadAction<{ id: number; data: UpdateBookmarkData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const response = yield call(featureApiService.updateBookmark, id, data);
    yield put(updateBookmarkSuccess(response.bookmark));
  } catch (error: any) {
    yield put(
      updateBookmarkFailure(error.message || "Failed to update bookmark")
    );
  }
}

function* deleteBookmarkSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const bookmarkId = action.payload;
    yield call(featureApiService.deleteBookmark, bookmarkId);
    yield put(deleteBookmarkSuccess(bookmarkId));
  } catch (error: any) {
    yield put(
      deleteBookmarkFailure(error.message || "Failed to delete bookmark")
    );
  }
}

// Member-specific Bookmark Sagas
function* fetchBookmarksByMemberSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const memberId = action.payload;
    const response = yield call(
      featureApiService.getBookmarksByMember,
      memberId
    );
    yield put(fetchBookmarksSuccess(response.bookmarks || []));
  } catch (error: any) {
    yield put(
      fetchBookmarksFailure(error.message || "Failed to fetch member bookmarks")
    );
  }
}

// Watch sagas
export function* featuresSaga() {
  yield takeLatest(fetchFeatureFlagsRequest.type, fetchFeatureFlagsSaga);
  yield takeLatest(fetchFeatureFlagRequest.type, fetchFeatureFlagSaga);
  yield takeLatest(createFeatureFlagRequest.type, createFeatureFlagSaga);
  yield takeLatest(updateFeatureFlagRequest.type, updateFeatureFlagSaga);
  yield takeLatest(deleteFeatureFlagRequest.type, deleteFeatureFlagSaga);
  yield takeLatest(fetchBookmarksRequest.type, fetchBookmarksSaga);
  yield takeLatest(fetchBookmarkRequest.type, fetchBookmarkSaga);
  yield takeLatest(createBookmarkRequest.type, createBookmarkSaga);
  yield takeLatest(updateBookmarkRequest.type, updateBookmarkSaga);
  yield takeLatest(deleteBookmarkRequest.type, deleteBookmarkSaga);
}

// Export member-specific sagas for use in member detail page
export function* fetchFeatureFlagsByMemberSagaAction(
  action: PayloadAction<number>
): Generator<any, void, any> {
  yield call(fetchFeatureFlagsByMemberSaga, action);
}

export function* fetchBookmarksByMemberSagaAction(
  action: PayloadAction<number>
): Generator<any, void, any> {
  yield call(fetchBookmarksByMemberSaga, action);
}
