import { RootState } from "../index";

// Feature Flags Selectors
export const selectFeatureFlags = (state: RootState) =>
  state.features.featureFlags;
export const selectCurrentFeatureFlag = (state: RootState) =>
  state.features.currentFeatureFlag;
export const selectFeatureFlagsLoading = (state: RootState) =>
  state.features.loading;
export const selectFeatureFlagsError = (state: RootState) =>
  state.features.error;
export const selectFeatureFlagsLastFetched = (state: RootState) =>
  state.features.lastFetched;

// Feature Flag CRUD Selectors
export const selectCreateFeatureFlagLoading = (state: RootState) =>
  state.features.createFeatureFlagLoading;
export const selectCreateFeatureFlagError = (state: RootState) =>
  state.features.createFeatureFlagError;
export const selectUpdateFeatureFlagLoading = (state: RootState) =>
  state.features.updateFeatureFlagLoading;
export const selectUpdateFeatureFlagError = (state: RootState) =>
  state.features.updateFeatureFlagError;
export const selectDeleteFeatureFlagLoading = (state: RootState) =>
  state.features.deleteFeatureFlagLoading;
export const selectDeleteFeatureFlagError = (state: RootState) =>
  state.features.deleteFeatureFlagError;

// Bookmarks Selectors
export const selectBookmarks = (state: RootState) => state.features.bookmarks;
export const selectCurrentBookmark = (state: RootState) =>
  state.features.currentBookmark;
export const selectBookmarksLoading = (state: RootState) =>
  state.features.bookmarksLoading;
export const selectBookmarksError = (state: RootState) =>
  state.features.bookmarksError;
export const selectBookmarksLastFetched = (state: RootState) =>
  state.features.bookmarksLastFetched;

// Bookmark CRUD Selectors
export const selectCreateBookmarkLoading = (state: RootState) =>
  state.features.createBookmarkLoading;
export const selectCreateBookmarkError = (state: RootState) =>
  state.features.createBookmarkError;
export const selectUpdateBookmarkLoading = (state: RootState) =>
  state.features.updateBookmarkLoading;
export const selectUpdateBookmarkError = (state: RootState) =>
  state.features.updateBookmarkError;
export const selectDeleteBookmarkLoading = (state: RootState) =>
  state.features.deleteBookmarkLoading;
export const selectDeleteBookmarkError = (state: RootState) =>
  state.features.deleteBookmarkError;

// Computed Selectors
export const selectFeatureFlagsByMember = (
  state: RootState,
  memberId: number
) => state.features.featureFlags.filter((flag) => flag.memberId === memberId);

export const selectEnabledFeatureFlagsByMember = (
  state: RootState,
  memberId: number
) =>
  state.features.featureFlags.filter(
    (flag) => flag.memberId === memberId && flag.enabled
  );

export const selectDisabledFeatureFlagsByMember = (
  state: RootState,
  memberId: number
) =>
  state.features.featureFlags.filter(
    (flag) => flag.memberId === memberId && !flag.enabled
  );

export const selectBookmarksByMember = (state: RootState, memberId: number) =>
  state.features.bookmarks.filter((bookmark) => bookmark.memberId === memberId);

export const selectFeatureFlagById = (state: RootState, flagId: number) =>
  state.features.featureFlags.find((flag) => flag.id === flagId);

export const selectBookmarkById = (state: RootState, bookmarkId: number) =>
  state.features.bookmarks.find((bookmark) => bookmark.id === bookmarkId);

export const selectFeatureFlagByHandle = (
  state: RootState,
  featureHandle: string
) =>
  state.features.featureFlags.find(
    (flag) => flag.featureHandle === featureHandle
  );
