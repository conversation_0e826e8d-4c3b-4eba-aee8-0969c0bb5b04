import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FeatureFlag, Bookmark } from "@/types/feature";

interface FeaturesState {
  // Feature Flags
  featureFlags: FeatureFlag[];
  currentFeatureFlag: FeatureFlag | null;
  loading: boolean;
  error: string | null;
  lastFetched: string | null;

  // Feature Flag CRUD states
  createFeatureFlagLoading: boolean;
  createFeatureFlagError: string | null;
  updateFeatureFlagLoading: boolean;
  updateFeatureFlagError: string | null;
  deleteFeatureFlagLoading: boolean;
  deleteFeatureFlagError: string | null;

  // Bookmarks
  bookmarks: Bookmark[];
  currentBookmark: Bookmark | null;
  bookmarksLoading: boolean;
  bookmarksError: string | null;
  bookmarksLastFetched: string | null;

  // Bookmark CRUD states
  createBookmarkLoading: boolean;
  createBookmarkError: string | null;
  updateBookmarkLoading: boolean;
  updateBookmarkError: string | null;
  deleteBookmarkLoading: boolean;
  deleteBookmarkError: string | null;
}

const initialState: FeaturesState = {
  // Feature Flags
  featureFlags: [],
  currentFeatureFlag: null,
  loading: false,
  error: null,
  lastFetched: null,

  // Feature Flag CRUD states
  createFeatureFlagLoading: false,
  createFeatureFlagError: null,
  updateFeatureFlagLoading: false,
  updateFeatureFlagError: null,
  deleteFeatureFlagLoading: false,
  deleteFeatureFlagError: null,

  // Bookmarks
  bookmarks: [],
  currentBookmark: null,
  bookmarksLoading: false,
  bookmarksError: null,
  bookmarksLastFetched: null,

  // Bookmark CRUD states
  createBookmarkLoading: false,
  createBookmarkError: null,
  updateBookmarkLoading: false,
  updateBookmarkError: null,
  deleteBookmarkLoading: false,
  deleteBookmarkError: null,
};

const featuresSlice = createSlice({
  name: "features",
  initialState,
  reducers: {
    // Feature Flags
    fetchFeatureFlagsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchFeatureFlagsSuccess: (state, action: PayloadAction<FeatureFlag[]>) => {
      state.loading = false;
      state.featureFlags = action.payload;
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchFeatureFlagsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    fetchFeatureFlagRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchFeatureFlagSuccess: (state, action: PayloadAction<FeatureFlag>) => {
      state.loading = false;
      state.currentFeatureFlag = action.payload;
      state.error = null;
    },
    fetchFeatureFlagFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    createFeatureFlagRequest: (state) => {
      state.createFeatureFlagLoading = true;
      state.createFeatureFlagError = null;
    },
    createFeatureFlagSuccess: (state, action: PayloadAction<FeatureFlag>) => {
      state.createFeatureFlagLoading = false;
      state.featureFlags.push(action.payload);
      state.createFeatureFlagError = null;
    },
    createFeatureFlagFailure: (state, action: PayloadAction<string>) => {
      state.createFeatureFlagLoading = false;
      state.createFeatureFlagError = action.payload;
    },

    updateFeatureFlagRequest: (state) => {
      state.updateFeatureFlagLoading = true;
      state.updateFeatureFlagError = null;
    },
    updateFeatureFlagSuccess: (state, action: PayloadAction<FeatureFlag>) => {
      state.updateFeatureFlagLoading = false;
      const index = state.featureFlags.findIndex(
        (f) => f.id === action.payload.id
      );
      if (index !== -1) {
        state.featureFlags[index] = action.payload;
      }
      if (
        state.currentFeatureFlag &&
        state.currentFeatureFlag.id === action.payload.id
      ) {
        state.currentFeatureFlag = action.payload;
      }
      state.updateFeatureFlagError = null;
    },
    updateFeatureFlagFailure: (state, action: PayloadAction<string>) => {
      state.updateFeatureFlagLoading = false;
      state.updateFeatureFlagError = action.payload;
    },

    deleteFeatureFlagRequest: (state) => {
      state.deleteFeatureFlagLoading = true;
      state.deleteFeatureFlagError = null;
    },
    deleteFeatureFlagSuccess: (state, action: PayloadAction<number>) => {
      state.deleteFeatureFlagLoading = false;
      state.featureFlags = state.featureFlags.filter(
        (f) => f.id !== action.payload
      );
      if (
        state.currentFeatureFlag &&
        state.currentFeatureFlag.id === action.payload
      ) {
        state.currentFeatureFlag = null;
      }
      state.deleteFeatureFlagError = null;
    },
    deleteFeatureFlagFailure: (state, action: PayloadAction<string>) => {
      state.deleteFeatureFlagLoading = false;
      state.deleteFeatureFlagError = action.payload;
    },

    // Bookmarks
    fetchBookmarksRequest: (state) => {
      state.bookmarksLoading = true;
      state.bookmarksError = null;
    },
    fetchBookmarksSuccess: (state, action: PayloadAction<Bookmark[]>) => {
      state.bookmarksLoading = false;
      state.bookmarks = action.payload;
      state.bookmarksLastFetched = new Date().toISOString();
      state.bookmarksError = null;
    },
    fetchBookmarksFailure: (state, action: PayloadAction<string>) => {
      state.bookmarksLoading = false;
      state.bookmarksError = action.payload;
    },

    fetchBookmarkRequest: (state) => {
      state.bookmarksLoading = true;
      state.bookmarksError = null;
    },
    fetchBookmarkSuccess: (state, action: PayloadAction<Bookmark>) => {
      state.bookmarksLoading = false;
      state.currentBookmark = action.payload;
      state.bookmarksError = null;
    },
    fetchBookmarkFailure: (state, action: PayloadAction<string>) => {
      state.bookmarksLoading = false;
      state.bookmarksError = action.payload;
    },

    createBookmarkRequest: (state) => {
      state.createBookmarkLoading = true;
      state.createBookmarkError = null;
    },
    createBookmarkSuccess: (state, action: PayloadAction<Bookmark>) => {
      state.createBookmarkLoading = false;
      state.bookmarks.push(action.payload);
      state.createBookmarkError = null;
    },
    createBookmarkFailure: (state, action: PayloadAction<string>) => {
      state.createBookmarkLoading = false;
      state.createBookmarkError = action.payload;
    },

    updateBookmarkRequest: (state) => {
      state.updateBookmarkLoading = true;
      state.updateBookmarkError = null;
    },
    updateBookmarkSuccess: (state, action: PayloadAction<Bookmark>) => {
      state.updateBookmarkLoading = false;
      const index = state.bookmarks.findIndex(
        (b) => b.id === action.payload.id
      );
      if (index !== -1) {
        state.bookmarks[index] = action.payload;
      }
      if (
        state.currentBookmark &&
        state.currentBookmark.id === action.payload.id
      ) {
        state.currentBookmark = action.payload;
      }
      state.updateBookmarkError = null;
    },
    updateBookmarkFailure: (state, action: PayloadAction<string>) => {
      state.updateBookmarkLoading = false;
      state.updateBookmarkError = action.payload;
    },

    deleteBookmarkRequest: (state) => {
      state.deleteBookmarkLoading = true;
      state.deleteBookmarkError = null;
    },
    deleteBookmarkSuccess: (state, action: PayloadAction<number>) => {
      state.deleteBookmarkLoading = false;
      state.bookmarks = state.bookmarks.filter((b) => b.id !== action.payload);
      if (
        state.currentBookmark &&
        state.currentBookmark.id === action.payload
      ) {
        state.currentBookmark = null;
      }
      state.deleteBookmarkError = null;
    },
    deleteBookmarkFailure: (state, action: PayloadAction<string>) => {
      state.deleteBookmarkLoading = false;
      state.deleteBookmarkError = action.payload;
    },

    // Member-specific actions
    fetchFeatureFlagsByMemberRequest: (
      state,
      action: PayloadAction<number>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchFeatureFlagsByMemberSuccess: (
      state,
      action: PayloadAction<FeatureFlag[]>
    ) => {
      state.loading = false;
      state.featureFlags = action.payload;
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchFeatureFlagsByMemberFailure: (
      state,
      action: PayloadAction<string>
    ) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Clear errors
    clearFeatureError: (state) => {
      state.error = null;
    },
    clearCreateFeatureFlagError: (state) => {
      state.createFeatureFlagError = null;
    },
    clearUpdateFeatureFlagError: (state) => {
      state.updateFeatureFlagError = null;
    },
    clearDeleteFeatureFlagError: (state) => {
      state.deleteFeatureFlagError = null;
    },
    clearBookmarkError: (state) => {
      state.bookmarksError = null;
    },
    clearCreateBookmarkError: (state) => {
      state.createBookmarkError = null;
    },
    clearUpdateBookmarkError: (state) => {
      state.updateBookmarkError = null;
    },
    clearDeleteBookmarkError: (state) => {
      state.deleteBookmarkError = null;
    },
  },
});

export const {
  // Feature Flags
  fetchFeatureFlagsRequest,
  fetchFeatureFlagsSuccess,
  fetchFeatureFlagsFailure,
  fetchFeatureFlagRequest,
  fetchFeatureFlagSuccess,
  fetchFeatureFlagFailure,
  createFeatureFlagRequest,
  createFeatureFlagSuccess,
  createFeatureFlagFailure,
  updateFeatureFlagRequest,
  updateFeatureFlagSuccess,
  updateFeatureFlagFailure,
  deleteFeatureFlagRequest,
  deleteFeatureFlagSuccess,
  deleteFeatureFlagFailure,
  
  // Member-specific Feature Flags
  fetchFeatureFlagsByMemberRequest,
  fetchFeatureFlagsByMemberSuccess,
  fetchFeatureFlagsByMemberFailure,
  
  // Bookmarks
  fetchBookmarksRequest,
  fetchBookmarksSuccess,
  fetchBookmarksFailure,
  fetchBookmarkRequest,
  fetchBookmarkSuccess,
  fetchBookmarkFailure,
  createBookmarkRequest,
  createBookmarkSuccess,
  createBookmarkFailure,
  updateBookmarkRequest,
  updateBookmarkSuccess,
  updateBookmarkFailure,
  deleteBookmarkRequest,
  deleteBookmarkSuccess,
  deleteBookmarkFailure,
  
  // Clear errors
  clearFeatureError,
  clearCreateFeatureFlagError,
  clearUpdateFeatureFlagError,
  clearDeleteFeatureFlagError,
  clearBookmarkError,
  clearCreateBookmarkError,
  clearUpdateBookmarkError,
  clearDeleteBookmarkError,
} = featuresSlice.actions;

export default featuresSlice.reducer;
