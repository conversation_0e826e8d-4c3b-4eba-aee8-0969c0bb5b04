import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Role, AllRolesPermissionsResponse, RoleWithPermissions, UpdateRoleData } from "@/types/role";

interface RolesState {
  roles: Role[];
  rolesMap: Record<string, Role>; // Map for O(1) lookups
  allRolesPermissions: AllRolesPermissionsResponse | null;
  loading: boolean;
  error: string | null;
  selectedRole: Role | null;
  lastFetched: number | null; // For cache invalidation
}

const initialState: RolesState = {
  roles: [],
  rolesMap: {},
  allRolesPermissions: null,
  loading: false,
  error: null,
  selectedRole: null,
  lastFetched: null,
};

const rolesSlice = createSlice({
  name: "roles",
  initialState,
  reducers: {
    fetchRolesRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchRolesSuccess(state, action: PayloadAction<Role[]>) {
      state.loading = false;
      state.roles = action.payload;
      state.error = null;
      state.lastFetched = Date.now();

      // Populate Map for O(1) lookups
      state.rolesMap = {};
      action.payload.forEach(role => {
        state.rolesMap[role.slug] = role;
      });
    },
    fetchRolesFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    upsertRoleRequest(state) {
      state.loading = true;
      state.error = null;
    },
    upsertRoleSuccess(state, action: PayloadAction<Role>) {
      state.loading = false;
      state.error = null;

      // Update both list and map
      const existingIndex = state.roles.findIndex(role => role.slug === action.payload.slug);
      if (existingIndex !== -1) {
        // Update existing role
        state.roles[existingIndex] = action.payload;
      } else {
        // Add new role
        state.roles.push(action.payload);
      }
      state.rolesMap[action.payload.slug] = action.payload;
    },
    upsertRoleFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    deleteRoleRequest(state, _action: PayloadAction<string>) {
      state.loading = true;
      state.error = null;
    },
    deleteRoleSuccess(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = null;

      // Remove from both list and map
      state.roles = state.roles.filter(role => role.slug !== action.payload);
      delete state.rolesMap[action.payload];
    },
    deleteRoleFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    bulkDeleteRolesRequest(state, _action: PayloadAction<string[]>) {
      state.loading = true;
      state.error = null;
    },
    bulkDeleteRolesSuccess(state, action: PayloadAction<string[]>) {
      state.loading = false;
      state.error = null;

      // Remove multiple roles from both list and map
      const slugsToDelete = action.payload;
      state.roles = state.roles.filter(role => !slugsToDelete.includes(role.slug));
      slugsToDelete.forEach(slug => {
        delete state.rolesMap[slug];
      });
    },
    bulkDeleteRolesFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    setSelectedRole(state, action: PayloadAction<Role | null>) {
      state.selectedRole = action.payload;
    },
    clearRolesError(state) {
      state.error = null;
    },

    // Cache management
    invalidateRolesCache(state) {
      state.lastFetched = null;
      state.rolesMap = {};
      state.roles = [];
    },
    fetchAllRolesPermissionsRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchAllRolesPermissionsSuccess(state, action: PayloadAction<RoleWithPermissions[]>) {
      state.loading = false;
      state.allRolesPermissions = {
        roles: action.payload,
        total_roles: action.payload.length,
      };
      state.error = null;
    },
    fetchAllRolesPermissionsFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchRolesRequest,
  fetchRolesSuccess,
  fetchRolesFailure,
  upsertRoleRequest,
  upsertRoleSuccess,
  upsertRoleFailure,
  deleteRoleRequest,
  deleteRoleSuccess,
  deleteRoleFailure,
  bulkDeleteRolesRequest,
  bulkDeleteRolesSuccess,
  bulkDeleteRolesFailure,
  setSelectedRole,
  clearRolesError,
  fetchAllRolesPermissionsRequest,
  fetchAllRolesPermissionsSuccess,
  fetchAllRolesPermissionsFailure,
  invalidateRolesCache,
} = rolesSlice.actions;

export default rolesSlice.reducer;
