import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest, select } from "redux-saga/effects";
import { rolesApiService } from "@/services/rolesApiService";
import {
  fetchRolesRequest,
  fetchRolesSuccess,
  fetchRolesFailure,
  upsertRoleRequest,
  upsertRoleSuccess,
  upsertRoleFailure,
  deleteRoleRequest,
  deleteRoleSuccess,
  deleteRoleFailure,
  bulkDeleteRolesRequest,
  bulkDeleteRolesSuccess,
  bulkDeleteRolesFailure,
  fetchAllRolesPermissionsRequest,
  fetchAllRolesPermissionsSuccess,
  fetchAllRolesPermissionsFailure,
} from "./redux";
import { CreateRoleData, UpdateRoleData } from "@/types/role";

function* fetchRolesSaga(): Generator<any, void, any> {
  try {
    const response = yield call(rolesApiService.getRoles);
    yield put(fetchRolesSuccess(response.roles));
  } catch (error: any) {
    yield put(fetchRolesFailure(error.message || "Failed to fetch roles"));
  }
}

function* upsertRoleSaga(
  action: PayloadAction<CreateRoleData | UpdateRoleData>
): Generator<any, void, any> {
  try {
    const response: any = yield call(
      rolesApiService.upsertRole,
      action.payload
    );

    // Create a role object from the response or action payload
    const role = response?.role || {
      slug:
        (action.payload as any).slug ||
        (action.payload as any).name?.toLowerCase().replace(/\s+/g, "_"),
      name: (action.payload as any).name,
      description: (action.payload as any).description || "",
      ...action.payload,
    };

    yield put(upsertRoleSuccess(role));

    // Only refresh permissions data (more efficient than refreshing both roles and permissions)
    yield put(fetchAllRolesPermissionsRequest());
  } catch (error: any) {
    yield put(upsertRoleFailure(error.message || "Failed to save role"));
  }
}

function* deleteRoleSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    yield call(rolesApiService.deleteRole, action.payload);
    yield put(deleteRoleSuccess(action.payload)); // Pass the slug to remove from state
    yield put(fetchRolesRequest());
  } catch (error: any) {
    yield put(deleteRoleFailure(error.message || "Failed to delete role"));
  }
}

function* bulkDeleteRolesSaga(
  action: PayloadAction<string[]>
): Generator<any, void, any> {
  try {
    yield call(rolesApiService.bulkDeleteRoles, action.payload);
    yield put(bulkDeleteRolesSuccess(action.payload)); // Pass the slugs to remove from state
    yield put(fetchRolesRequest());
  } catch (error: any) {
    yield put(
      bulkDeleteRolesFailure(error.message || "Failed to bulk delete roles")
    );
  }
}

function* fetchAllRolesPermissionsSaga(): Generator<any, void, any> {
  try {
    const data = yield call(rolesApiService.getAllRolesPermissions);
    yield put(fetchAllRolesPermissionsSuccess(data.permissions));
  } catch (error: any) {
    yield put(
      fetchAllRolesPermissionsFailure(
        error.message || "Failed to fetch all roles permissions"
      )
    );
  }
}

export function* rolesSaga(): Generator<any, void, any> {
  yield all([
    takeLatest(fetchRolesRequest.type, fetchRolesSaga),
    takeLatest(upsertRoleRequest.type, upsertRoleSaga),
    takeLatest(deleteRoleRequest.type, deleteRoleSaga),
    takeLatest(bulkDeleteRolesRequest.type, bulkDeleteRolesSaga),
    takeLatest(
      fetchAllRolesPermissionsRequest.type,
      fetchAllRolesPermissionsSaga
    ),
  ]);
}
