import { createSelector } from '@reduxjs/toolkit';
import { useAppSelector } from "../hooks";
import { RootState } from "..";
import { Role } from "@/types/role";

// Base selectors
export const selectRolesState = (state: RootState) => state.roles;
export const selectRolesList = (state: RootState) => state.roles.roles;
export const selectRolesMap = (state: RootState) => state.roles.rolesMap;
export const selectRolesLoading = (state: RootState) => state.roles.loading;
export const selectRolesError = (state: RootState) => state.roles.error;
export const selectSelectedRole = (state: RootState) => state.roles.selectedRole;
export const selectAllRolesPermissions = (state: RootState) => state.roles.allRolesPermissions;
export const selectRolesLastFetched = (state: RootState) => state.roles.lastFetched;

// O(1) lookup selectors using Map
export const selectRoleBySlug = (state: RootState, slug: string) => 
  state.roles.rolesMap[slug] || null;

// Memoized selectors using Map for better performance
export const selectRolesByType = createSelector(
  [selectRolesMap],
  (rolesMap: Record<string, Role>) => {
    const roles = Object.values(rolesMap);
    return {
      system: roles.filter(role => role.slug.startsWith('system_')),
      custom: roles.filter(role => !role.slug.startsWith('system_')),
      admin: roles.filter(role => role.slug.includes('admin')),
    };
  }
);

export const selectRolesStats = createSelector(
  [selectRolesMap],
  (rolesMap: Record<string, Role>) => {
    const roles = Object.values(rolesMap);
    return {
      total: roles.length,
      systemRoles: roles.filter(role => role.slug.startsWith('system_')).length,
      customRoles: roles.filter(role => !role.slug.startsWith('system_')).length,
      adminRoles: roles.filter(role => role.slug.includes('admin')).length,
    };
  }
);

// Selector to check if data should be refreshed
export const selectShouldRefreshRoles = createSelector(
  [selectRolesLastFetched, selectRolesList],
  (lastFetched: number | null, rolesList: Role[]) => {
    if (!lastFetched || rolesList.length === 0) return true;
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastFetched > fiveMinutes;
  }
);

// Convenience hooks
export const useRoles = () => useAppSelector(selectRolesState);
export const useRolesList = () => useAppSelector(selectRolesList);
export const useRolesStats = () => useAppSelector(selectRolesStats);
export const useSelectedRole = () => useAppSelector(selectSelectedRole);
export const useRolesLoading = () => useAppSelector(selectRolesLoading);
export const useRolesError = () => useAppSelector(selectRolesError);

// Hook to get role by slug
export const useRoleBySlug = (slug: string) => 
  useAppSelector(state => selectRoleBySlug(state, slug));

// Hook to check if roles should be refreshed
export const useShouldRefreshRoles = () => useAppSelector(selectShouldRefreshRoles);
