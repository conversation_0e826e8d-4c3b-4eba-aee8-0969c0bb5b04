import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Award, AwardListing } from "@/types/award";

interface AwardsState {
  // Awards
  awards: Award[];
  currentAward: Award | null;
  loading: boolean;
  error: string | null;
  lastFetched: string | null;

  // Award CRUD states
  createAwardLoading: boolean;
  createAwardError: string | null;
  updateAwardLoading: boolean;
  updateAwardError: string | null;
  deleteAwardLoading: boolean;
  deleteAwardError: string | null;

  // Award Listings
  awardListings: AwardListing[];
  currentAwardListing: AwardListing | null;
  awardListingsLoading: boolean;
  awardListingsError: string | null;
  awardListingsLastFetched: string | null;

  // Award Listing CRUD states
  createAwardListingLoading: boolean;
  createAwardListingError: string | null;
  updateAwardListingLoading: boolean;
  updateAwardListingError: string | null;
  deleteAwardListingLoading: boolean;
  deleteAwardListingError: string | null;
}

const initialState: AwardsState = {
  // Awards
  awards: [],
  currentAward: null,
  loading: false,
  error: null,
  lastFetched: null,

  // Award CRUD states
  createAwardLoading: false,
  createAwardError: null,
  updateAwardLoading: false,
  updateAwardError: null,
  deleteAwardLoading: false,
  deleteAwardError: null,

  // Award Listings
  awardListings: [],
  currentAwardListing: null,
  awardListingsLoading: false,
  awardListingsError: null,
  awardListingsLastFetched: null,

  // Award Listing CRUD states
  createAwardListingLoading: false,
  createAwardListingError: null,
  updateAwardListingLoading: false,
  updateAwardListingError: null,
  deleteAwardListingLoading: false,
  deleteAwardListingError: null,
};

const awardsSlice = createSlice({
  name: "awards",
  initialState,
  reducers: {
    // Awards
    fetchAwardsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchAwardsSuccess: (state, action: PayloadAction<Award[]>) => {
      state.loading = false;
      state.awards = action.payload;
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchAwardsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    fetchAwardRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchAwardSuccess: (state, action: PayloadAction<Award>) => {
      state.loading = false;
      state.currentAward = action.payload;
      state.error = null;
    },
    fetchAwardFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    createAwardRequest: (state) => {
      state.createAwardLoading = true;
      state.createAwardError = null;
    },
    createAwardSuccess: (state, action: PayloadAction<Award>) => {
      state.createAwardLoading = false;
      state.awards.push(action.payload);
      state.createAwardError = null;
    },
    createAwardFailure: (state, action: PayloadAction<string>) => {
      state.createAwardLoading = false;
      state.createAwardError = action.payload;
    },

    updateAwardRequest: (state) => {
      state.updateAwardLoading = true;
      state.updateAwardError = null;
    },
    updateAwardSuccess: (state, action: PayloadAction<Award>) => {
      state.updateAwardLoading = false;
      const index = state.awards.findIndex((a) => a.id === action.payload.id);
      if (index !== -1) {
        state.awards[index] = action.payload;
      }
      if (state.currentAward && state.currentAward.id === action.payload.id) {
        state.currentAward = action.payload;
      }
      state.updateAwardError = null;
    },
    updateAwardFailure: (state, action: PayloadAction<string>) => {
      state.updateAwardLoading = false;
      state.updateAwardError = action.payload;
    },

    deleteAwardRequest: (state) => {
      state.deleteAwardLoading = true;
      state.deleteAwardError = null;
    },
    deleteAwardSuccess: (state, action: PayloadAction<number>) => {
      state.deleteAwardLoading = false;
      state.awards = state.awards.filter((a) => a.id !== action.payload);
      if (state.currentAward && state.currentAward.id === action.payload) {
        state.currentAward = null;
      }
      state.deleteAwardError = null;
    },
    deleteAwardFailure: (state, action: PayloadAction<string>) => {
      state.deleteAwardLoading = false;
      state.deleteAwardError = action.payload;
    },

    // Award Listings
    fetchAwardListingsRequest: (state) => {
      state.awardListingsLoading = true;
      state.awardListingsError = null;
    },
    fetchAwardListingsSuccess: (
      state,
      action: PayloadAction<AwardListing[]>
    ) => {
      state.awardListingsLoading = false;
      state.awardListings = action.payload;
      state.awardListingsLastFetched = new Date().toISOString();
      state.awardListingsError = null;
    },
    fetchAwardListingsFailure: (state, action: PayloadAction<string>) => {
      state.awardListingsLoading = false;
      state.awardListingsError = action.payload;
    },

    fetchAwardListingRequest: (state) => {
      state.awardListingsLoading = true;
      state.awardListingsError = null;
    },
    fetchAwardListingSuccess: (state, action: PayloadAction<AwardListing>) => {
      state.awardListingsLoading = false;
      state.currentAwardListing = action.payload;
      state.awardListingsError = null;
    },
    fetchAwardListingFailure: (state, action: PayloadAction<string>) => {
      state.awardListingsLoading = false;
      state.awardListingsError = action.payload;
    },

    createAwardListingRequest: (state) => {
      state.createAwardListingLoading = true;
      state.createAwardListingError = null;
    },
    createAwardListingSuccess: (state, action: PayloadAction<AwardListing>) => {
      state.createAwardListingLoading = false;
      state.awardListings.push(action.payload);
      state.createAwardListingError = null;
    },
    createAwardListingFailure: (state, action: PayloadAction<string>) => {
      state.createAwardListingLoading = false;
      state.createAwardListingError = action.payload;
    },

    updateAwardListingRequest: (state) => {
      state.updateAwardListingLoading = true;
      state.updateAwardListingError = null;
    },
    updateAwardListingSuccess: (state, action: PayloadAction<AwardListing>) => {
      state.updateAwardListingLoading = false;
      const index = state.awardListings.findIndex(
        (al) => al.id === action.payload.id
      );
      if (index !== -1) {
        state.awardListings[index] = action.payload;
      }
      if (
        state.currentAwardListing &&
        state.currentAwardListing.id === action.payload.id
      ) {
        state.currentAwardListing = action.payload;
      }
      state.updateAwardListingError = null;
    },
    updateAwardListingFailure: (state, action: PayloadAction<string>) => {
      state.updateAwardListingLoading = false;
      state.updateAwardListingError = action.payload;
    },

    deleteAwardListingRequest: (state) => {
      state.deleteAwardListingLoading = true;
      state.deleteAwardListingError = null;
    },
    deleteAwardListingSuccess: (state, action: PayloadAction<number>) => {
      state.deleteAwardListingLoading = false;
      state.awardListings = state.awardListings.filter(
        (al) => al.id !== action.payload
      );
      if (
        state.currentAwardListing &&
        state.currentAwardListing.id === action.payload
      ) {
        state.currentAwardListing = null;
      }
      state.deleteAwardListingError = null;
    },
    deleteAwardListingFailure: (state, action: PayloadAction<string>) => {
      state.deleteAwardListingLoading = false;
      state.deleteAwardListingError = action.payload;
    },

    // Member-specific actions
    fetchAwardsByMemberRequest: (state, action: PayloadAction<number>) => {
      state.loading = true;
      state.error = null;
    },
    fetchAwardsByMemberSuccess: (state, action: PayloadAction<Award[]>) => {
      state.loading = false;
      state.awards = action.payload;
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchAwardsByMemberFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Clear errors
    clearAwardError: (state) => {
      state.error = null;
    },
    clearCreateAwardError: (state) => {
      state.createAwardError = null;
    },
    clearUpdateAwardError: (state) => {
      state.updateAwardError = null;
    },
    clearDeleteAwardError: (state) => {
      state.deleteAwardError = null;
    },
    clearAwardListingError: (state) => {
      state.awardListingsError = null;
    },
    clearCreateAwardListingError: (state) => {
      state.createAwardListingError = null;
    },
    clearUpdateAwardListingError: (state) => {
      state.updateAwardListingError = null;
    },
    clearDeleteAwardListingError: (state) => {
      state.deleteAwardListingError = null;
    },
  },
});

export const {
  // Awards
  fetchAwardsRequest,
  fetchAwardsSuccess,
  fetchAwardsFailure,
  fetchAwardRequest,
  fetchAwardSuccess,
  fetchAwardFailure,
  createAwardRequest,
  createAwardSuccess,
  createAwardFailure,
  updateAwardRequest,
  updateAwardSuccess,
  updateAwardFailure,
  deleteAwardRequest,
  deleteAwardSuccess,
  deleteAwardFailure,

  // Member-specific Awards
  fetchAwardsByMemberRequest,
  fetchAwardsByMemberSuccess,
  fetchAwardsByMemberFailure,

  // Award Listings
  fetchAwardListingsRequest,
  fetchAwardListingsSuccess,
  fetchAwardListingsFailure,
  fetchAwardListingRequest,
  fetchAwardListingSuccess,
  fetchAwardListingFailure,
  createAwardListingRequest,
  createAwardListingSuccess,
  createAwardListingFailure,
  updateAwardListingRequest,
  updateAwardListingSuccess,
  updateAwardListingFailure,
  deleteAwardListingRequest,
  deleteAwardListingSuccess,
  deleteAwardListingFailure,

  // Clear errors
  clearAwardError,
  clearCreateAwardError,
  clearUpdateAwardError,
  clearDeleteAwardError,
  clearAwardListingError,
  clearCreateAwardListingError,
  clearUpdateAwardListingError,
  clearDeleteAwardListingError,
} = awardsSlice.actions;

export default awardsSlice.reducer;
