import { RootState } from "../index";

// Awards Selectors
export const selectAwards = (state: RootState) => state.awards.awards;
export const selectCurrentAward = (state: RootState) =>
  state.awards.currentAward;
export const selectAwardsLoading = (state: RootState) => state.awards.loading;
export const selectAwardsError = (state: RootState) => state.awards.error;
export const selectAwardsLastFetched = (state: RootState) =>
  state.awards.lastFetched;

// Award CRUD Selectors
export const selectCreateAwardLoading = (state: RootState) =>
  state.awards.createAwardLoading;
export const selectCreateAwardError = (state: RootState) =>
  state.awards.createAwardError;
export const selectUpdateAwardLoading = (state: RootState) =>
  state.awards.updateAwardLoading;
export const selectUpdateAwardError = (state: RootState) =>
  state.awards.updateAwardError;
export const selectDeleteAwardLoading = (state: RootState) =>
  state.awards.deleteAwardLoading;
export const selectDeleteAwardError = (state: RootState) =>
  state.awards.deleteAwardError;

// Award Listings Selectors
export const selectAwardListings = (state: RootState) =>
  state.awards.awardListings;
export const selectCurrentAwardListing = (state: RootState) =>
  state.awards.currentAwardListing;
export const selectAwardListingsLoading = (state: RootState) =>
  state.awards.awardListingsLoading;
export const selectAwardListingsError = (state: RootState) =>
  state.awards.awardListingsError;
export const selectAwardListingsLastFetched = (state: RootState) =>
  state.awards.awardListingsLastFetched;

// Award Listing CRUD Selectors
export const selectCreateAwardListingLoading = (state: RootState) =>
  state.awards.createAwardListingLoading;
export const selectCreateAwardListingError = (state: RootState) =>
  state.awards.createAwardListingError;
export const selectUpdateAwardListingLoading = (state: RootState) =>
  state.awards.updateAwardListingLoading;
export const selectUpdateAwardListingError = (state: RootState) =>
  state.awards.updateAwardListingError;
export const selectDeleteAwardListingLoading = (state: RootState) =>
  state.awards.deleteAwardListingLoading;
export const selectDeleteAwardListingError = (state: RootState) =>
  state.awards.deleteAwardListingError;

// Computed Selectors
export const selectAwardsByMember = (state: RootState, memberId: number) =>
  state.awards.awards.filter((award) => award.memberId === memberId);

export const selectAwardsByOrganization = (
  state: RootState,
  organizationId: number
) =>
  state.awards.awards.filter(
    (award) => award.organizationId === organizationId
  );

export const selectAwardById = (state: RootState, awardId: number) =>
  state.awards.awards.find((award) => award.id === awardId);

export const selectAwardListingById = (state: RootState, listingId: number) =>
  state.awards.awardListings.find((listing) => listing.id === listingId);
