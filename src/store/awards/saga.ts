import { call, put, select, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { awardApiService } from "@/services/awardApiService";
import {
  fetchAwardsRequest,
  fetchAwardsSuccess,
  fetchAwardsFailure,
  fetchAwardRequest,
  fetchAwardSuccess,
  fetchAwardFailure,
  createA<PERSON>Request,
  createAwardSuccess,
  createAwardFailure,
  updateAwardRequest,
  updateAwardSuccess,
  updateAwardFailure,
  deleteAwardRequest,
  deleteAwardSuccess,
  deleteAwardFailure,
  fetchAwardsByMemberRequest,
  fetchAwardsByMemberSuccess,
  fetchAwardsByMemberFailure,
  fetchAwardListingsRequest,
  fetchAwardListingsSuccess,
  fetchAwardListingsFailure,
  fetchAwardListingRequest,
  fetchAwardListingSuccess,
  fetchAwardListingFailure,
  createAwardListingRequest,
  createAwardListingSuccess,
  createAwardListingFailure,
  updateAwardListingRequest,
  updateAwardListingSuccess,
  updateAwardListingFailure,
  deleteAwardListingRequest,
  deleteAwardListingSuccess,
  deleteAwardListingFailure,
} from "./redux";
import {
  CreateAwardData,
  UpdateAwardData,
  CreateAwardListingData,
  UpdateAwardListingData,
} from "@/types/award";

// Selectors
const selectAwardsArray = (state: any) => state.awards.awards;
const selectAwardsLastFetched = (state: any) => state.awards.lastFetched;
const selectAwardListingsArray = (state: any) => state.awards.awardListings;
const selectAwardListingsLastFetched = (state: any) =>
  state.awards.awardListingsLastFetched;

// Awards Sagas
function* fetchAwardsSaga(): Generator<any, void, any> {
  try {
    const awards = yield select(selectAwardsArray);
    const lastFetched = yield select(selectAwardsLastFetched);
    const now = Date.now();
    const twentyMinutes = 20 * 60 * 1000;
    const lastFetchedTime = lastFetched ? new Date(lastFetched).getTime() : 0;

    if (awards.length === 0 || now - lastFetchedTime > twentyMinutes) {
      const response = yield call(awardApiService.getAwards);
      yield put(fetchAwardsSuccess(response.awards || []));
    } else {
      // Cache is fresh, dispatch success with existing data
      yield put(fetchAwardsSuccess(awards));
    }
  } catch (error: any) {
    yield put(fetchAwardsFailure(error.message || "Failed to fetch awards"));
  }
}

function* fetchAwardSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const awardId = action.payload;
    const response = yield call(awardApiService.getAward, awardId);
    yield put(fetchAwardSuccess(response.award));
  } catch (error: any) {
    yield put(fetchAwardFailure(error.message || "Failed to fetch award"));
  }
}

function* createAwardSaga(
  action: PayloadAction<CreateAwardData>
): Generator<any, void, any> {
  try {
    const awardData = action.payload;
    const response = yield call(awardApiService.createAward, awardData);
    yield put(createAwardSuccess(response.award));
  } catch (error: any) {
    yield put(createAwardFailure(error.message || "Failed to create award"));
  }
}

function* updateAwardSaga(
  action: PayloadAction<{ id: number; data: UpdateAwardData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const response = yield call(awardApiService.updateAward, id, data);
    yield put(updateAwardSuccess(response.award));
  } catch (error: any) {
    yield put(updateAwardFailure(error.message || "Failed to update award"));
  }
}

function* deleteAwardSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const awardId = action.payload;
    yield call(awardApiService.deleteAward, awardId);
    yield put(deleteAwardSuccess(awardId));
  } catch (error: any) {
    yield put(deleteAwardFailure(error.message || "Failed to delete award"));
  }
}

// Member-specific Award Sagas
function* fetchAwardsByMemberSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const memberId = action.payload;
    const response = yield call(awardApiService.getAwardsByMember, memberId);
    yield put(fetchAwardsSuccess(response.awards || []));
  } catch (error: any) {
    yield put(
      fetchAwardsFailure(error.message || "Failed to fetch member awards")
    );
  }
}

// Award Listings Sagas
function* fetchAwardListingsSaga(): Generator<any, void, any> {
  try {
    const awardListings = yield select(selectAwardListingsArray);
    const lastFetched = yield select(selectAwardListingsLastFetched);
    const now = Date.now();
    const twentyMinutes = 20 * 60 * 1000;
    const lastFetchedTime = lastFetched ? new Date(lastFetched).getTime() : 0;

    if (awardListings.length === 0 || now - lastFetchedTime > twentyMinutes) {
      const response = yield call(awardApiService.getAwardListings);
      yield put(fetchAwardListingsSuccess(response.award_listings || []));
    } else {
      // Cache is fresh, dispatch success with existing data
      yield put(fetchAwardListingsSuccess(awardListings));
    }
  } catch (error: any) {
    yield put(
      fetchAwardListingsFailure(
        error.message || "Failed to fetch award listings"
      )
    );
  }
}

function* fetchAwardListingSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const listingId = action.payload;
    const response = yield call(awardApiService.getAwardListing, listingId);
    yield put(fetchAwardListingSuccess(response.award_listing));
  } catch (error: any) {
    yield put(
      fetchAwardListingFailure(error.message || "Failed to fetch award listing")
    );
  }
}

function* createAwardListingSaga(
  action: PayloadAction<CreateAwardListingData>
): Generator<any, void, any> {
  try {
    const listingData = action.payload;
    const response = yield call(
      awardApiService.createAwardListing,
      listingData
    );
    yield put(createAwardListingSuccess(response.award_listing));
  } catch (error: any) {
    yield put(
      createAwardListingFailure(
        error.message || "Failed to create award listing"
      )
    );
  }
}

function* updateAwardListingSaga(
  action: PayloadAction<{ id: number; data: UpdateAwardListingData }>
): Generator<any, void, any> {
  try {
    const { id, data } = action.payload;
    const response = yield call(awardApiService.updateAwardListing, id, data);
    yield put(updateAwardListingSuccess(response.award_listing));
  } catch (error: any) {
    yield put(
      updateAwardListingFailure(
        error.message || "Failed to update award listing"
      )
    );
  }
}

function* deleteAwardListingSaga(
  action: PayloadAction<number>
): Generator<any, void, any> {
  try {
    const listingId = action.payload;
    yield call(awardApiService.deleteAwardListing, listingId);
    yield put(deleteAwardListingSuccess(listingId));
  } catch (error: any) {
    yield put(
      deleteAwardListingFailure(
        error.message || "Failed to delete award listing"
      )
    );
  }
}

// Watch sagas
export function* awardsSaga() {
  yield takeLatest(fetchAwardsRequest.type, fetchAwardsSaga);
  yield takeLatest(fetchAwardRequest.type, fetchAwardSaga);
  yield takeLatest(createAwardRequest.type, createAwardSaga);
  yield takeLatest(updateAwardRequest.type, updateAwardSaga);
  yield takeLatest(deleteAwardRequest.type, deleteAwardSaga);
  yield takeLatest(fetchAwardsByMemberRequest.type, fetchAwardsByMemberSaga);
  yield takeLatest(fetchAwardListingsRequest.type, fetchAwardListingsSaga);
  yield takeLatest(fetchAwardListingRequest.type, fetchAwardListingSaga);
  yield takeLatest(createAwardListingRequest.type, createAwardListingSaga);
  yield takeLatest(updateAwardListingRequest.type, updateAwardListingSaga);
  yield takeLatest(deleteAwardListingRequest.type, deleteAwardListingSaga);
}
