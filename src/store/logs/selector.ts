import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { LogsState } from "./redux";

// Base selectors
const selectLogsState = (state: RootState) => state.logs;
const selectLogs = (state: RootState) => state.logs.logs;
const selectLoading = (state: RootState) => state.logs.loading;
const selectExportLoading = (state: RootState) => state.logs.exportLoading;
const selectError = (state: RootState) => state.logs.error;
const selectExportError = (state: RootState) => state.logs.exportError;
const selectPagination = (state: RootState) => state.logs.pagination;
const selectFilters = (state: RootState) => state.logs.filters;
const selectAvailableActions = (state: RootState) =>
  state.logs.availableActions;
const selectExportFields = (state: RootState) => state.logs.exportFields;
const selectActionsLoading = (state: RootState) => state.logs.actionsLoading;
const selectFieldsLoading = (state: RootState) => state.logs.fieldsLoading;

// Tab state selectors
const selectActiveTab = (state: RootState) => state.logs.activeTab;
const selectAuditFilters = (state: RootState) => state.logs.auditFilters;
const selectMemberDeleteFilters = (state: RootState) =>
  state.logs.memberDeleteFilters;

// State selectors
export const logsSelectors = {
  // Basic state selectors
  selectLogs,
  selectLoading,
  selectExportLoading,
  selectError,
  selectExportError,
  selectPagination,
  selectFilters,
  selectAvailableActions,
  selectExportFields,
  selectActionsLoading,
  selectFieldsLoading,

  // Tab state selectors
  selectActiveTab,
  selectAuditFilters,
  selectMemberDeleteFilters,

  // Derived selectors
  selectLogsByAction: createSelector(
    [selectLogs, (_, action: string) => action],
    (logs, action) => {
      if (!action) return logs;
      return logs.filter((log) => log.action === action);
    }
  ),

  selectLogsByDateRange: createSelector(
    [
      selectLogs,
      (_, startDate: string, endDate: string) => ({ startDate, endDate }),
    ],
    (logs, { startDate, endDate }) => {
      if (!startDate && !endDate) return logs;
      return logs.filter((log) => {
        const logDate = new Date(log.timestamp);
        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;

        if (start && logDate < start) return false;
        if (end && logDate > end) return false;
        return true;
      });
    }
  ),

  selectLogsSummary: createSelector([selectLogs], (logs) => {
    const summary = {
      total: logs.length,
      byAction: logs.reduce((acc, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byUser: logs.reduce((acc, log) => {
        acc[log.userUuid] = (acc[log.userUuid] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      recentLogs: logs
        .sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
        .slice(0, 5),
    };
    return summary;
  }),

  // Pagination selectors
  selectTotalPages: createSelector([selectPagination], (pagination) =>
    Math.ceil(pagination.total_count / pagination.page_size)
  ),

  selectHasNextPage: createSelector([selectPagination], (pagination) => {
    const totalPages = Math.ceil(pagination.total_count / pagination.page_size);
    return pagination.page < totalPages;
  }),

  selectHasPreviousPage: createSelector(
    [selectPagination],
    (pagination) => pagination.page > 1
  ),

  selectCurrentPageInfo: createSelector([selectPagination], (pagination) => {
    const totalPages = Math.ceil(pagination.total_count / pagination.page_size);
    const startItem = (pagination.page - 1) * pagination.page_size + 1;
    const endItem = Math.min(
      pagination.page * pagination.page_size,
      pagination.total_count
    );

    return {
      currentPage: pagination.page,
      totalPages,
      pageSize: pagination.page_size,
      totalCount: pagination.total_count,
      startItem,
      endItem,
      hasNext: pagination.page < totalPages,
      hasPrevious: pagination.page > 1,
    };
  }),

  // Filter selectors
  selectActiveFiltersCount: createSelector([selectFilters], (filters) => {
    const { page, page_size, sort_order, ...filterFields } = filters;
    return Object.values(filterFields).filter(
      (value) => value !== undefined && value !== ""
    ).length;
  }),

  selectHasActiveFilters: createSelector([selectFilters], (filters) => {
    const { page, page_size, sort_order, ...filterFields } = filters;
    return Object.values(filterFields).some(
      (value) => value !== undefined && value !== ""
    );
  }),

  // Loading state selectors
  selectIsLoading: createSelector(
    [
      selectLoading,
      selectExportLoading,
      selectActionsLoading,
      selectFieldsLoading,
    ],
    (loading, exportLoading, actionsLoading, fieldsLoading) =>
      loading || exportLoading || actionsLoading || fieldsLoading
  ),

  selectHasError: createSelector(
    [selectError, selectExportError],
    (error, exportError) => error !== null || exportError !== null
  ),

  // Export selectors
  selectSelectedExportFields: createSelector(
    [selectExportFields],
    (exportFields) =>
      exportFields.filter((field) =>
        ["timestamp", "username", "action", "purpose"].includes(field.field)
      )
  ),

  selectExportFieldsOptions: createSelector(
    [selectExportFields],
    (exportFields) =>
      exportFields.map((field) => ({
        value: field.field,
        label: field.display_name,
        description: field.description,
      }))
  ),
};
