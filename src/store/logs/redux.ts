// Logs Redux Store
import { createAction, createReducer } from "@reduxjs/toolkit";
import {
  LogEntry,
  LogFilterParams,
  PaginationState,
  ExportField,
  ExportRequest,
} from "../../types/log";

// State Interface
export interface LogsState {
  logs: LogEntry[];
  loading: boolean;
  exportLoading: boolean;
  error: string | null;
  exportError: string | null;
  pagination: PaginationState;
  filters: LogFilterParams;
  availableActions: { label: string; value: string }[];
  exportFields: ExportField[];
  actionsLoading: boolean;
  fieldsLoading: boolean;
  // Tab state management
  activeTab: "audit" | "member_delete";
  auditFilters: LogFilterParams;
  memberDeleteFilters: LogFilterParams;
}

// Initial State
const initialState: LogsState = {
  logs: [],
  loading: false,
  exportLoading: false,
  error: null,
  exportError: null,
  pagination: {
    page: 1,
    page_size: 50,
    total_count: 0,
  },
  filters: {
    page: 1,
    page_size: 50,
    sort_order: "desc",
  },
  availableActions: [],
  exportFields: [],
  actionsLoading: false,
  fieldsLoading: false,
  // Tab state management
  activeTab: "audit",
  auditFilters: {
    page: 1,
    page_size: 50,
    sort_order: "desc",
  },
  memberDeleteFilters: {
    page: 1,
    page_size: 50,
    sort_order: "desc",
  },
};

// Actions
export const logsActions = {
  // List logs actions
  fetchLogs: createAction<LogFilterParams>("logs/fetchLogs"),
  fetchLogsSuccess: createAction<{
    logs: LogEntry[];
    pagination: PaginationState;
  }>("logs/fetchLogsSuccess"),
  fetchLogsFailure: createAction<string>("logs/fetchLogsFailure"),

  // My logs actions (for member users)
  fetchMyLogs: createAction<LogFilterParams>("logs/fetchMyLogs"),
  fetchMyLogsSuccess: createAction<{
    logs: LogEntry[];
    pagination: PaginationState;
  }>("logs/fetchMyLogsSuccess"),
  fetchMyLogsFailure: createAction<string>("logs/fetchMyLogsFailure"),

  // Export actions
  exportLogs: createAction<ExportRequest>("logs/exportLogs"),
  exportLogsSuccess: createAction<{
    csv_content: string;
    filename: string;
    export_timestamp: string;
  }>("logs/exportLogsSuccess"),
  exportLogsFailure: createAction<string>("logs/exportLogsFailure"),

  // Available actions
  fetchActions: createAction("logs/fetchActions"),
  fetchActionsSuccess: createAction<string[]>("logs/fetchActionsSuccess"),
  fetchActionsFailure: createAction<string>("logs/fetchActionsFailure"),

  // Export fields
  fetchExportFields: createAction("logs/fetchExportFields"),
  fetchExportFieldsSuccess: createAction<ExportField[]>(
    "logs/fetchExportFieldsSuccess"
  ),
  fetchExportFieldsFailure: createAction<string>(
    "logs/fetchExportFieldsFailure"
  ),

  // Filter actions
  setFilters: createAction<LogFilterParams>("logs/setFilters"),
  updateFilters: createAction<Partial<LogFilterParams>>("logs/updateFilters"),
  clearFilters: createAction("logs/clearFilters"),

  // UI actions
  setPage: createAction<number>("logs/setPage"),
  setPageSize: createAction<number>("logs/setPageSize"),
  clearError: createAction("logs/clearError"),
  clearExportError: createAction("logs/clearExportError"),

  // Tab management actions
  setActiveTab: createAction<"audit" | "member_delete">("logs/setActiveTab"),
  setAuditFilters: createAction<LogFilterParams>("logs/setAuditFilters"),
  setMemberDeleteFilters: createAction<LogFilterParams>(
    "logs/setMemberDeleteFilters"
  ),
  updateAuditFilters: createAction<Partial<LogFilterParams>>(
    "logs/updateAuditFilters"
  ),
  updateMemberDeleteFilters: createAction<Partial<LogFilterParams>>(
    "logs/updateMemberDeleteFilters"
  ),
};

// Reducer
export const logsReducer = createReducer(initialState, (builder) => {
  builder
    // Fetch Logs
    .addCase(logsActions.fetchLogs, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(logsActions.fetchLogsSuccess, (state, action) => {
      state.loading = false;
      state.logs = action.payload.logs;
      state.pagination = action.payload.pagination;
    })
    .addCase(logsActions.fetchLogsFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })

    // Fetch My Logs
    .addCase(logsActions.fetchMyLogs, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(logsActions.fetchMyLogsSuccess, (state, action) => {
      state.loading = false;
      state.logs = action.payload.logs;
      state.pagination = action.payload.pagination;
    })
    .addCase(logsActions.fetchMyLogsFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })

    // Export Logs
    .addCase(logsActions.exportLogs, (state) => {
      state.exportLoading = true;
      state.exportError = null;
    })
    .addCase(logsActions.exportLogsSuccess, (state, action) => {
      state.exportLoading = false;
      // CSV download will be handled in the saga
    })
    .addCase(logsActions.exportLogsFailure, (state, action) => {
      state.exportLoading = false;
      state.exportError = action.payload;
    })

    // Fetch Actions
    .addCase(logsActions.fetchActions, (state) => {
      state.actionsLoading = true;
    })
    .addCase(logsActions.fetchActionsSuccess, (state, action) => {
      state.actionsLoading = false;
      state.availableActions = action.payload.map((action) => ({
        label:
          action.split("_").join(" ").charAt(0).toUpperCase() +
          action.split("_").join(" ").slice(1),
        value: action,
      }));
    })
    .addCase(logsActions.fetchActionsFailure, (state, action) => {
      state.actionsLoading = false;
      state.error = action.payload;
    })

    // Fetch Export Fields
    .addCase(logsActions.fetchExportFields, (state) => {
      state.fieldsLoading = true;
    })
    .addCase(logsActions.fetchExportFieldsSuccess, (state, action) => {
      state.fieldsLoading = false;
      state.exportFields = action.payload;
    })
    .addCase(logsActions.fetchExportFieldsFailure, (state, action) => {
      state.fieldsLoading = false;
      state.error = action.payload;
    })

    // Filter actions
    .addCase(logsActions.setFilters, (state, action) => {
      state.filters = action.payload;
    })
    .addCase(logsActions.updateFilters, (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    })
    .addCase(logsActions.clearFilters, (state) => {
      state.filters = initialState.filters;
    })

    // UI actions
    .addCase(logsActions.setPage, (state, action) => {
      state.pagination.page = action.payload;
      state.filters.page = action.payload;
    })
    .addCase(logsActions.setPageSize, (state, action) => {
      state.pagination.page_size = action.payload;
      state.pagination.page = 1; // Reset to first page
      state.filters.page_size = action.payload;
      state.filters.page = 1;
    })
    .addCase(logsActions.clearError, (state) => {
      state.error = null;
    })
    .addCase(logsActions.clearExportError, (state) => {
      state.exportError = null;
    })

    // Tab management actions
    .addCase(logsActions.setActiveTab, (state, action) => {
      state.activeTab = action.payload;
      // Switch to the appropriate filters based on active tab
      if (action.payload === "audit") {
        state.filters = state.auditFilters;
      } else {
        state.filters = state.memberDeleteFilters;
      }
    })
    .addCase(logsActions.setAuditFilters, (state, action) => {
      state.auditFilters = action.payload;
      if (state.activeTab === "audit") {
        state.filters = action.payload;
      }
    })
    .addCase(logsActions.setMemberDeleteFilters, (state, action) => {
      state.memberDeleteFilters = action.payload;
      if (state.activeTab === "member_delete") {
        state.filters = action.payload;
      }
    })
    .addCase(logsActions.updateAuditFilters, (state, action) => {
      state.auditFilters = { ...state.auditFilters, ...action.payload };
      if (state.activeTab === "audit") {
        state.filters = { ...state.filters, ...action.payload };
      }
    })
    .addCase(logsActions.updateMemberDeleteFilters, (state, action) => {
      state.memberDeleteFilters = {
        ...state.memberDeleteFilters,
        ...action.payload,
      };
      if (state.activeTab === "member_delete") {
        state.filters = { ...state.filters, ...action.payload };
      }
    });
});

export default logsReducer;
