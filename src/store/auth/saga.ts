import { authApiService } from "@/services/authApiService";
import {
  LoginFormData,
  MFAFormData,
  User,
  ForgotPasswordFormData,
  ResetPasswordFormData,
} from "@/types/auth";
import { syncCognitoTokensToCookies } from "@/utils/auth";
import { navigate } from "@/utils/routerService";
import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, fork, put, select, takeLatest } from "redux-saga/effects";
import {
  confirmMFAFailure,
  confirmMFARequest,
  confirmMFASuccess,
  loginFailure,
  loginMFARequired,
  loginRequest,
  loginSuccess,
  logoutFailure,
  logoutRequest,
  logoutSuccess,
  refreshUserFailure,
  refreshUserRequest,
  refreshUserSuccess,
  setupTOTPFailure,
  setupTOTPRequest,
  setupTOTPSuccess,
  setTotpSetup,
  setEmailVerificationRequired,
  confirmEmailVerificationRequest,
  confirmEmailVerificationSuccess,
  confirmEmailVerificationFailure,
  resendEmailVerificationRequest,
  resendEmailVerificationSuccess,
  resendEmailVerificationFailure,
  initiateForgotPasswordRequest,
  initiateForgotPasswordSuccess,
  initiateForgotPasswordFailure,
  confirmForgotPasswordRequest,
  confirmForgotPasswordSuccess,
  confirmForgotPasswordFailure,
} from "./redux";
import { initAction } from "../init/action";

function* loginSaga(
  action: PayloadAction<LoginFormData>
): Generator<any, void, any> {
  try {
    console.log("Login saga started with payload:", action.payload);
    const result = yield call(authApiService.login, action.payload);
    console.log("Login result:", result);

    if (result && result.isSignedIn === false && result.nextStep) {
      let mfaType = result.nextStep.signInStep;
      console.log("Next step:", mfaType);

      // Handle TOTP setup and MFA verification
      if (mfaType === "CONFIRM_SIGN_IN_WITH_TOTP_CODE") {
        // User needs to enter TOTP code (MFA verification)
        mfaType = "TOTP";
        yield put(loginMFARequired({ mfaType }));
        return;
      } else if (mfaType === "CONTINUE_SIGN_IN_WITH_TOTP_SETUP") {
        // User needs to setup TOTP for the first time
        console.log("Setting up TOTP for first time");
        yield put(
          setTotpSetup({
            required: true,
            sharedSecret: result.nextStep?.totpSetupDetails?.sharedSecret,
          })
        );
        return;
      } else {
        // All other verification steps (SMS, email, etc.) go through email verification
        console.log(
          "Forcing verification step to email verification:",
          mfaType
        );
        yield put(
          setEmailVerificationRequired({
            username: action.payload.username,
            password: action.payload.password,
          })
        );
        return;
      }
    } else {
      yield put(loginSuccess(result));
    }
  } catch (error: any) {
    console.log("Login error:", error);
    // Check if error is due to unverified email
    if (
      error.message &&
      (error.message.includes("UserNotConfirmedException") ||
        error.message.includes("User is not confirmed") ||
        error.message.includes("not confirmed"))
    ) {
      yield put(
        setEmailVerificationRequired({
          username: action.payload.username,
          password: action.payload.password,
        })
      );
    } else if (
      error.message &&
      error.message.includes(
        "User cannot be confirmed. Current status is CONFIRMED"
      )
    ) {
      console.log("User already confirmed, proceeding with login");
      // Try to log in again since user is already confirmed
      try {
        const signInResult = yield call(authApiService.login, action.payload);
        yield put(loginSuccess(signInResult));
        return;
      } catch (retryError: any) {
        // If retry fails, show the original error
        yield put(loginFailure(retryError.message || "Login failed"));
        return;
      }
    } else {
      yield put(loginFailure(error.message || "Login failed"));
    }
  }
}

function* confirmMFASaga(
  action: PayloadAction<MFAFormData>
): Generator<any, void, any> {
  try {
    const state = yield select();
    const username =
      state.auth.user?.username || state.auth.loginData?.username || "";
    const result = yield call(
      authApiService.confirmMFA,
      username,
      action.payload.code
    );
    yield put(confirmMFASuccess(result));
    if (result && result.isSignedIn) {
      const user = yield call(authApiService.getCurrentUser);

      yield call(syncCognitoTokensToCookies, user.username);
      yield put(initAction());
      yield call(navigate, "/dashboard");
    }
  } catch (error: any) {
    yield put(confirmMFAFailure(error.message || "MFA confirmation failed"));
  }
}

function* setupTOTPSaga(
  action: PayloadAction<{ code: string }>
): Generator<any, void, any> {
  try {
    const result = yield call(authApiService.setupTOTP, action.payload.code);
    yield put(setupTOTPSuccess());

    // After successful TOTP setup, the user should be fully authenticated
    // Get current user data and set as authenticated
    const currentUser = yield call(authApiService.getCurrentUser);
    if (currentUser) {
      yield put(loginSuccess(currentUser));
    }
  } catch (error: any) {
    yield put(setupTOTPFailure(error.message || "TOTP setup failed"));
  }
}

function* confirmEmailVerificationSaga(
  action: PayloadAction<{ username: string; code: string }>
): Generator<any, void, any> {
  try {
    yield call(
      authApiService.confirmSignUp,
      action.payload.username,
      action.payload.code
    );
    yield put(confirmEmailVerificationSuccess());
  } catch (error: any) {
    // Check if user is already confirmed
    if (
      error.message &&
      error.message.includes(
        "User cannot be confirmed. Current status is CONFIRMED"
      )
    ) {
      console.log("User already confirmed, proceeding to TOTP setup");
      // User is already confirmed, proceed to TOTP setup
      yield put(confirmEmailVerificationSuccess());
    } else {
      yield put(
        confirmEmailVerificationFailure(
          error.message || "Email verification failed"
        )
      );
    }
  }
}

function* resendEmailVerificationSaga(
  action: PayloadAction<{ username: string }>
): Generator<any, void, any> {
  try {
    yield call(authApiService.resendSignUpCode, action.payload.username);
    yield put(resendEmailVerificationSuccess());
  } catch (error: any) {
    yield put(
      resendEmailVerificationFailure(
        error.message || "Failed to resend verification code"
      )
    );
  }
}


function* refreshUserSaga(): Generator<any, void, any> {
  try {
    const user: User = yield call(authApiService.getCurrentUser);
    yield put(refreshUserSuccess(user));
  } catch (error: any) {
    yield put(refreshUserFailure(error.message || "Failed to refresh user"));
  }
}

function* initiateForgotPasswordSaga(
  action: PayloadAction<ForgotPasswordFormData>
): Generator<any, void, any> {
  try {
    console.log("Initiating forgot password for:", action.payload.email);
    const result = yield call(
      authApiService.initiateForgotPassword,
      action.payload
    );
    console.log("Forgot password initiated:", result);

    yield put(
      initiateForgotPasswordSuccess({
        username: result.username,
        codeDeliveryDetails: result.codeDeliveryDetails,
      })
    );
  } catch (error: any) {
    console.error("Forgot password initiation failed:", error);
    yield put(
      initiateForgotPasswordFailure(
        error.message || "Failed to initiate password reset"
      )
    );
  }
}

function* confirmForgotPasswordSaga(
  action: PayloadAction<ResetPasswordFormData>
): Generator<any, void, any> {
  try {
    console.log("Confirming forgot password for:", action.payload.email);
    const result = yield call(
      authApiService.confirmForgotPassword,
      action.payload
    );
    console.log("Password reset confirmed:", result);

    yield put(confirmForgotPasswordSuccess());
  } catch (error: any) {
    console.error("Password reset confirmation failed:", error);
    yield put(
      confirmForgotPasswordFailure(error.message || "Failed to reset password")
    );
  }
}

export function* authSaga(): Generator<any, void, any> {
  yield all([
    takeLatest(loginRequest.type, loginSaga),
    takeLatest(confirmMFARequest.type, confirmMFASaga),
    takeLatest(setupTOTPRequest.type, setupTOTPSaga),
    takeLatest(
      confirmEmailVerificationRequest.type,
      confirmEmailVerificationSaga
    ),
    takeLatest(
      resendEmailVerificationRequest.type,
      resendEmailVerificationSaga
    ),
    takeLatest(initiateForgotPasswordRequest.type, initiateForgotPasswordSaga),
    takeLatest(confirmForgotPasswordRequest.type, confirmForgotPasswordSaga),
    takeLatest(refreshUserRequest.type, refreshUserSaga),
  ]);
}
