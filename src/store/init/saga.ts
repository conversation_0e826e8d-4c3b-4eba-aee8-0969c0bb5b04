import { all, call, put, takeLatest, delay } from "redux-saga/effects";
import { fetchAdminUserRequest } from "../adminUser/redux";
import { fetchModulesRequest } from "../module/redux";
import {
  fetchAllRolesPermissionsRequest,
  fetchRolesRequest,
} from "../roles/redux";
import { initAction } from "./action";
import { navigate } from "@/utils/routerService";

function* postLoginRolesInitSaga() {
  // Add a longer delay to ensure component is fully mounted and CORS context is established
  yield delay(1000);

  yield put(fetchRolesRequest());
  yield put(fetchAllRolesPermissionsRequest());
  yield put(fetchAdminUserRequest());
  yield put(fetchModulesRequest());
}

export function* initSaga(): Generator<any, void, any> {
  yield all([takeLatest(initAction.type, postLoginRolesInitSaga)]);
}
