import { all, fork } from "redux-saga/effects";
import { adminUserSaga } from "./adminUser/saga";
import { authSaga } from "./auth/saga";
import { rolesSaga } from "./roles/saga";
import membersSaga from "./members/saga";
import { moduleSaga } from "./module/saga";
import { awardsSaga } from "./awards/saga";
import { featuresSaga } from "./features/saga";
import { organizationsSaga } from "./organizations/saga";
import { logsSaga } from "./logs/saga";
import { initSaga } from "./init/saga";

const allFunction = [
  authSaga,
  rolesSaga,
  adminUserSaga,
  membersSaga,
  moduleSaga,
  awardsSaga,
  featuresSaga,
  organizationsSaga,
  logsSaga,
  initSaga,
];

const hocFn = allFunction.map((fn) => fork(fn));

export function* rootSaga() {
  yield all(hocFn);
}
