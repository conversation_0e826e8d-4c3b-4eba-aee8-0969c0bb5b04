// Development script to suppress Next.js scroll warnings
// This runs only in development mode

if (process.env.NODE_ENV === 'development') {
  // Store original console methods
  const originalConsole = {
    warn: console.warn,
    log: console.log,
    error: console.error,
    info: console.info
  };

  // Function to check if a message should be suppressed
  const shouldSuppressMessage = (message: any): boolean => {
    if (typeof message === 'string') {
      const suppressPatterns = [
        'Skipping auto-scroll behavior',
        'position: sticky',
        'position: fixed',
        'MuiBox-root',
        'MuiContainer-root', 
        'MuiPaper-root',
        'css-',
        'auto-scroll behavior due to'
      ];
      
      return suppressPatterns.some(pattern => 
        message.toLowerCase().includes(pattern.toLowerCase())
      );
    }
    return false;
  };

  // Override console methods
  console.warn = (...args: any[]) => {
    if (!shouldSuppressMessage(args[0])) {
      originalConsole.warn.apply(console, args);
    }
  };

  console.log = (...args: any[]) => {
    if (!shouldSuppressMessage(args[0])) {
      originalConsole.log.apply(console, args);
    }
  };

  console.error = (...args: any[]) => {
    if (!shouldSuppressMessage(args[0])) {
      originalConsole.error.apply(console, args);
    }
  };

  console.info = (...args: any[]) => {
    if (!shouldSuppressMessage(args[0])) {
      originalConsole.info.apply(console, args);
    }
  };

  // Add a way to restore original console methods if needed
  (window as any).__restoreConsole = () => {
    console.warn = originalConsole.warn;
    console.log = originalConsole.log;
    console.error = originalConsole.error;
    console.info = originalConsole.info;
  };
}
