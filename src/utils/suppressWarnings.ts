// Suppress Next.js scroll warnings that expose DOM structure
// This prevents the console from showing the entire DOM tree in development

if (typeof window !== 'undefined') {
  const originalWarn = console.warn;
  const originalLog = console.log;
  
  // Override console.warn to filter out scroll behavior warnings
  console.warn = (...args: any[]) => {
    // Check if this is the scroll behavior warning
    const message = args[0];
    if (
      typeof message === 'string' && 
      (
        message.includes('Skipping auto-scroll behavior') ||
        message.includes('position: sticky') ||
        message.includes('position: fixed')
      )
    ) {
      return; // Suppress this warning
    }
    
    // Allow all other warnings through
    originalWarn.apply(console, args);
  };
  
  // Also override console.log for any scroll-related logs
  console.log = (...args: any[]) => {
    const message = args[0];
    if (
      typeof message === 'string' && 
      message.includes('Skipping auto-scroll behavior')
    ) {
      return; // Suppress this log
    }
    
    // Allow all other logs through
    originalLog.apply(console, args);
  };
}

// Export empty object to make this a module
export {};
