import { Admin } from "@/types/adminUser";
import { RootState } from "@/store";

/**
 * Utility functions for admin user data management
 * Provides O(1) lookups and efficient data operations
 */

// Get admin user by UUID with O(1) lookup
export const getAdminUserById = (
  state: RootState,
  uuid: string
): Admin | null => {
  return state.adminUser.adminUsersMap[uuid] || null;
};

// Get multiple admin users by UUIDs
export const getAdminUsersByIds = (
  state: RootState,
  uuids: string[]
): Admin[] => {
  return uuids
    .map((uuid) => state.adminUser.adminUsersMap[uuid])
    .filter(Boolean);
};

// Check if admin user exists
export const adminUserExists = (state: RootState, uuid: string): boolean => {
  return uuid in state.adminUser.adminUsersMap;
};

// Get admin users by role with efficient filtering
export const getAdminUsersByRole = (
  state: RootState,
  role: string
): Admin[] => {
  return Object.values(state.adminUser.adminUsersMap).filter((user) => {
    const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
    return userRoles.includes(role as any);
  });
};

// Get active admin users
export const getActiveAdminUsers = (state: RootState): Admin[] => {
  return Object.values(state.adminUser.adminUsersMap).filter(
    (user) => user.isactive
  );
};

// Get admin users count by role
export const getAdminUsersCountByRole = (
  state: RootState
): Record<string, number> => {
  const counts: Record<string, number> = {};
  Object.values(state.adminUser.adminUsersMap).forEach((user) => {
    const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
    userRoles.forEach((role) => {
      counts[role] = (counts[role] || 0) + 1;
    });
  });
  return counts;
};

// Search admin users by multiple criteria
export const searchAdminUsers = (
  state: RootState,
  searchTerm: string,
  searchFields: (keyof Admin)[] = ["username", "email"]
): Admin[] => {
  if (!searchTerm.trim()) return state.adminUser.adminUsersList;

  const search = searchTerm.toLowerCase();
  return Object.values(state.adminUser.adminUsersMap).filter((user) => {
    return searchFields.some((field) => {
      const value = user[field];
      if (typeof value === "string") {
        return value.toLowerCase().includes(search);
      }
      if (Array.isArray(value)) {
        return value.some(
          (item: any) =>
            typeof item === "string" && item.toLowerCase().includes(search)
        );
      }
      return false;
    });
  });
};

// Update admin user in state (for optimistic updates)
export const updateAdminUserInState = (
  adminUsersMap: Record<string, Admin>,
  adminUsersList: Admin[],
  updatedUser: Admin
): { map: Record<string, Admin>; list: Admin[] } => {
  const newMap = { ...adminUsersMap, [updatedUser.uuid]: updatedUser };
  const newList = adminUsersList.map((user) =>
    user.uuid === updatedUser.uuid ? updatedUser : user
  );

  return { map: newMap, list: newList };
};

// Remove admin user from state
export const removeAdminUserFromState = (
  adminUsersMap: Record<string, Admin>,
  adminUsersList: Admin[],
  uuid: string
): { map: Record<string, Admin>; list: Admin[] } => {
  const newMap = { ...adminUsersMap };
  delete newMap[uuid];
  const newList = adminUsersList.filter((user) => user.uuid !== uuid);

  return { map: newMap, list: newList };
};

// Add admin user to state
export const addAdminUserToState = (
  adminUsersMap: Record<string, Admin>,
  adminUsersList: Admin[],
  newUser: Admin
): { map: Record<string, Admin>; list: Admin[] } => {
  const newMap = { ...adminUsersMap, [newUser.uuid]: newUser };
  const newList = [...adminUsersList, newUser];

  return { map: newMap, list: newList };
};

// Get admin user statistics
export const calculateAdminUserStats = (
  adminUsersMap: Record<string, Admin>
) => {
  const users = Object.values(adminUsersMap);
  return {
    total: users.length,
    superAdmins: users.filter((user) =>
      Array.isArray(user.roles)
        ? user.roles.includes("super_admin")
        : user.roles === "super_admin"
    ).length,
    admins: users.filter((user) =>
      Array.isArray(user.roles)
        ? user.roles.includes("admin")
        : user.roles === "admin"
    ).length,
    moderators: users.filter((user) =>
      Array.isArray(user.roles)
        ? user.roles.includes("moderator")
        : user.roles === "moderator"
    ).length,
    activeUsers: users.filter((user) => user.isactive).length,
    newThisMonth: 0, // Would need created date to calculate properly
  };
};

// Check if cache is valid
export const isCacheValid = (
  lastFetched: number | null,
  maxAgeMinutes: number = 5
): boolean => {
  if (!lastFetched) return false;
  const maxAge = maxAgeMinutes * 60 * 1000;
  return Date.now() - lastFetched < maxAge;
};

// Batch operations for multiple admin users
export const batchUpdateAdminUsers = (
  adminUsersMap: Record<string, Admin>,
  adminUsersList: Admin[],
  updates: { uuid: string; data: Partial<Admin> }[]
): { map: Record<string, Admin>; list: Admin[] } => {
  let newMap = { ...adminUsersMap };
  let newList = [...adminUsersList];

  updates.forEach(({ uuid, data }) => {
    if (newMap[uuid]) {
      const updatedUser = { ...newMap[uuid], ...data };
      newMap = { ...newMap, [uuid]: updatedUser };
      const index = newList.findIndex((user) => user.uuid === uuid);
      if (index !== -1) {
        newList = [
          ...newList.slice(0, index),
          updatedUser,
          ...newList.slice(index + 1),
        ];
      }
    }
  });

  return { map: newMap, list: newList };
};

// Sort admin users by field
export const sortAdminUsers = (
  users: Admin[],
  sortBy: keyof Admin,
  sortOrder: "asc" | "desc" = "asc"
): Admin[] => {
  return [...users].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // Handle array values (like roles)
    if (Array.isArray(aValue)) aValue = aValue.join(", ") as any;
    if (Array.isArray(bValue)) bValue = bValue.join(", ") as any;

    // Handle null/undefined values
    if (aValue == null) aValue = "" as any;
    if (bValue == null) bValue = "" as any;

    if (typeof aValue === "string" && typeof bValue === "string") {
      const comparison = aValue.localeCompare(bValue);
      return sortOrder === "asc" ? comparison : -comparison;
    }

    // Ensure values are not null before comparison
    const safeAValue = aValue ?? 0;
    const safeBValue = bValue ?? 0;

    if (safeAValue < safeBValue) return sortOrder === "asc" ? -1 : 1;
    if (safeAValue > safeBValue) return sortOrder === "asc" ? 1 : -1;
    return 0;
  });
};

// Paginate admin users
export const paginateAdminUsers = (
  users: Admin[],
  page: number = 1,
  pageSize: number = 10
): {
  users: Admin[];
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
} => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedUsers = users.slice(startIndex, endIndex);
  const totalPages = Math.ceil(users.length / pageSize);

  return {
    users: paginatedUsers,
    total: users.length,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
};

// Filter admin users by multiple criteria
export const filterAdminUsers = (
  users: Admin[],
  filters: {
    search?: string;
    role?: string;
    isActive?: boolean;
    searchFields?: (keyof Admin)[];
  }
): Admin[] => {
  let filtered = users;

  // Apply search filter
  if (filters.search?.trim()) {
    const search = filters.search.toLowerCase();
    const searchFields = filters.searchFields || ["username", "email"];
    filtered = filtered.filter((user) => {
      return searchFields.some((field) => {
        const value = user[field];
        if (typeof value === "string") {
          return value.toLowerCase().includes(search);
        }
        if (Array.isArray(value)) {
          return value.some(
            (item: any) =>
              typeof item === "string" && item.toLowerCase().includes(search)
          );
        }
        return false;
      });
    });
  }

  // Apply role filter
  if (filters.role && filters.role !== "all") {
    filtered = filtered.filter((user) => {
      const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
      return userRoles.includes(filters.role as any);
    });
  }

  // Apply active status filter
  if (filters.isActive !== undefined) {
    filtered = filtered.filter((user) => user.isactive === filters.isActive);
  }

  return filtered;
};
