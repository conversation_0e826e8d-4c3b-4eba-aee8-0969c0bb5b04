import { AxiosResponse, AxiosError } from "axios";
import {
  ApiResponse,
  ApiErrorResponse,
  PaginationParams,
  ValidationError,
  ApiError,
  HttpStatus,
} from "@/types/api";

// Response data extraction utilities
export function extractData<T>(response: AxiosResponse<ApiResponse<T>>): T {
  return response.data.data;
}

export function extractPaginatedData<T>(
  response: AxiosResponse<ApiResponse<T>>
): T {
  return response.data.data;
}

export function extractMessage(response: AxiosResponse<ApiResponse>): string {
  return response.data.message || "Operation completed successfully";
}

// Error message formatting
export function formatErrorMessage(error: AxiosError): string {
  const responseData = error.response?.data as ApiErrorResponse;

  if (responseData?.error?.message) {
    return responseData.error.message;
  }

  if (error.response?.status) {
    return getStatusMessage(error.response.status);
  }

  if (error.message) {
    return error.message;
  }

  return "An unexpected error occurred";
}

export function formatValidationErrors(error: AxiosError): ValidationError[] {
  const responseData = error.response?.data as ApiErrorResponse;

  if (
    responseData?.error?.details &&
    Array.isArray(responseData.error.details)
  ) {
    return responseData.error.details;
  }

  return [];
}

export function getStatusMessage(status: number): string {
  switch (status) {
    case HttpStatus.BAD_REQUEST:
      return "Invalid request. Please check your input.";
    case HttpStatus.UNAUTHORIZED:
      return "Authentication required. Please log in.";
    case HttpStatus.FORBIDDEN:
      return "Access denied. You do not have permission to perform this action.";
    case HttpStatus.NOT_FOUND:
      return "The requested resource was not found.";
    case HttpStatus.CONFLICT:
      return "A conflict occurred. The resource may already exist.";
    case HttpStatus.UNPROCESSABLE_ENTITY:
      return "The request could not be processed. Please check your input.";
    case HttpStatus.INTERNAL_SERVER_ERROR:
      return "An internal server error occurred. Please try again later.";
    case HttpStatus.BAD_GATEWAY:
      return "Bad gateway. Please try again later.";
    case HttpStatus.SERVICE_UNAVAILABLE:
      return "Service temporarily unavailable. Please try again later.";
    default:
      return `HTTP ${status}: An error occurred.`;
  }
}

// API endpoint builders
export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

export function buildPaginationQuery(params: PaginationParams): string {
  const queryParams: Record<string, any> = {
    page: params.page,
    page_size: params.pageSize,
  };

  if (params.sortBy) {
    queryParams.sort_by = params.sortBy;
  }

  if (params.sortOrder) {
    queryParams.sort_order = params.sortOrder;
  }

  return buildQueryString(queryParams);
}

export function buildFilterQuery(filters: Record<string, any>): string {
  const queryParams: Record<string, any> = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (
      value !== undefined &&
      value !== null &&
      value !== "" &&
      value !== "all"
    ) {
      queryParams[`filter_${key}`] = value;
    }
  });

  return buildQueryString(queryParams);
}

// URL builders
export function buildApiUrl(
  endpoint: string,
  params?: Record<string, any>
): string {
  const baseUrl =
    process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";
  const queryString = params ? buildQueryString(params) : "";
  return `${baseUrl}${endpoint}${queryString}`;
}

export function buildEndpointUrl(
  endpoint: string,
  id?: string,
  params?: Record<string, any>
): string {
  const url = id ? `${endpoint}/${id}` : endpoint;
  return buildApiUrl(url, params);
}

// Request helpers
export function createRequestConfig(config?: Record<string, any>) {
  return {
    headers: {
      "Content-Type": "application/json",
      ...config?.headers,
    },
    ...config,
  };
}

export function createFormDataRequestConfig(config?: Record<string, any>) {
  return {
    headers: {
      "Content-Type": "multipart/form-data",
      ...config?.headers,
    },
    ...config,
  };
}

// Response validation
export function isValidApiResponse(response: any): response is ApiResponse {
  return (
    response &&
    typeof response === "object" &&
    "success" in response &&
    "data" in response &&
    "timestamp" in response
  );
}

export function isApiErrorResponse(
  response: any
): response is ApiErrorResponse {
  return (
    response &&
    typeof response === "object" &&
    response.success === false &&
    "error" in response
  );
}

// Retry logic helpers
export function shouldRetry(
  error: AxiosError,
  retryCount: number,
  maxRetries: number
): boolean {
  if (retryCount >= maxRetries) return false;

  const status = error.response?.status;
  const retryableStatuses = [
    HttpStatus.INTERNAL_SERVER_ERROR,
    HttpStatus.BAD_GATEWAY,
    HttpStatus.SERVICE_UNAVAILABLE,
  ];

  return status ? retryableStatuses.includes(status) : false;
}

export function calculateRetryDelay(
  retryCount: number,
  baseDelay: number = 1000
): number {
  return baseDelay * Math.pow(2, retryCount);
}

// Cache helpers
export function generateCacheKey(
  endpoint: string,
  params?: Record<string, any>
): string {
  const paramString = params ? JSON.stringify(params) : "";
  return `${endpoint}${paramString}`;
}

export function isCacheValid(timestamp: number, ttl: number): boolean {
  return Date.now() - timestamp < ttl * 1000;
}

// Performance monitoring
export function measureApiCall<T>(
  apiCall: () => Promise<T>,
  endpoint: string
): Promise<{ data: T; duration: number }> {
  const startTime = performance.now();

  return apiCall().then((data) => {
    const duration = performance.now() - startTime;

    // Log performance in development
    if (process.env.NODE_ENV === "development") {
      console.log(
        `⏱️ API Call Performance: ${endpoint} - ${duration.toFixed(2)}ms`
      );
    }

    return { data, duration };
  });
}

// Error categorization
export function categorizeError(
  error: AxiosError
): "network" | "auth" | "validation" | "server" | "unknown" {
  if (!error.response) {
    return "network";
  }

  const status = error.response.status;

  if (status === HttpStatus.UNAUTHORIZED || status === HttpStatus.FORBIDDEN) {
    return "auth";
  }

  if (
    status === HttpStatus.BAD_REQUEST ||
    status === HttpStatus.UNPROCESSABLE_ENTITY
  ) {
    return "validation";
  }

  if (status >= 500) {
    return "server";
  }

  return "unknown";
}
