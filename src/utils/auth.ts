import jwt from "jsonwebtoken";
import { isAccessTokenExpired } from "./simpleTokenRefresh";

// Cognito token sync utilities
interface CognitoTokens {
  accessToken: string | null;
  idToken: string | null;
  refreshToken: string | null;
}

// Get Cognito configuration for localStorage key generation
const getCognitoConfig = () => {
  const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID;
  const region = process.env.NEXT_PUBLIC_AWS_REGION;
  return { userPoolClientId, region };
};

// Generate Cognito localStorage keys
const getCognitoStorageKeys = (username?: string) => {
  const { userPoolClientId } = getCognitoConfig();
  const baseKey = `CognitoIdentityServiceProvider.${userPoolClientId}`;

  if (username) {
    return {
      accessToken: `${baseKey}.${username}.accessToken`,
      idToken: `${baseKey}.${username}.idToken`,
      refreshToken: `${baseKey}.${username}.refreshToken`,
      userData: `${baseKey}.${username}.userData`,
      lastAuthUser: `${baseKey}.LastAuthUser`,
    };
  }

  return {
    lastAuthUser: `${baseKey}.LastAuthUser`,
    accessToken: undefined,
    idToken: undefined,
    refreshToken: undefined,
    userData: undefined,
  };
};

// Get current authenticated user from Cognito localStorage
export const getCognitoCurrentUser = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    const { lastAuthUser } = getCognitoStorageKeys();
    return localStorage.getItem(lastAuthUser);
  } catch (error) {
    console.error("Error getting current Cognito user:", error);
    return null;
  }
};

// Get Cognito tokens from localStorage
export const getCognitoTokens = (username?: string): CognitoTokens => {
  if (typeof window === "undefined") {
    return { accessToken: null, idToken: null, refreshToken: null };
  }

  try {
    const currentUser = username || getCognitoCurrentUser();
    if (!currentUser) {
      return { accessToken: null, idToken: null, refreshToken: null };
    }

    const keys = getCognitoStorageKeys(currentUser);

    // Check if keys exist before accessing localStorage
    if (!keys.accessToken || !keys.idToken || !keys.refreshToken) {
      return { accessToken: null, idToken: null, refreshToken: null };
    }

    return {
      accessToken: localStorage.getItem(keys.accessToken),
      idToken: localStorage.getItem(keys.idToken),
      refreshToken: localStorage.getItem(keys.refreshToken),
    };
  } catch (error) {
    console.error("Error getting Cognito tokens:", error);
    return { accessToken: null, idToken: null, refreshToken: null };
  }
};

// Sync Cognito tokens to cookies with proper expiry based on token expiration
let syncInProgress = false;

export const syncCognitoTokensToCookies = async (
  username?: string,
  days: number = 7
): Promise<boolean> => {
  // Prevent multiple simultaneous sync operations
  if (syncInProgress) {
    console.log("Token sync already in progress, skipping...");
    return false;
  }

  syncInProgress = true;

  try {
    const pollInterval = 100;
    const maxWait = 3000;
    let waited = 0;
    while (waited < maxWait) {
      const tokens = getCognitoTokens(username);
      if (tokens.accessToken && tokens.idToken) {
        // Set cookies as before
        let tokenExpiry = 60 * 60;
        try {
          const decoded = jwt.decode(tokens.accessToken, { complete: true });
          if (decoded && typeof decoded !== "string" && decoded.payload) {
            const payload = decoded.payload as any;
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresIn = payload.exp - currentTime;
            if (expiresIn > 300) {
              tokenExpiry = expiresIn;
            }
          }
        } catch (error) {
          console.warn("Could not decode token expiry, using default 1 hour");
        }
        const cookieOptions = `path=/; max-age=${tokenExpiry}; SameSite=Lax; Secure`;
        document.cookie = `cognito_access_token=${tokens.accessToken}; ${cookieOptions}`;
        document.cookie = `cognito_id_token=${tokens.idToken}; ${cookieOptions}`;
        if (tokens.refreshToken) {
          const refreshMaxAge = 60 * 60 * 24 * 30;
          const refreshCookieOptions = `path=/; max-age=${refreshMaxAge}; SameSite=Lax; Secure`;
          document.cookie = `cognito_refresh_token=${tokens.refreshToken}; ${refreshCookieOptions}`;
        }
        document.cookie = `token=${tokens.accessToken}; ${cookieOptions}`;
        return true;
      }
      await new Promise((res) => setTimeout(res, pollInterval));
      waited += pollInterval;
    }
    console.warn("⚠️ Tokens not found in localStorage after waiting");
    return false;
  } finally {
    syncInProgress = false;
  }
};

// Get tokens from cookies
export const getCognitoTokensFromCookies = (): CognitoTokens => {
  if (typeof document === "undefined") {
    return { accessToken: null, idToken: null, refreshToken: null };
  }

  const cookies = document.cookie.split(";");
  const getCookie = (name: string) => {
    const cookie = cookies.find((c) => c.trim().startsWith(`${name}=`));
    return cookie ? cookie.split("=")[1] : null;
  };

  return {
    accessToken: getCookie("cognito_access_token"),
    idToken: getCookie("cognito_id_token"),
    refreshToken: getCookie("cognito_refresh_token"),
  };
};

// Legacy functions for backward compatibility
export const setAuthToken = (token: string, days: number = 7) => {
  document.cookie = `token=${token}; path=/; max-age=${
    60 * 60 * 24 * days
  }; SameSite=Lax`;
  console.log("Token set in cookie:", token);
};

export const getAuthToken = (): string | null => {
  if (typeof document === "undefined") return null;

  const cookies = document.cookie.split(";");
  const tokenCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("token=")
  );

  if (tokenCookie) {
    const token = tokenCookie.split("=")[1];
    console.log("Token retrieved from cookie:", token);
    return token;
  }

  console.log("No token found in cookies");
  return null;
};

export const removeAuthToken = () => {
  // Remove all auth-related cookies
  const cookiesToRemove = [
    "token",
    "cognito_access_token",
    "cognito_id_token",
    "cognito_refresh_token",
  ];

  cookiesToRemove.forEach((cookieName) => {
    document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  });

  localStorage.clear();

  console.log("All auth tokens removed from cookies");
};

export const logout = () => {
  removeAuthToken();
  window.location.href = "/";
};

// Complete logout - clear all tokens and data
export const performCompleteLogout = (): void => {
  try {
    // Clear all auth-related cookies
    const cookiesToClear = [
      "token",
      "cognito_access_token",
      "cognito_id_token",
      "cognito_refresh_token",
    ];

    const clearOptions =
      "path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax";

    cookiesToClear.forEach((cookieName) => {
      document.cookie = `${cookieName}=; ${clearOptions}`;
    });

    // Clear Cognito data from localStorage
    const currentUser = getCognitoCurrentUser();
    const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID;

    if (userPoolClientId) {
      // Clear user-specific tokens
      if (currentUser) {
        const keyPrefix = `CognitoIdentityServiceProvider.${userPoolClientId}.${currentUser}`;

        const keysToRemove = [
          `${keyPrefix}.accessToken`,
          `${keyPrefix}.idToken`,
          `${keyPrefix}.refreshToken`,
          `${keyPrefix}.clockDrift`,
          `${keyPrefix}.userData`,
          `${keyPrefix}.deviceKey`,
          `${keyPrefix}.randomPasswordKey`,
          `${keyPrefix}.deviceGroupKey`,
        ];

        keysToRemove.forEach((key) => {
          localStorage.removeItem(key);
        });
      }

      // Clear general Cognito keys
      localStorage.removeItem(
        `CognitoIdentityServiceProvider.${userPoolClientId}.LastAuthUser`
      );

      // Clear any remaining Cognito keys
      Object.keys(localStorage).forEach((key) => {
        if (key.includes("CognitoIdentityServiceProvider")) {
          localStorage.removeItem(key);
        }
      });
    }

    // Clear last activity timestamp
    clearLastActivity();

    console.log("🧹 Complete logout performed - all tokens and data cleared");
  } catch (error) {
    console.error("❌ Error during complete logout:", error);
  }
};

export const isAuthenticated = (): boolean => {
  try {
    // Check both legacy token and Cognito tokens
    const legacyToken = getAuthToken();
    const cognitoTokens = getCognitoTokensFromCookies();

    const hasLegacyToken = !!(
      legacyToken &&
      legacyToken !== "undefined" &&
      legacyToken !== "null" &&
      legacyToken.trim() !== ""
    );
    const hasCognitoTokens = !!(
      cognitoTokens.accessToken && cognitoTokens.idToken
    );

    const isValid = hasLegacyToken || hasCognitoTokens;

    console.log("Client auth check:", {
      legacyToken: hasLegacyToken ? "exists" : "missing",
      cognitoTokens: hasCognitoTokens ? "exists" : "missing",
      isValid,
    });

    return isValid;
  } catch (error) {
    console.error("Error checking authentication:", error);
    return false;
  }
};

// Last activity tracking for 1-hour timeout on site revisit
const LAST_ACTIVITY_KEY = "last_activity_timestamp";

export const updateLastActivity = (): void => {
  try {
    console.log("updateLastActivity");
    const timestamp = Date.now();
    localStorage.setItem(LAST_ACTIVITY_KEY, timestamp.toString());
    console.log(
      "🔄 Last activity timestamp updated:",
      new Date(timestamp).toISOString()
    );
  } catch (error) {
    console.error("❌ Error updating last activity:", error);
  }
};

export const getLastActivity = (): number | null => {
  try {
    const timestamp = localStorage.getItem(LAST_ACTIVITY_KEY);
    console.log(timestamp, "timestamptimestamp");
    return timestamp ? parseInt(timestamp, 10) : null;
  } catch (error) {
    console.error("❌ Error getting last activity:", error);
    return null;
  }
};

export const checkLastActivityTimeout = (timeoutHours: number = 1): boolean => {
  try {
    console.log(
      "🔍 DEBUG: checkLastActivityTimeout called with timeoutHours:",
      timeoutHours
    );

    const lastActivity = getLastActivity();
    console.log("🔍 DEBUG: lastActivity from localStorage:", lastActivity);

    if (!lastActivity) {
      console.log("🔍 No last activity timestamp found, user needs to login");
      return true; // Force logout if no activity record
    }

    const now = Date.now();
    const timeSinceLastActivity = now - lastActivity;
    const timeoutMs = timeoutHours * 60 * 60 * 1000; // Convert hours to milliseconds

    const shouldLogout = timeSinceLastActivity >= timeoutMs;

    console.log("🔍 DEBUG: Detailed last activity check:", {
      lastActivity: new Date(lastActivity).toISOString(),
      now: new Date(now).toISOString(),
      timeSinceLastActivity: Math.round(timeSinceLastActivity / 1000 / 60), // minutes
      timeoutHours,
      timeoutMs: Math.round(timeoutMs / 1000 / 60), // minutes
      shouldLogout,
      timeSinceLastActivityMs: timeSinceLastActivity,
      timeoutMsValue: timeoutMs,
    });

    return shouldLogout;
  } catch (error) {
    console.error("❌ Error checking last activity timeout:", error);
    return true; // Force logout on error
  }
};

export const clearLastActivity = (): void => {
  try {
    localStorage.removeItem(LAST_ACTIVITY_KEY);
    console.log("🧹 Last activity timestamp cleared");
  } catch (error) {
    console.error("❌ Error clearing last activity:", error);
  }
};

// Auth0 environment validation
export const validateAuth0Config = () => {
  const requiredVars = {
    AUTH0_DOMAIN: process.env.AUTH0_DOMAIN,
    AUTH0_CLIENT_ID: process.env.AUTH0_CLIENT_ID,
    AUTH0_CLIENT_SECRET: process.env.AUTH0_CLIENT_SECRET,
    AUTH0_SECRET: process.env.AUTH0_SECRET,
  };

  const missingVars = Object.entries(requiredVars)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0) {
    console.error("Missing Auth0 environment variables:", missingVars);
    return {
      isValid: false,
      missing: missingVars,
    };
  }

  return {
    isValid: true,
    missing: [],
  };
};

// Get Auth0 configuration for debugging
export const getAuth0Config = () => {
  return {
    domain: process.env.AUTH0_DOMAIN,
    clientId: process.env.AUTH0_CLIENT_ID,
    hasClientSecret: !!process.env.AUTH0_CLIENT_SECRET,
    hasSecret: !!process.env.AUTH0_SECRET,
    audience: process.env.AUTH0_AUDIENCE,
    connection:
      process.env.AUTH0_CONNECTION || "Username-Password-Authentication",
    appBaseUrl: process.env.APP_BASE_URL,
    environment: process.env.NODE_ENV,
  };
};

// Network connectivity testing
export const testNetworkConnectivity = async (
  timeout: number = 5000
): Promise<boolean> => {
  try {
    // Try to fetch a small resource to test connectivity
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch("/api/health", {
      method: "HEAD",
      signal: controller.signal,
      cache: "no-cache",
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.log("🌐 Network connectivity test failed:", error);
    return false;
  }
};

// Enhanced error handling for authentication operations
export const handleAuthError = (
  error: any,
  operation: string = "authentication"
): {
  isNetworkError: boolean;
  isAuthError: boolean;
  isRetryable: boolean;
  message: string;
} => {
  const errorMessage = error?.message || error?.toString() || "Unknown error";
  const isNetwork = isNetworkError(error);
  const isAuth = isAuthError(error);
  const isRetryable = isRetryableError(error);

  console.error(`❌ ${operation} error:`, {
    message: errorMessage,
    isNetwork,
    isAuth,
    isRetryable,
    error,
  });

  return {
    isNetworkError: isNetwork,
    isAuthError: isAuth,
    isRetryable,
    message: errorMessage,
  };
};

// Check if error is network-related
const isNetworkError = (error: any): boolean => {
  const networkPatterns = [
    "network error",
    "fetch",
    "timeout",
    "connection refused",
    "connection timed out",
    "name not resolved",
    "econnreset",
    "enotfound",
    "etimedout",
    "offline",
  ];

  const errorMessage = (
    error?.message ||
    error?.toString() ||
    ""
  ).toLowerCase();
  return networkPatterns.some((pattern) => errorMessage.includes(pattern));
};

// Check if error is authentication-related
const isAuthError = (error: any): boolean => {
  const authPatterns = [
    "unauthorized",
    "not authorized",
    "invalid token",
    "token expired",
    "refresh token",
    "authentication",
    "notauthenticatedexception",
  ];

  const errorMessage = (
    error?.message ||
    error?.toString() ||
    ""
  ).toLowerCase();
  return authPatterns.some((pattern) => errorMessage.includes(pattern));
};

// Check if error is retryable
const isRetryableError = (error: any): boolean => {
  // Don't retry authentication errors
  if (isAuthError(error)) {
    return false;
  }

  // Retry network errors and temporary errors
  return (
    isNetworkError(error) ||
    error?.message?.toLowerCase().includes("temporary") ||
    error?.message?.toLowerCase().includes("retry") ||
    error?.code === "ECONNRESET" ||
    error?.code === "ETIMEDOUT"
  );
};

// Enhanced token validation with network awareness
export const validateTokensWithNetworkCheck = async (): Promise<{
  isValid: boolean;
  isNetworkError: boolean;
  needsRefresh: boolean;
  error?: any;
}> => {
  try {
    // First check network connectivity
    const isOnline = await testNetworkConnectivity();
    if (!isOnline) {
      return {
        isValid: false,
        isNetworkError: true,
        needsRefresh: false,
        error: new Error("Network connectivity issue"),
      };
    }

    const tokens = getCognitoTokensFromCookies();

    if (!tokens.accessToken) {
      return {
        isValid: false,
        isNetworkError: false,
        needsRefresh: false,
        error: new Error("No access token found"),
      };
    }

    // Check if token is expired
    const isExpired = isAccessTokenExpired(tokens.accessToken);

    if (isExpired) {
      return {
        isValid: false,
        isNetworkError: false,
        needsRefresh: true,
        error: new Error("Access token expired"),
      };
    }

    return {
      isValid: true,
      isNetworkError: false,
      needsRefresh: false,
    };
  } catch (error) {
    const isNetwork = isNetworkError(error);
    return {
      isValid: false,
      isNetworkError: isNetwork,
      needsRefresh: false,
      error,
    };
  }
};
