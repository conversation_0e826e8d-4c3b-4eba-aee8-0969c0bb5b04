import { AuthResult } from '@/hooks/useAuth';
import { AUTH_CONSTANTS } from '@/constants/auth';

export class AuthFlowManager {
  private static handlePendingTOTPSetup(username: string): boolean {
    const pendingTOTP = localStorage.getItem(AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP);
    if (!pendingTOTP) return false;

    try {
      const totpData = JSON.parse(pendingTOTP);
      if (totpData.username === username) {
        localStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP);
        return true;
      }
    } catch (e) {
      console.error('Error parsing pending TOTP data:', e);
      localStorage.removeItem(AUTH_CONSTANTS.STORAGE_KEYS.PENDING_TOTP_SETUP);
    }
    
    return false;
  }

  static shouldSetupTOTP(result: AuthResult, username: string): boolean {
    if (result.status === "SIGNED_IN") {
      return this.handlePendingTOTPSetup(username);
    }
    return false;
  }

  static getMFAStepType(result: AuthResult): string | null {
    if (result.status === "MFA_REQUIRED") {
      return result.nextStep?.signInStep || null;
    }
    return null;
  }

  static getSharedSecret(result: AuthResult): string | null {
    return result.nextStep?.totpSetupDetails?.sharedSecret || null;
  }

  static logAuthResult(result: AuthResult, context: string): void {
    console.log(`[${context}] Auth result:`, {
      status: result.status,
      nextStep: result.nextStep?.signInStep,
      hasSharedSecret: !!result.nextStep?.totpSetupDetails?.sharedSecret,
    });
  }
}

export const AUTH_STEPS = {
  CONFIRM_TOTP: "CONFIRM_SIGN_IN_WITH_TOTP_CODE",
  CONFIRM_SMS: "CONFIRM_SIGN_IN_WITH_SMS_CODE",
  SETUP_TOTP: "CONTINUE_SIGN_IN_WITH_TOTP_SETUP",
} as const;

export type AuthStep = typeof AUTH_STEPS[keyof typeof AUTH_STEPS];
