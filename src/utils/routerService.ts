// utils/routerService.js

import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

let routerInstance: AppRouterInstance | null = null;

export const setRouter = (router: AppRouterInstance) => {
  routerInstance = router;
};

export const navigate = (path: string) => {
  if (routerInstance) {
    routerInstance.push(path);
  } else {
    console.warn('Router not initialized in service.');
  }
};

export const replace = (path: string) => {
  if (routerInstance) {
    routerInstance.replace(path);
  }
};