import { fetchAuthSession } from "aws-amplify/auth";
import jwt from "jsonwebtoken";
import {
  getCognitoTokensFromCookies,
  syncCognitoTokensToCookies,
  getCognitoCurrentUser,
} from "./auth";

// Retry configuration
interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // Base delay in milliseconds
  maxDelay: number; // Maximum delay in milliseconds
  backoffMultiplier: number; // Exponential backoff multiplier
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
};

// Network error detection
const isNetworkError = (error: any): boolean => {
  const networkErrorPatterns = [
    "Network Error",
    "ERR_NETWORK",
    "ERR_INTERNET_DISCONNECTED",
    "ERR_CONNECTION_REFUSED",
    "ERR_CONNECTION_TIMED_OUT",
    "ERR_NAME_NOT_RESOLVED",
    "fetch",
    "timeout",
    "ECONNRESET",
    "ENOTFOUND",
    "ETIMEDOUT",
  ];

  const errorMessage = (
    error?.message ||
    error?.toString() ||
    ""
  ).toLowerCase();
  return networkErrorPatterns.some((pattern) =>
    errorMessage.includes(pattern.toLowerCase())
  );
};

// Exponential backoff delay calculation
const calculateBackoffDelay = (
  attempt: number,
  config: RetryConfig
): number => {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
  return Math.min(delay, config.maxDelay);
};

// Check if access token is expired
export const isAccessTokenExpired = (accessToken: string): boolean => {
  try {
    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === "string" || !decoded.payload) {
      return true;
    }

    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error("Error checking token expiry:", error);
    return true;
  }
};

// Enhanced token refresh with retry logic
export const refreshTokenIfNeeded = async (
  retryConfig: Partial<RetryConfig> = {}
): Promise<boolean> => {
  const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
  let lastError: any = null;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      // Get tokens from cookies
      const tokens = getCognitoTokensFromCookies();

      // If no access token, user needs to login
      if (!tokens.accessToken) {
        console.log("No access token found");
        return false;
      }

      // Check if access token is expired
      if (!isAccessTokenExpired(tokens.accessToken)) {
        console.log("Access token is still valid");
        return true;
      }

      console.log(
        `🔄 Access token expired, refreshing... (attempt ${attempt + 1}/${
          config.maxRetries + 1
        })`
      );

      // Use Amplify's fetchAuthSession to refresh tokens
      const session = await fetchAuthSession({ forceRefresh: true });

      if (!session.tokens || !session.tokens.accessToken) {
        throw new Error("Failed to refresh tokens - no tokens returned");
      }

      // Get current user to sync new tokens
      const currentUser = getCognitoCurrentUser();
      if (!currentUser) {
        throw new Error("No current user found");
      }

      // Sync new tokens to cookies
      const syncSuccess = await syncCognitoTokensToCookies(currentUser);

      if (syncSuccess) {
        console.log("✅ Tokens refreshed and synced successfully");
        return true;
      } else {
        throw new Error("Failed to sync refreshed tokens");
      }
    } catch (error: any) {
      lastError = error;
      console.error(`❌ Token refresh attempt ${attempt + 1} failed:`, error);

      // Check if this is a non-retryable error
      if (
        error.name === "NotAuthorizedException" ||
        error.message?.includes("refresh token") ||
        error.message?.includes("not authorized") ||
        error.message?.includes("invalid refresh token") ||
        error.message?.includes("token expired")
      ) {
        console.log(
          "🛑 Non-retryable error detected, user needs to login again"
        );
        return false;
      }

      // If this is the last attempt, don't retry
      if (attempt === config.maxRetries) {
        console.error(
          `🛑 All ${config.maxRetries + 1} token refresh attempts failed`
        );
        return false;
      }

      // Check if it's a network error
      const isNetwork = isNetworkError(error);
      if (isNetwork) {
        console.log(
          `🌐 Network error detected, will retry in ${calculateBackoffDelay(
            attempt,
            config
          )}ms`
        );
      } else {
        console.log(
          `⚠️ Non-network error, will retry in ${calculateBackoffDelay(
            attempt,
            config
          )}ms`
        );
      }

      // Wait before retrying with exponential backoff
      const delay = calculateBackoffDelay(attempt, config);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  console.error("🛑 Token refresh failed after all retry attempts:", lastError);
  return false;
};

// Enhanced token refresh with network status awareness
export const refreshTokenWithNetworkAwareness = async (
  retryConfig: Partial<RetryConfig> = {}
): Promise<{ success: boolean; isNetworkError: boolean; error?: any }> => {
  try {
    const success = await refreshTokenIfNeeded(retryConfig);
    return { success, isNetworkError: false };
  } catch (error: any) {
    const isNetwork = isNetworkError(error);
    return {
      success: false,
      isNetworkError: isNetwork,
      error,
    };
  }
};

// Check authentication and refresh if needed with retry
export const checkAuthAndRefresh = async (
  retryConfig: Partial<RetryConfig> = {}
): Promise<boolean> => {
  try {
    const tokens = getCognitoTokensFromCookies();

    // No tokens means user is not authenticated
    if (!tokens.accessToken || !tokens.idToken) {
      return false;
    }

    // If access token is valid, user is authenticated
    if (!isAccessTokenExpired(tokens.accessToken)) {
      return true;
    }

    // Try to refresh tokens with retry logic
    return await refreshTokenIfNeeded(retryConfig);
  } catch (error) {
    console.error("Auth check failed:", error);
    return false;
  }
};

// Utility function to check if we should retry based on error type
export const shouldRetryTokenRefresh = (error: any): boolean => {
  // Don't retry for authentication errors
  if (
    error.name === "NotAuthorizedException" ||
    error.message?.includes("refresh token") ||
    error.message?.includes("not authorized") ||
    error.message?.includes("invalid refresh token") ||
    error.message?.includes("token expired")
  ) {
    return false;
  }

  // Retry for network errors and other transient errors
  return (
    isNetworkError(error) ||
    error.message?.includes("timeout") ||
    error.message?.includes("temporary") ||
    error.message?.includes("retry")
  );
};

// Get token expiry information for debugging
export const getTokenExpiryInfo = (
  accessToken: string
): {
  isExpired: boolean;
  expiresAt: Date | null;
  timeUntilExpiry: number | null;
  timeUntilExpiryMinutes: number | null;
} => {
  try {
    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === "string" || !decoded.payload) {
      return {
        isExpired: true,
        expiresAt: null,
        timeUntilExpiry: null,
        timeUntilExpiryMinutes: null,
      };
    }

    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    const expiresAt = new Date(payload.exp * 1000);
    const timeUntilExpiry = payload.exp - currentTime;
    const timeUntilExpiryMinutes = Math.floor(timeUntilExpiry / 60);

    return {
      isExpired: currentTime >= payload.exp,
      expiresAt,
      timeUntilExpiry,
      timeUntilExpiryMinutes,
    };
  } catch (error) {
    console.error("Error getting token expiry info:", error);
    return {
      isExpired: true,
      expiresAt: null,
      timeUntilExpiry: null,
      timeUntilExpiryMinutes: null,
    };
  }
};
