// Organization Validation Schema using Yup
import { CreateOrganizationData } from "@/types/organization";
import * as Yup from "yup";
import { Schema } from "yup";

// Options for form fields - moved to top to avoid hoisting issues
export const industryOptions = [
  "Technology",
  "Healthcare",
  "Finance",
  "Education",
  "Manufacturing",
  "Retail",
  "Real Estate",
  "Consulting",
  "Legal",
  "Marketing",
  "Non-Profit",
  "Government",
  "Other",
];

export const companySizeOptions = [
  "1-10",
  "11-50",
  "51-100",
  "101-500",
  "500+",
];

export const stateOptions = [
  "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
  "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
  "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
  "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
  "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY"
];

// Enhanced validation schemas with better error messages and styling
export const createOrganizationValidationSchema = Yup.object({
  name: Yup.string()
    .required("Organization name is required")
    .min(2, "Organization name must be at least 2 characters")
    .max(100, "Organization name must be less than 100 characters")
    .trim(),

  email: Yup.string()
    .email("Please enter a valid email address")
    .required("email is required")
    .max(100, "email must be less than 100 characters")
    .trim(),

  phone: Yup.string()
    .required("Phone number is required")
    .matches(
      /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/,
      "Please enter a valid phone number"
    )
    .max(20, "Phone number must be less than 20 characters"),

  address1: Yup.string()
    .required("Address is required")
    .min(5, "Address must be at least 5 characters")
    .max(200, "Address must be less than 200 characters")
    .trim(),

  address2: Yup.string()
    .optional()
    .max(200, "Address line 2 must be less than 200 characters")
    .trim(),

  city: Yup.string()
    .required("City is required")
    .min(2, "City must be at least 2 characters")
    .max(100, "City must be less than 100 characters")
    .trim(),

  state: Yup.string()
    .required("State is required")
    .oneOf(stateOptions, "Please select a valid state"),

  zip: Yup.string().required("ZIP code is required"),

  industry: Yup.string()
    .required("Industry is required")
    .oneOf(industryOptions, "Please select a valid industry"),

  annualRevenue: Yup.string()
    .optional()
    .max(50, "Annual revenue must be less than 50 characters")
    .matches(
      /^[\d,]+$/,
      "Please enter a valid number (e.g., 1000000 or 1,000,000)"
    ),

  companySize: Yup.string()
    .optional()
    .oneOf(companySizeOptions, "Please select a valid company size"),

  yearFounded: Yup.string()
    .optional()
    .matches(/^\d{4}$/, "Please enter a valid 4-digit year (e.g., 2020)")
    .test(
      "year-range",
      "Year must be between 1800 and current year",
      (value) => {
        if (!value) return true;
        const year = parseInt(value);
        const currentYear = new Date().getFullYear();
        return year >= 1800 && year <= currentYear;
      }
    ),

  businessProfileElementId: Yup.number()
    .optional()
    .positive("Business profile element ID must be a positive number")
    .integer("Business profile element ID must be an integer"),
});

export const updateOrganizationValidationSchema = Yup.object({
  name: Yup.string()
    .required("Organization name is required")
    .min(2, "Organization name must be at least 2 characters")
    .max(100, "Organization name must be less than 100 characters")
    .trim(),

  email: Yup.string()
    .email("Please enter a valid email address")
    .required("email is required")
    .max(100, "email must be less than 100 characters")
    .trim(),

  phone: Yup.string()
    .required("Phone number is required")
    .matches(
      /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/,
      "Please enter a valid phone number"
    )
    .max(20, "Phone number must be less than 20 characters"),

  address1: Yup.string()
    .required("Address is required")
    .min(5, "Address must be at least 5 characters")
    .max(200, "Address must be less than 200 characters")
    .trim(),

  address2: Yup.string()
    .optional()
    .max(200, "Address line 2 must be less than 200 characters")
    .trim(),

  city: Yup.string()
    .required("City is required")
    .min(2, "City must be at least 2 characters")
    .max(100, "City must be less than 100 characters")
    .trim(),

  state: Yup.string()
    .required("State is required")
    .oneOf(stateOptions, "Please select a valid state"),

  zip: Yup.string().required("ZIP code is required"),

  industry: Yup.string()
    .required("Industry is required")
    .oneOf(industryOptions, "Please select a valid industry"),

  annualRevenue: Yup.string()
    .optional()
    .max(50, "Annual revenue must be less than 50 characters")
    .matches(
      /^[\d,]+$/,
      "Please enter a valid number (e.g., 1000000 or 1,000,000)"
    ),

  companySize: Yup.string()
    .optional()
    .oneOf(companySizeOptions, "Please select a valid company size"),

  yearFounded: Yup.string()
    .optional()
    .matches(/^\d{4}$/, "Please enter a valid 4-digit year (e.g., 2020)")
    .test(
      "year-range",
      "Year must be between 1800 and current year",
      (value) => {
        if (!value) return true;
        const year = parseInt(value);
        const currentYear = new Date().getFullYear();
        return year >= 1800 && year <= currentYear;
      }
    ),

  businessProfileElementId: Yup.number()
    .optional()
    .positive("Business profile element ID must be a positive number")
    .integer("Business profile element ID must be an integer"),
});

// Enhanced validation for API data errors - more lenient for valid data
export const validateApiData = (data: any) => {
  const errors: Record<string, string> = {};

  // Only validate if data exists and has content
  if (!data) {
    return { isValid: false, errors: { general: "No data provided" } };
  }

  // Check for required fields - only if they're truly missing or empty
  if (!data.name || data.name.trim() === "") {
    errors.name = "Organization name is required";
  }

  if (!data.email || data.email.trim() === "") {
    errors.email = "email is required";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (!data.phone || data.phone.trim() === "") {
    errors.phone = "Phone number is required";
  } else if (
    !/^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/.test(
      data.phone
    )
  ) {
    errors.phone = "Please enter a valid phone number";
  }

  if (!data.address1 || data.address1.trim() === "") {
    errors.address1 = "Address is required";
  }

  if (!data.city || data.city.trim() === "") {
    errors.city = "City is required";
  }

  if (!data.state || data.state.trim() === "") {
    errors.state = "State is required";
  }

  if (!data.zip || data.zip.trim() === "") {
    errors.zip = "ZIP code is required";
  } else if (!/^\d{5}(-\d{4})?$/.test(data.zip)) {
    errors.zip = "Please enter a valid ZIP code";
  }

  if (!data.industry || data.industry.trim() === "") {
    errors.industry = "Industry is required";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Enhanced field validation with API error handling
export const getFieldError = (
  fieldName: string,
  formErrors: any,
  apiErrors: Record<string, string> = {}
) => {
  // Priority: API errors > form validation errors
  if (apiErrors[fieldName]) {
    return apiErrors[fieldName];
  }

  if (formErrors[fieldName]) {
    return formErrors[fieldName].message;
  }

  return "";
};

// Enhanced field styling based on errors
export const getFieldStyling = (
  fieldName: string,
  formErrors: any,
  apiErrors: Record<string, string> = {}
) => {
  const hasError = !!(formErrors[fieldName] || apiErrors[fieldName]);

  return {
    error: hasError,
    helperText: getFieldError(fieldName, formErrors, apiErrors),
    sx: hasError
      ? {
          "& .MuiOutlinedInput-root": {
            "& fieldset": {
              borderColor: "error.main",
              borderWidth: "2px",
            },
            "&:hover fieldset": {
              borderColor: "error.main",
            },
            "&.Mui-focused fieldset": {
              borderColor: "error.main",
            },
          },
        }
      : undefined,
  };
};
