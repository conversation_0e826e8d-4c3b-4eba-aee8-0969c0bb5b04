import { Role } from "@/types/role";
import { RootState } from "@/store";

/**
 * Utility functions for roles data management
 * Provides O(1) lookups and efficient data operations
 */

// Get role by slug with O(1) lookup
export const getRoleBySlug = (state: RootState, slug: string): Role | null => {
  return state.roles.rolesMap[slug] || null;
};

// Get multiple roles by slugs
export const getRolesBySlugs = (state: RootState, slugs: string[]): Role[] => {
  return slugs
    .map(slug => state.roles.rolesMap[slug])
    .filter(Boolean);
};

// Check if role exists
export const roleExists = (state: RootState, slug: string): boolean => {
  return slug in state.roles.rolesMap;
};

// Search roles by multiple criteria
export const searchRoles = (
  state: RootState,
  searchTerm: string,
  searchFields: (keyof Role)[] = ['name', 'description']
): Role[] => {
  if (!searchTerm.trim()) return state.roles.roles;
  
  const search = searchTerm.toLowerCase();
  return Object.values(state.roles.rolesMap).filter(role => {
    return searchFields.some(field => {
      const value = role[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(search);
      }
      return false;
    });
  });
};

// Filter roles by multiple criteria
export const filterRoles = (
  roles: Role[],
  filters: {
    search?: string;
    searchFields?: (keyof Role)[];
  }
): Role[] => {
  let filtered = roles.filter(role => role?.slug);
  
  // Apply search filter
  if (filters.search?.trim()) {
    const search = filters.search.toLowerCase();
    const searchFields = filters.searchFields || ['name', 'description'];
    filtered = filtered.filter(role => {
      return searchFields.some(field => {
        const value = role[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(search);
        }
        return false;
      });
    });
  }
  
  return filtered;
};

// Sort roles by field
export const sortRoles = (
  roles: Role[],
  sortBy: keyof Role = 'name',
  sortOrder: 'asc' | 'desc' = 'asc'
): Role[] => {
  return [...roles].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    // Handle null/undefined values
    if (aValue == null) aValue = '' as any;
    if (bValue == null) bValue = '' as any;

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortOrder === 'asc' ? comparison : -comparison;
    }

    // Ensure values are not null before comparison
    const safeAValue = aValue ?? 0;
    const safeBValue = bValue ?? 0;

    if (safeAValue < safeBValue) return sortOrder === 'asc' ? -1 : 1;
    if (safeAValue > safeBValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

// Paginate roles
export const paginateRoles = (
  roles: Role[],
  page: number = 1,
  pageSize: number = 10
): { roles: Role[]; total: number; totalPages: number; hasNext: boolean; hasPrev: boolean } => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedRoles = roles.slice(startIndex, endIndex);
  const totalPages = Math.ceil(roles.length / pageSize);
  
  return {
    roles: paginatedRoles,
    total: roles.length,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
};

// Update role in state (for optimistic updates)
export const updateRoleInState = (
  rolesMap: Record<string, Role>,
  rolesList: Role[],
  updatedRole: Role
): { map: Record<string, Role>; list: Role[] } => {
  const newMap = { ...rolesMap, [updatedRole.slug]: updatedRole };
  const newList = rolesList.map(role => 
    role.slug === updatedRole.slug ? updatedRole : role
  );
  
  return { map: newMap, list: newList };
};

// Remove role from state
export const removeRoleFromState = (
  rolesMap: Record<string, Role>,
  rolesList: Role[],
  slug: string
): { map: Record<string, Role>; list: Role[] } => {
  const newMap = { ...rolesMap };
  delete newMap[slug];
  const newList = rolesList.filter(role => role.slug !== slug);
  
  return { map: newMap, list: newList };
};

// Add role to state
export const addRoleToState = (
  rolesMap: Record<string, Role>,
  rolesList: Role[],
  newRole: Role
): { map: Record<string, Role>; list: Role[] } => {
  const newMap = { ...rolesMap, [newRole.slug]: newRole };
  const newList = [...rolesList, newRole];
  
  return { map: newMap, list: newList };
};

// Get roles statistics
export const calculateRolesStats = (rolesMap: Record<string, Role>) => {
  const roles = Object.values(rolesMap);
  return {
    total: roles.length,
    systemRoles: roles.filter(role => role.slug.startsWith('system_')).length,
    customRoles: roles.filter(role => !role.slug.startsWith('system_')).length,
    adminRoles: roles.filter(role => role.slug.includes('admin')).length,
  };
};

// Check if cache is valid
export const isRolesCacheValid = (lastFetched: number | null, maxAgeMinutes: number = 5): boolean => {
  if (!lastFetched) return false;
  const maxAge = maxAgeMinutes * 60 * 1000;
  return Date.now() - lastFetched < maxAge;
};

// Batch operations for multiple roles
export const batchUpdateRoles = (
  rolesMap: Record<string, Role>,
  rolesList: Role[],
  updates: { slug: string; data: Partial<Role> }[]
): { map: Record<string, Role>; list: Role[] } => {
  let newMap = { ...rolesMap };
  let newList = [...rolesList];

  updates.forEach(({ slug, data }) => {
    if (newMap[slug]) {
      const updatedRole = { ...newMap[slug], ...data };
      newMap = { ...newMap, [slug]: updatedRole };
      const index = newList.findIndex(role => role.slug === slug);
      if (index !== -1) {
        newList = [...newList.slice(0, index), updatedRole, ...newList.slice(index + 1)];
      }
    }
  });
  
  return { map: newMap, list: newList };
};

// Get roles by permission
export const getRolesByPermission = (state: RootState, permission: string): Role[] => {
  return Object.values(state.roles.rolesMap).filter(role => {
    // This would need to be implemented based on your permission structure
    // For now, returning all roles as placeholder
    return true;
  });
};

// Check if user has permission through roles
export const hasPermissionThroughRoles = (
  userRoles: string[],
  requiredPermission: string,
  rolesMap: Record<string, Role>
): boolean => {
  return userRoles.some(roleSlug => {
    const role = rolesMap[roleSlug];
    if (!role) return false;
    // This would need to be implemented based on your permission structure
    // For now, returning true as placeholder
    return true;
  });
};

// Get role hierarchy (if roles have parent-child relationships)
export const getRoleHierarchy = (state: RootState): Role[] => {
  // This would need to be implemented based on your role hierarchy structure
  // For now, returning all roles sorted by name
  return Object.values(state.roles.rolesMap).sort((a, b) => a.name.localeCompare(b.name));
};
