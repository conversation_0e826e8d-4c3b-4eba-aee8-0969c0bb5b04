import toast from 'react-hot-toast';

// Custom toast functions with predefined styles
export const showToast = {
  success: (message: string, options?: any) => {
    return toast.success(message, {
      duration: 3000,
      ...options,
    });
  },

  error: (message: string, options?: any) => {
    return toast.error(message, {
      duration: 5000,
      ...options,
    });
  },

  loading: (message: string, options?: any) => {
    return toast.loading(message, {
      ...options,
    });
  },

  info: (message: string, options?: any) => {
    return toast(message, {
      icon: 'ℹ️',
      duration: 4000,
      style: {
        background: '#3B82F6',
        color: '#fff',
      },
      ...options,
    });
  },

  warning: (message: string, options?: any) => {
    return toast(message, {
      icon: '⚠️',
      duration: 4000,
      style: {
        background: '#F59E0B',
        color: '#fff',
      },
      ...options,
    });
  },

  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: any
  ) => {
    return toast.promise(promise, messages, {
      success: {
        duration: 3000,
      },
      error: {
        duration: 5000,
      },
      ...options,
    });
  },

  dismiss: (toastId?: string) => {
    return toast.dismiss(toastId);
  },

  remove: (toastId?: string) => {
    return toast.remove(toastId);
  },
};

// Export the default toast for custom usage
export default toast;
