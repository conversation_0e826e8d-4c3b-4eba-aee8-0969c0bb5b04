load_module modules/ngx_http_perl_module.so;

user  nginx;
worker_processes  2;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  2048;
}


http {
    real_ip_header X-Forwarded-For;
    set_real_ip_from 0.0.0.0/0;
    server_names_hash_bucket_size 128;
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';


log_format  timed_combined  '{'
                    '"time": "$time_iso8601",'
                    '"remote_addr": "$remote_addr",'
                    '"x-forward-for": "$proxy_add_x_forwarded_for",'
                    '"remote_user": "$remote_user",'
                    '"bytes_sent": "$bytes_sent",'
                    '"request_time": $request_time,'
                    '"status": $status,'
                    '"vhost": "$host",'
                    '"request_proto": "$server_protocol",'
                    '"request": "$request",'
                    '"path": "$uri",'
                    '"request_query": "$args",'
                    '"request_length": "$request_length",'
                    '"duration": "$request_time",'
                    '"method": "$request_method",'
                    '"http_referrer": "$http_referrer",'
                    '"http_user_agent": "$http_user_agent"'
                  '}';


    access_log  /var/log/nginx/access.log  timed_combined;

    sendfile        on;

    client_body_timeout   300;
    client_header_timeout 300;
    client_max_body_size 10M;
    keepalive_timeout     300;
    send_timeout          300;
    server_tokens         off;

    map $http_accept $webp_suffix {
      default "";
      "~*webp" ".webp";
    }

    include /etc/nginx/conf.d/*.conf;

}
