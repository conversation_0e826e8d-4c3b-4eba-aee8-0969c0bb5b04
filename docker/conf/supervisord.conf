[supervisord]
nodaemon = true
logfile = /dev/null
logfile_maxbytes = 0

[program:nextjs]
directory=/app
command=node_modules/.bin/next start
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes = 0

[program:nginx]
command = /usr/sbin/nginx -g "daemon off;"
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes = 0
