# Automatic Inactivity Logout

This document describes the automatic inactivity logout functionality that has been implemented to enhance security by automatically logging out users who are inactive for more than 30 minutes.

## Overview

The inactivity logout feature automatically tracks user activity and logs out users when they have been inactive for a specified period (default: 30 minutes). This helps protect sensitive data when users step away from their computers without manually logging out.

## Features

- **Automatic Activity Tracking**: Monitors mouse movements, clicks, keyboard input, scrolling, and touch events
- **Configurable Timeout**: Default 30-minute timeout with customizable settings
- **Warning Dialog**: Shows a warning 5 minutes before automatic logout
- **Silent Operation**: Runs in the background without affecting user experience
- **Cross-tab Synchronization**: Works across multiple browser tabs
- **Integration with Token Refresh**: Coordinates with existing authentication system

## Implementation

### Core Components

1. **`useUserInactivityLogout` Hook** (`src/hooks/useUserInactivityLogout.ts`)
   - Tracks user activity events
   - Manages inactivity timers
   - Triggers logout when timeout is reached
   - Provides warning callbacks

2. **`InactivityWarningDialog` Component** (`src/components/auth/InactivityWarningDialog.tsx`)
   - Shows warning dialog before automatic logout
   - Provides countdown timer
   - Allows users to extend their session

3. **`SessionManager` Component** (`src/components/auth/SessionManager.tsx`)
   - Combines token refresh and inactivity logout
   - Manages warning dialog state
   - Coordinates all session-related functionality

### Integration

The functionality is automatically enabled application-wide through the `SessionManager` component in the root layout (`src/app/layout.tsx`):

```tsx
<SessionManager
  checkInterval={30000}           // Token check every 30 seconds
  refreshBuffer={5}               // Refresh tokens 5 minutes before expiry
  inactivityTimeout={30}          // Logout after 30 minutes of inactivity
  enableInactivityLogout={true}   // Enable inactivity tracking
  warningTime={5}                 // Show warning 5 minutes before logout
  showWarningDialog={true}        // Show warning dialog
  warningAutoCloseTime={60}       // Auto-close warning after 60 seconds
/>
```

## Configuration Options

### SessionManager Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `checkInterval` | number | 30000 | Token refresh check interval (ms) |
| `refreshBuffer` | number | 5 | Refresh tokens X minutes before expiry |
| `inactivityTimeout` | number | 30 | Auto logout after X minutes of inactivity |
| `enableInactivityLogout` | boolean | true | Enable/disable inactivity logout |
| `warningTime` | number | 5 | Show warning X minutes before logout |
| `showWarningDialog` | boolean | true | Show warning dialog |
| `warningAutoCloseTime` | number | 60 | Auto-close warning after X seconds |

### useUserInactivityLogout Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `timeout` | number | 30 | Timeout in minutes |
| `enabled` | boolean | true | Enable/disable tracking |
| `checkInterval` | number | 60000 | Check interval in milliseconds |
| `events` | string[] | ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'] | Events to track |
| `onWarning` | function | undefined | Callback when warning should be shown |
| `warningTime` | number | 5 | Show warning X minutes before logout |

## User Experience

### Normal Operation
- Users interact with the application normally
- Activity is tracked silently in the background
- No impact on performance or user experience

### Warning Phase
- Warning dialog appears 5 minutes before logout
- Shows countdown timer with remaining time
- User can click "Stay Logged In" to continue session
- Dialog auto-closes after 60 seconds if no action taken

### Automatic Logout
- Occurs after 30 minutes of complete inactivity
- Clears all authentication tokens and data
- Redirects to login page with reason parameter
- Shows appropriate message about session timeout

## Security Benefits

1. **Data Protection**: Prevents unauthorized access when users leave their workstation
2. **Compliance**: Helps meet security compliance requirements
3. **Token Management**: Coordinates with token expiration for comprehensive security
4. **Multi-tab Protection**: Works across all browser tabs and windows

## Testing

A test page is available at `/test-inactivity` that demonstrates the functionality with a shorter 2-minute timeout for testing purposes. This page shows:

- Real-time activity tracking
- Countdown timers
- Manual controls for testing
- Implementation details

## Technical Details

### Activity Detection
The system tracks these user events:
- `mousedown` - Mouse button presses
- `mousemove` - Mouse movements
- `keypress` - Keyboard input
- `scroll` - Page scrolling
- `touchstart` - Touch screen interactions
- `click` - Mouse clicks

### Timer Management
- Uses `setInterval` for periodic inactivity checks
- Resets timer on any tracked user activity
- Coordinates with existing token refresh system
- Handles browser focus/visibility changes

### Logout Process
1. Clears all Cognito tokens from localStorage
2. Removes authentication cookies
3. Calls `performCompleteLogout()` utility
4. Redirects to login page with reason parameter

## Customization

### Disabling Inactivity Logout
```tsx
<SessionManager
  enableInactivityLogout={false}
  // ... other props
/>
```

### Custom Timeout
```tsx
<SessionManager
  inactivityTimeout={45} // 45 minutes
  warningTime={10}       // 10 minute warning
  // ... other props
/>
```

### Custom Warning Handling
```tsx
const customWarningHandler = (timeLeft: number) => {
  // Custom warning logic
  console.log(`User will be logged out in ${timeLeft} minutes`);
};

// Use with useUserInactivityLogout hook directly
useUserInactivityLogout({
  timeout: 30,
  onWarning: customWarningHandler
});
```

## Browser Compatibility

The inactivity logout functionality is compatible with all modern browsers that support:
- `addEventListener` for event handling
- `setInterval`/`clearInterval` for timers
- `localStorage` for token storage
- `document.hidden` and `visibilitychange` events

## Performance Considerations

- Minimal performance impact due to efficient event handling
- Uses passive event listeners where possible
- Debounced activity updates to prevent excessive processing
- Automatic cleanup on component unmount

## Troubleshooting

### Common Issues

1. **Logout not triggering**: Check if `enableInactivityLogout` is set to `true`
2. **Warning not showing**: Verify `showWarningDialog` is `true` and `onWarning` callback is provided
3. **Timer not resetting**: Ensure tracked events are being fired (check browser console)

### Debug Mode

Enable debug logging by checking the browser console for messages prefixed with:
- `🔄` - Activity reset messages
- `🔍` - Inactivity check messages
- `⚠️` - Warning trigger messages
- `⏰` - Logout trigger messages

## Future Enhancements

Potential improvements for future versions:
- Server-side session validation
- Configurable activity events
- Grace period for network reconnection
- Integration with idle detection APIs
- Custom warning UI themes
- Analytics for session patterns
