#stage 1
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat git bash openssh
# Create a Virtual directory inside the docker image
WORKDIR /app
COPY package.json package-lock.json ./

# Copy files to virtual directory
# Run command in Virtual directory
RUN npm cache clean --force

#RUN npm ci --omit=dev
RUN npm ci

#stage 2
FROM node:20-alpine AS builder
ARG DEPLOY_ENVIRONMENT
ARG NEXT_PUBLIC_AWS_USER_POOL_ID
ARG NEXT_PUBLIC_AWS_REGION
ARG NEXT_PUBLIC_AWS_CLIENT_ID
ARG NEXT_PUBLIC_BACKEND_API_URL
ARG APP_BASE_URL
ARG AUTH0_DOMAIN
ARG AUTH0_CLIENT_ID
ARG AUTH0_CLIENT_SECRET
ARG AUTH0_SECRET
ARG AUTH0_AUDIENCE
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV DEPLOY_ENVIRONMENT=$DEPLOY_ENVIRONMENT
ENV NEXT_PUBLIC_AWS_USER_POOL_ID=$NEXT_PUBLIC_AWS_USER_POOL_ID
ENV NEXT_PUBLIC_AWS_REGION=$NEXT_PUBLIC_AWS_REGION
ENV NEXT_PUBLIC_AWS_CLIENT_ID=$NEXT_PUBLIC_AWS_CLIENT_ID
ENV NEXT_PUBLIC_BACKEND_API_URL=$NEXT_PUBLIC_BACKEND_API_URL
ENV APP_BASE_URL=$APP_BASE_URL
ENV AUTH0_DOMAIN=$AUTH0_DOMAIN
ENV AUTH0_CLIENT_ID=$AUTH0_CLIENT_ID
ENV AUTH0_CLIENT_SECRET=$AUTH0_CLIENT_SECRET
ENV AUTH0_SECRET=$AUTH0_SECRET
ENV AUTH0_AUDIENCE=$AUTH0_AUDIENCE

RUN npm run build

#stage 3
FROM nginx:1.24.0-alpine-perl
ARG DEPLOY_ENVIRONMENT
ARG NEXT_PUBLIC_AWS_USER_POOL_ID
ARG NEXT_PUBLIC_AWS_REGION
ARG NEXT_PUBLIC_AWS_CLIENT_ID
ARG NEXT_PUBLIC_BACKEND_API_URL
ARG APP_BASE_URL
ARG AUTH0_DOMAIN
ARG AUTH0_CLIENT_ID
ARG AUTH0_CLIENT_SECRET
ARG AUTH0_SECRET
ARG AUTH0_AUDIENCE
WORKDIR /app
LABEL name="md-fe-nextjs"
LABEL maintainer="Dustin Mason"
LABEL Version="0.0.1"

ENV DEPLOY_ENVIRONMENT=$DEPLOY_ENVIRONMENT
ENV NEXT_PUBLIC_AWS_USER_POOL_ID=$NEXT_PUBLIC_AWS_USER_POOL_ID
ENV NEXT_PUBLIC_AWS_REGION=$NEXT_PUBLIC_AWS_REGION
ENV NEXT_PUBLIC_AWS_CLIENT_ID=$NEXT_PUBLIC_AWS_CLIENT_ID
ENV NEXT_PUBLIC_BACKEND_API_URL=$NEXT_PUBLIC_BACKEND_API_URL
ENV APP_BASE_URL=$APP_BASE_URL
ENV AUTH0_DOMAIN=$AUTH0_DOMAIN
ENV AUTH0_CLIENT_ID=$AUTH0_CLIENT_ID
ENV AUTH0_CLIENT_SECRET=$AUTH0_CLIENT_SECRET
ENV AUTH0_SECRET=$AUTH0_SECRET
ENV AUTH0_AUDIENCE=$AUTH0_AUDIENCE

COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next-env.d.ts ./
COPY --from=builder /app/src ./app
COPY --from=builder /usr/lib /usr/lib
COPY --from=builder /usr/local/lib /usr/local/lib
COPY --from=builder /usr/local/include /usr/local/include
COPY --from=builder /usr/local/bin /usr/local/bin

#Copy public folder
COPY public ./public
COPY next.config.ts ./
#COPY tailwind.config.ts ./
COPY tsconfig.json ./
COPY eslint.config.mjs ./
EXPOSE 3000

RUN apk add supervisor bash

#Copy the configuration files
COPY docker/conf/nginx.conf /etc/nginx/nginx.conf
COPY docker/conf/md-fe-nextjs.conf /etc/nginx/conf.d/md-fe-nextjs.conf

#remove the default nginx conf
RUN mv /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.disabled

COPY docker/conf/supervisord.conf /etc/supervisord.conf
RUN mkdir -p /var/www/html

RUN chown -R nginx:nginx /var/www/html
EXPOSE 80

#make supervisor log directory for nextjs
#RUN mkdir -p /var/log/supervisor
#let's have nginx log to STDOUT and STDERR
RUN ln -sf /dev/stdout /var/log/nginx/access.log
RUN ln -sf /dev/stderr /var/log/nginx/error.log

CMD ["/usr/bin/supervisord"]
